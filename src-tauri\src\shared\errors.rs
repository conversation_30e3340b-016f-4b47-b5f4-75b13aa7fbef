// 统一错误处理系统
// 定义应用中所有可能的错误类型，提供统一的错误处理机制

use serde::{Deserialize, Serialize};
use std::fmt;

/// 应用错误类型
#[derive(Debug, thiserror::Error, Serialize, Deserialize)]
#[serde(tag = "type", content = "message")]
pub enum AppError {
    #[error("数据库错误: {message}")]
    Database { message: String },

    #[error("文件系统错误: {message}")]
    FileSystem { message: String },

    #[error("验证错误: {message}")]
    Validation { message: String },

    #[error("业务逻辑错误: {message}")]
    BusinessLogic { message: String },

    #[error("资源未找到: {resource}")]
    NotFound { resource: String },

    #[error("权限不足: {action}")]
    Unauthorized { action: String },

    #[error("配置错误: {message}")]
    Configuration { message: String },

    #[error("外部服务错误: {service} - {message}")]
    ExternalService { service: String, message: String },

    #[error("序列化错误: {message}")]
    Serialization { message: String },

    #[error("网络错误: {message}")]
    Network { message: String },

    #[error("内部错误: {message}")]
    Internal { message: String },
}

/// 应用结果类型别名
pub type AppResult<T> = Result<T, AppError>;

impl AppError {
    /// 创建数据库错误
    pub fn database<S: Into<String>>(message: S) -> Self {
        Self::Database {
            message: message.into(),
        }
    }

    /// 创建验证错误
    pub fn validation<S: Into<String>>(message: S) -> Self {
        Self::Validation {
            message: message.into(),
        }
    }

    /// 创建业务逻辑错误
    pub fn business_logic<S: Into<String>>(message: S) -> Self {
        Self::BusinessLogic {
            message: message.into(),
        }
    }

    /// 创建资源未找到错误
    pub fn not_found<S: Into<String>>(resource: S) -> Self {
        Self::NotFound {
            resource: resource.into(),
        }
    }

    /// 创建权限不足错误
    pub fn unauthorized<S: Into<String>>(action: S) -> Self {
        Self::Unauthorized {
            action: action.into(),
        }
    }

    /// 获取错误代码
    pub fn error_code(&self) -> &'static str {
        match self {
            AppError::Database { .. } => "DATABASE_ERROR",
            AppError::FileSystem { .. } => "FILE_SYSTEM_ERROR",
            AppError::Validation { .. } => "VALIDATION_ERROR",
            AppError::BusinessLogic { .. } => "BUSINESS_LOGIC_ERROR",
            AppError::NotFound { .. } => "NOT_FOUND",
            AppError::Unauthorized { .. } => "UNAUTHORIZED",
            AppError::Configuration { .. } => "CONFIGURATION_ERROR",
            AppError::ExternalService { .. } => "EXTERNAL_SERVICE_ERROR",
            AppError::Serialization { .. } => "SERIALIZATION_ERROR",
            AppError::Network { .. } => "NETWORK_ERROR",
            AppError::Internal { .. } => "INTERNAL_ERROR",
        }
    }

    /// 判断是否为用户错误（用户可以修复的错误）
    pub fn is_user_error(&self) -> bool {
        matches!(
            self,
            AppError::Validation { .. }
                | AppError::BusinessLogic { .. }
                | AppError::NotFound { .. }
                | AppError::Unauthorized { .. }
        )
    }

    /// 判断是否为系统错误（需要开发者修复的错误）
    pub fn is_system_error(&self) -> bool {
        !self.is_user_error()
    }
}

// 实现从常见错误类型的转换
impl From<std::io::Error> for AppError {
    fn from(err: std::io::Error) -> Self {
        AppError::FileSystem {
            message: err.to_string(),
        }
    }
}

impl From<serde_json::Error> for AppError {
    fn from(err: serde_json::Error) -> Self {
        AppError::Serialization {
            message: err.to_string(),
        }
    }
}

// 为Tauri命令提供错误转换
impl From<AppError> for tauri::Error {
    fn from(err: AppError) -> Self {
        tauri::Error::Anyhow(anyhow::anyhow!(err))
    }
}

/// 错误上下文扩展trait
pub trait ErrorContext<T> {
    fn with_context<F>(self, f: F) -> AppResult<T>
    where
        F: FnOnce() -> String;

    fn with_business_context<F>(self, f: F) -> AppResult<T>
    where
        F: FnOnce() -> String;
}

impl<T, E> ErrorContext<T> for Result<T, E>
where
    E: Into<AppError>,
{
    fn with_context<F>(self, f: F) -> AppResult<T>
    where
        F: FnOnce() -> String,
    {
        self.map_err(|e| {
            let original_error = e.into();
            match original_error {
                AppError::Database { message } => AppError::Database {
                    message: format!("{}: {}", f(), message),
                },
                AppError::FileSystem { message } => AppError::FileSystem {
                    message: format!("{}: {}", f(), message),
                },
                other => other,
            }
        })
    }

    fn with_business_context<F>(self, f: F) -> AppResult<T>
    where
        F: FnOnce() -> String,
    {
        self.map_err(|_| AppError::business_logic(f()))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let err = AppError::validation("测试验证错误");
        assert_eq!(err.error_code(), "VALIDATION_ERROR");
        assert!(err.is_user_error());
        assert!(!err.is_system_error());
    }

    #[test]
    fn test_error_context() {
        let result: Result<(), std::io::Error> = Err(std::io::Error::new(
            std::io::ErrorKind::NotFound,
            "文件未找到",
        ));

        let app_result = result
            .map_err(AppError::from)
            .with_context(|| "读取配置文件时".to_string());

        assert!(app_result.is_err());
        if let Err(AppError::FileSystem { message }) = app_result {
            assert!(message.contains("读取配置文件时"));
        } else {
            panic!("期望FileSystem错误");
        }
    }
}
