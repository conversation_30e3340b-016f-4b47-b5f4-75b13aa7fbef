// 输入组件
// 提供统一的输入框样式和行为

import { Component, JSX, splitProps, createSignal, Show } from 'solid-js';
import { Dynamic } from 'solid-js/web';

export interface InputProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  helperText?: string;
  leftIcon?: Component;
  rightIcon?: Component;
  variant?: 'default' | 'filled' | 'outline';
  size?: 'sm' | 'md' | 'lg';
}

export const Input: Component<InputProps> = (props) => {
  const [local, others] = splitProps(props, [
    'label',
    'error',
    'helperText',
    'leftIcon',
    'rightIcon',
    'variant',
    'size',
    'class',
    'id'
  ]);

  const variant = () => local.variant || 'default';
  const size = () => local.size || 'md';
  const inputId = () => local.id || `input-${Math.random().toString(36).substr(2, 9)}`;

  const baseClasses = () => [
    'block w-full rounded-lg border transition-colors',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    // Size classes
    size() === 'sm' && 'px-3 py-1.5 text-sm',
    size() === 'md' && 'px-4 py-2 text-sm',
    size() === 'lg' && 'px-4 py-3 text-base',
    // Variant classes
    variant() === 'default' && [
      'border-gray-300 bg-white',
      'focus:border-blue-500 focus:ring-blue-500',
      local.error && 'border-red-500 focus:border-red-500 focus:ring-red-500'
    ],
    variant() === 'filled' && [
      'border-transparent bg-gray-100',
      'focus:bg-white focus:border-blue-500 focus:ring-blue-500',
      local.error && 'bg-red-50 focus:border-red-500 focus:ring-red-500'
    ],
    variant() === 'outline' && [
      'border-2 border-gray-300 bg-transparent',
      'focus:border-blue-500 focus:ring-blue-500',
      local.error && 'border-red-500 focus:border-red-500 focus:ring-red-500'
    ],
    // Icon padding
    local.leftIcon && 'pl-10',
    local.rightIcon && 'pr-10',
    local.class
  ].filter(Boolean).flat().join(' ');

  return (
    <div class="w-full">
      <Show when={local.label}>
        <label
          for={inputId()}
          class={`block text-sm font-medium mb-1 ${
            local.error ? 'text-red-700' : 'text-gray-700'
          }`}
        >
          {local.label}
        </label>
      </Show>

      <div class="relative">
        <Show when={local.leftIcon}>
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <Dynamic component={local.leftIcon!} class="h-5 w-5 text-gray-400" />
          </div>
        </Show>

        <input
          id={inputId()}
          class={baseClasses()}
          {...others}
        />

        <Show when={local.rightIcon}>
          <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
            <Dynamic component={local.rightIcon!} class="h-5 w-5 text-gray-400" />
          </div>
        </Show>
      </div>

      <Show when={local.error || local.helperText}>
        <p class={`mt-1 text-sm ${
          local.error ? 'text-red-600' : 'text-gray-500'
        }`}>
          {local.error || local.helperText}
        </p>
      </Show>
    </div>
  );
};

// 密码输入组件
export interface PasswordInputProps extends Omit<InputProps, 'type' | 'rightIcon'> {
  showToggle?: boolean;
}

export const PasswordInput: Component<PasswordInputProps> = (props) => {
  const [local, others] = splitProps(props, ['showToggle']);
  const [showPassword, setShowPassword] = createSignal(false);

  const EyeIcon = () => (
    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
    </svg>
  );

  const EyeOffIcon = () => (
    <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
    </svg>
  );

  const togglePassword = () => setShowPassword(!showPassword());

  return (
    <div class="relative">
      <Input
        type={showPassword() ? 'text' : 'password'}
        {...others}
      />
      <Show when={local.showToggle !== false}>
        <button
          type="button"
          class="absolute inset-y-0 right-0 pr-3 flex items-center"
          onClick={togglePassword}
        >
          <Dynamic
            component={showPassword() ? EyeOffIcon : EyeIcon}
            class="h-5 w-5 text-gray-400 hover:text-gray-600"
          />
        </button>
      </Show>
    </div>
  );
};

// 文本域组件
export interface TextareaProps extends JSX.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string;
  error?: string;
  helperText?: string;
  variant?: 'default' | 'filled' | 'outline';
  resize?: 'none' | 'vertical' | 'horizontal' | 'both';
}

export const Textarea: Component<TextareaProps> = (props) => {
  const [local, others] = splitProps(props, [
    'label',
    'error',
    'helperText',
    'variant',
    'resize',
    'class',
    'id'
  ]);

  const variant = () => local.variant || 'default';
  const resize = () => local.resize || 'vertical';
  const textareaId = () => local.id || `textarea-${Math.random().toString(36).substr(2, 9)}`;

  const baseClasses = () => [
    'block w-full rounded-lg border transition-colors',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'px-4 py-2 text-sm',
    // Resize classes
    resize() === 'none' && 'resize-none',
    resize() === 'vertical' && 'resize-y',
    resize() === 'horizontal' && 'resize-x',
    resize() === 'both' && 'resize',
    // Variant classes
    variant() === 'default' && [
      'border-gray-300 bg-white',
      'focus:border-blue-500 focus:ring-blue-500',
      local.error && 'border-red-500 focus:border-red-500 focus:ring-red-500'
    ],
    variant() === 'filled' && [
      'border-transparent bg-gray-100',
      'focus:bg-white focus:border-blue-500 focus:ring-blue-500',
      local.error && 'bg-red-50 focus:border-red-500 focus:ring-red-500'
    ],
    variant() === 'outline' && [
      'border-2 border-gray-300 bg-transparent',
      'focus:border-blue-500 focus:ring-blue-500',
      local.error && 'border-red-500 focus:border-red-500 focus:ring-red-500'
    ],
    local.class
  ].filter(Boolean).flat().join(' ');

  return (
    <div class="w-full">
      <Show when={local.label}>
        <label
          for={textareaId()}
          class={`block text-sm font-medium mb-1 ${
            local.error ? 'text-red-700' : 'text-gray-700'
          }`}
        >
          {local.label}
        </label>
      </Show>

      <textarea
        id={textareaId()}
        class={baseClasses()}
        {...others}
      />

      <Show when={local.error || local.helperText}>
        <p class={`mt-1 text-sm ${
          local.error ? 'text-red-600' : 'text-gray-500'
        }`}>
          {local.error || local.helperText}
        </p>
      </Show>
    </div>
  );
};

// 选择框组件
export interface SelectProps extends JSX.SelectHTMLAttributes<HTMLSelectElement> {
  label?: string;
  error?: string;
  helperText?: string;
  placeholder?: string;
  options: Array<{ value: string; label: string; disabled?: boolean }>;
  variant?: 'default' | 'filled' | 'outline';
  size?: 'sm' | 'md' | 'lg';
}

export const Select: Component<SelectProps> = (props) => {
  const [local, others] = splitProps(props, [
    'label',
    'error',
    'helperText',
    'placeholder',
    'options',
    'variant',
    'size',
    'class',
    'id'
  ]);

  const variant = () => local.variant || 'default';
  const size = () => local.size || 'md';
  const selectId = () => local.id || `select-${Math.random().toString(36).substr(2, 9)}`;

  const baseClasses = () => [
    'block w-full rounded-lg border transition-colors',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'bg-white appearance-none',
    // Size classes
    size() === 'sm' && 'px-3 py-1.5 text-sm',
    size() === 'md' && 'px-4 py-2 text-sm',
    size() === 'lg' && 'px-4 py-3 text-base',
    // Variant classes
    variant() === 'default' && [
      'border-gray-300',
      'focus:border-blue-500 focus:ring-blue-500',
      local.error && 'border-red-500 focus:border-red-500 focus:ring-red-500'
    ],
    variant() === 'filled' && [
      'border-transparent bg-gray-100',
      'focus:bg-white focus:border-blue-500 focus:ring-blue-500',
      local.error && 'bg-red-50 focus:border-red-500 focus:ring-red-500'
    ],
    variant() === 'outline' && [
      'border-2 border-gray-300',
      'focus:border-blue-500 focus:ring-blue-500',
      local.error && 'border-red-500 focus:border-red-500 focus:ring-red-500'
    ],
    local.class
  ].filter(Boolean).flat().join(' ');

  return (
    <div class="w-full">
      <Show when={local.label}>
        <label
          for={selectId()}
          class={`block text-sm font-medium mb-1 ${
            local.error ? 'text-red-700' : 'text-gray-700'
          }`}
        >
          {local.label}
        </label>
      </Show>

      <div class="relative">
        <select
          id={selectId()}
          class={baseClasses()}
          {...others}
        >
          <Show when={local.placeholder}>
            <option value="" disabled>
              {local.placeholder}
            </option>
          </Show>
          {local.options.map((option) => (
            <option
              value={option.value}
              disabled={option.disabled}
            >
              {option.label}
            </option>
          ))}
        </select>
        
        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      <Show when={local.error || local.helperText}>
        <p class={`mt-1 text-sm ${
          local.error ? 'text-red-600' : 'text-gray-500'
        }`}>
          {local.error || local.helperText}
        </p>
      </Show>
    </div>
  );
};

// 复选框组件
export interface CheckboxProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
  error?: string;
  indeterminate?: boolean;
}

export const Checkbox: Component<CheckboxProps> = (props) => {
  const [local, others] = splitProps(props, [
    'label',
    'description',
    'error',
    'indeterminate',
    'class',
    'id'
  ]);

  const checkboxId = () => local.id || `checkbox-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div class="flex items-start">
      <div class="flex items-center h-5">
        <input
          id={checkboxId()}
          type="checkbox"
          class={`
            h-4 w-4 text-blue-600 border-gray-300 rounded
            focus:ring-blue-500 focus:ring-2
            ${local.error ? 'border-red-500' : ''}
            ${local.class || ''}
          `}
          {...others}
        />
      </div>
      
      <Show when={local.label || local.description}>
        <div class="ml-3 text-sm">
          <Show when={local.label}>
            <label
              for={checkboxId()}
              class={`font-medium ${
                local.error ? 'text-red-700' : 'text-gray-700'
              }`}
            >
              {local.label}
            </label>
          </Show>
          
          <Show when={local.description}>
            <p class="text-gray-500">{local.description}</p>
          </Show>
          
          <Show when={local.error}>
            <p class="text-red-600">{local.error}</p>
          </Show>
        </div>
      </Show>
    </div>
  );
};

// 单选框组件
export interface RadioProps extends JSX.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  description?: string;
  error?: string;
}

export const Radio: Component<RadioProps> = (props) => {
  const [local, others] = splitProps(props, [
    'label',
    'description',
    'error',
    'class',
    'id'
  ]);

  const radioId = () => local.id || `radio-${Math.random().toString(36).substr(2, 9)}`;

  return (
    <div class="flex items-start">
      <div class="flex items-center h-5">
        <input
          id={radioId()}
          type="radio"
          class={`
            h-4 w-4 text-blue-600 border-gray-300
            focus:ring-blue-500 focus:ring-2
            ${local.error ? 'border-red-500' : ''}
            ${local.class || ''}
          `}
          {...others}
        />
      </div>
      
      <Show when={local.label || local.description}>
        <div class="ml-3 text-sm">
          <Show when={local.label}>
            <label
              for={radioId()}
              class={`font-medium ${
                local.error ? 'text-red-700' : 'text-gray-700'
              }`}
            >
              {local.label}
            </label>
          </Show>
          
          <Show when={local.description}>
            <p class="text-gray-500">{local.description}</p>
          </Show>
          
          <Show when={local.error}>
            <p class="text-red-600">{local.error}</p>
          </Show>
        </div>
      </Show>
    </div>
  );
};

export default Input;
