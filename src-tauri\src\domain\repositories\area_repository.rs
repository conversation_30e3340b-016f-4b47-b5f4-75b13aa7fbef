// 领域仓储接口
// 定义领域数据访问的抽象接口

use crate::domain::entities::area::{Area, CreateAreaData, UpdateAreaData, AreaQuery, AreaStats};
use crate::shared::types::{EntityId, QueryParams};
use crate::shared::errors::AppResult;
use async_trait::async_trait;

/// 领域仓储接口
#[async_trait]
pub trait AreaRepository: Send + Sync {
    /// 创建领域
    async fn create(&self, data: CreateAreaData) -> AppResult<Area>;

    /// 根据ID查找领域
    async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<Area>>;

    /// 根据名称查找领域
    async fn find_by_name(&self, name: &str) -> AppResult<Option<Area>>;

    /// 更新领域
    async fn update(&self, id: &EntityId, data: UpdateAreaData) -> AppResult<Area>;

    /// 删除领域
    async fn delete(&self, id: &EntityId) -> AppResult<()>;

    /// 查询领域列表
    async fn find_all(&self, query: AreaQuery, params: QueryParams) -> AppResult<Vec<Area>>;

    /// 统计领域数量
    async fn count(&self, query: AreaQuery) -> AppResult<u64>;

    /// 根据创建者查找领域
    async fn find_by_creator(&self, creator_id: &EntityId, params: QueryParams) -> AppResult<Vec<Area>>;

    /// 查找活跃领域
    async fn find_active(&self, params: QueryParams) -> AppResult<Vec<Area>>;

    /// 查找已归档领域
    async fn find_archived(&self, params: QueryParams) -> AppResult<Vec<Area>>;

    /// 激活领域
    async fn activate(&self, id: &EntityId) -> AppResult<()>;

    /// 停用领域
    async fn deactivate(&self, id: &EntityId) -> AppResult<()>;

    /// 归档领域
    async fn archive(&self, id: &EntityId) -> AppResult<()>;

    /// 取消归档领域
    async fn unarchive(&self, id: &EntityId) -> AppResult<()>;

    /// 更新排序顺序
    async fn update_sort_order(&self, id: &EntityId, sort_order: i32) -> AppResult<()>;

    /// 批量更新排序顺序
    async fn batch_update_sort_order(&self, updates: Vec<(EntityId, i32)>) -> AppResult<()>;

    /// 检查名称是否存在
    async fn name_exists(&self, name: &str) -> AppResult<bool>;

    /// 获取领域统计信息
    async fn get_stats(&self, id: &EntityId) -> AppResult<AreaStats>;

    /// 获取领域摘要信息
    async fn get_area_summary(&self, id: &EntityId) -> AppResult<AreaSummary>;

    /// 搜索领域
    async fn search(&self, keyword: &str, params: QueryParams) -> AppResult<Vec<Area>>;
}

/// 领域摘要信息
#[derive(Debug, Clone)]
pub struct AreaSummary {
    pub area: Area,
    pub total_projects: u64,
    pub completed_projects: u64,
    pub active_projects: u64,
    pub total_tasks: u64,
    pub completed_tasks: u64,
    pub active_tasks: u64,
    pub total_habits: u64,
    pub active_habits: u64,
}
