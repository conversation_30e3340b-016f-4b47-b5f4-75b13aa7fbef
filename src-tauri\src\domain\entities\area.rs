// 领域实体
// 基于P.A.R.A.方法论的领域管理核心业务逻辑

use crate::shared::types::{EntityId, Timestamp};
use crate::shared::errors::{AppError, AppResult};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 领域实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Area {
    pub id: EntityId,
    pub name: String,
    pub description: Option<String>,
    pub standard: Option<String>, // 维护标准
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: i32,
    pub is_active: bool,
    pub created_by: EntityId,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub archived_at: Option<Timestamp>,
}

/// 领域创建数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAreaData {
    pub name: String,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub created_by: EntityId,
}

/// 领域更新数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateAreaData {
    pub name: Option<String>,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: Option<i32>,
    pub is_active: Option<bool>,
}

/// 领域查询条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaQuery {
    pub name: Option<String>,
    pub is_active: Option<bool>,
    pub created_by: Option<EntityId>,
    pub created_after: Option<Timestamp>,
    pub created_before: Option<Timestamp>,
}

impl Area {
    /// 创建新领域
    pub fn new(data: CreateAreaData) -> AppResult<Self> {
        // 验证领域名称
        Self::validate_name(&data.name)?;
        
        // 验证颜色格式
        if let Some(ref color) = data.color_hex {
            Self::validate_color(color)?;
        }

        let now = Utc::now();
        
        Ok(Self {
            id: crate::shared::utils::generate_id(),
            name: data.name,
            description: data.description,
            standard: data.standard,
            icon_name: data.icon_name,
            color_hex: data.color_hex,
            sort_order: 0,
            is_active: true,
            created_by: data.created_by,
            created_at: now,
            updated_at: now,
            archived_at: None,
        })
    }

    /// 更新领域信息
    pub fn update(&mut self, data: UpdateAreaData) -> AppResult<()> {
        // 验证领域名称
        if let Some(ref name) = data.name {
            Self::validate_name(name)?;
        }

        // 验证颜色格式
        if let Some(ref color) = data.color_hex {
            Self::validate_color(color)?;
        }

        // 更新字段
        if let Some(name) = data.name {
            self.name = name;
        }
        if let Some(description) = data.description {
            self.description = Some(description);
        }
        if let Some(standard) = data.standard {
            self.standard = Some(standard);
        }
        if let Some(icon_name) = data.icon_name {
            self.icon_name = Some(icon_name);
        }
        if let Some(color_hex) = data.color_hex {
            self.color_hex = Some(color_hex);
        }
        if let Some(sort_order) = data.sort_order {
            self.sort_order = sort_order;
        }
        if let Some(is_active) = data.is_active {
            self.is_active = is_active;
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// 激活领域
    pub fn activate(&mut self) -> AppResult<()> {
        if self.archived_at.is_some() {
            return Err(AppError::business_logic("已归档的领域不能激活"));
        }

        self.is_active = true;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 停用领域
    pub fn deactivate(&mut self) -> AppResult<()> {
        self.is_active = false;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 归档领域
    pub fn archive(&mut self) -> AppResult<()> {
        self.is_active = false;
        self.archived_at = Some(Utc::now());
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 取消归档
    pub fn unarchive(&mut self) -> AppResult<()> {
        if self.archived_at.is_none() {
            return Err(AppError::business_logic("领域未归档"));
        }

        self.is_active = true;
        self.archived_at = None;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 更新排序顺序
    pub fn update_sort_order(&mut self, new_order: i32) -> AppResult<()> {
        self.sort_order = new_order;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 验证领域名称
    fn validate_name(name: &str) -> AppResult<()> {
        if name.trim().is_empty() {
            return Err(AppError::validation("领域名称不能为空"));
        }
        
        if name.len() > 100 {
            return Err(AppError::validation("领域名称长度不能超过100个字符"));
        }

        Ok(())
    }

    /// 验证颜色格式
    fn validate_color(color: &str) -> AppResult<()> {
        if !color.starts_with('#') || color.len() != 7 {
            return Err(AppError::validation("颜色格式必须为 #RRGGBB"));
        }

        // 验证十六进制字符
        for c in color.chars().skip(1) {
            if !c.is_ascii_hexdigit() {
                return Err(AppError::validation("颜色包含无效的十六进制字符"));
            }
        }

        Ok(())
    }

    /// 检查是否已归档
    pub fn is_archived(&self) -> bool {
        self.archived_at.is_some()
    }

    /// 获取领域统计信息
    pub fn get_stats(&self) -> AreaStats {
        AreaStats {
            is_active: self.is_active,
            is_archived: self.is_archived(),
            days_since_created: (Utc::now() - self.created_at).num_days(),
            days_since_updated: (Utc::now() - self.updated_at).num_days(),
            days_since_archived: self.archived_at.map(|archived| {
                (Utc::now() - archived).num_days()
            }),
        }
    }
}

/// 领域统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaStats {
    pub is_active: bool,
    pub is_archived: bool,
    pub days_since_created: i64,
    pub days_since_updated: i64,
    pub days_since_archived: Option<i64>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_area_creation() {
        let data = CreateAreaData {
            name: "健康".to_string(),
            description: Some("健康相关的活动和目标".to_string()),
            standard: Some("每天运动30分钟".to_string()),
            icon_name: Some("health".to_string()),
            color_hex: Some("#4CAF50".to_string()),
            created_by: "user1".to_string(),
        };

        let area = Area::new(data).unwrap();
        assert_eq!(area.name, "健康");
        assert_eq!(area.color_hex, Some("#4CAF50".to_string()));
        assert!(area.is_active);
        assert!(area.archived_at.is_none());
    }

    #[test]
    fn test_area_validation() {
        // 测试空名称
        let data = CreateAreaData {
            name: "".to_string(),
            description: None,
            standard: None,
            icon_name: None,
            color_hex: None,
            created_by: "user1".to_string(),
        };
        assert!(Area::new(data).is_err());

        // 测试无效颜色
        let data = CreateAreaData {
            name: "测试领域".to_string(),
            description: None,
            standard: None,
            icon_name: None,
            color_hex: Some("invalid".to_string()),
            created_by: "user1".to_string(),
        };
        assert!(Area::new(data).is_err());
    }

    #[test]
    fn test_color_validation() {
        // 有效颜色
        assert!(Area::validate_color("#FF0000").is_ok());
        assert!(Area::validate_color("#4CAF50").is_ok());
        
        // 无效颜色
        assert!(Area::validate_color("FF0000").is_err()); // 缺少#
        assert!(Area::validate_color("#FF00").is_err()); // 长度不对
        assert!(Area::validate_color("#GGGGGG").is_err()); // 无效字符
    }

    #[test]
    fn test_area_lifecycle() {
        let data = CreateAreaData {
            name: "测试领域".to_string(),
            description: None,
            standard: None,
            icon_name: None,
            color_hex: None,
            created_by: "user1".to_string(),
        };

        let mut area = Area::new(data).unwrap();
        
        // 停用领域
        area.deactivate().unwrap();
        assert!(!area.is_active);
        
        // 激活领域
        area.activate().unwrap();
        assert!(area.is_active);
        
        // 归档领域
        area.archive().unwrap();
        assert!(!area.is_active);
        assert!(area.is_archived());
        
        // 取消归档
        area.unarchive().unwrap();
        assert!(area.is_active);
        assert!(!area.is_archived());
    }

    #[test]
    fn test_area_update() {
        let data = CreateAreaData {
            name: "原始名称".to_string(),
            description: None,
            standard: None,
            icon_name: None,
            color_hex: None,
            created_by: "user1".to_string(),
        };

        let mut area = Area::new(data).unwrap();
        
        let update_data = UpdateAreaData {
            name: Some("新名称".to_string()),
            description: Some("新描述".to_string()),
            standard: Some("新标准".to_string()),
            icon_name: Some("new_icon".to_string()),
            color_hex: Some("#FF5722".to_string()),
            sort_order: Some(10),
            is_active: Some(false),
        };

        area.update(update_data).unwrap();
        
        assert_eq!(area.name, "新名称");
        assert_eq!(area.description, Some("新描述".to_string()));
        assert_eq!(area.standard, Some("新标准".to_string()));
        assert_eq!(area.icon_name, Some("new_icon".to_string()));
        assert_eq!(area.color_hex, Some("#FF5722".to_string()));
        assert_eq!(area.sort_order, 10);
        assert!(!area.is_active);
    }
}
