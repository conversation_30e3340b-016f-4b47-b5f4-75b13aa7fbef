// 任务数据模型
// 对应数据库中的任务表结构

use sqlx::FromRow;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, NaiveDate};

/// 任务数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TaskModel {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub status: String,
    pub priority: i32,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub due_date: Option<NaiveDate>,
    pub estimated_minutes: Option<i32>,
    pub actual_minutes: i32,
    pub completion_percentage: i32,
    pub sort_order: i32,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 创建任务数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTaskModel {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub status: String,
    pub priority: i32,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub due_date: Option<NaiveDate>,
    pub estimated_minutes: Option<i32>,
    pub actual_minutes: i32,
    pub completion_percentage: i32,
    pub sort_order: i32,
}

/// 更新任务数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTaskModel {
    pub title: Option<String>,
    pub description: Option<String>,
    pub status: Option<String>,
    pub priority: Option<i32>,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub due_date: Option<NaiveDate>,
    pub estimated_minutes: Option<i32>,
    pub actual_minutes: Option<i32>,
    pub completion_percentage: Option<i32>,
    pub sort_order: Option<i32>,
    pub completed_at: Option<DateTime<Utc>>,
}

/// 任务查询模型
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct TaskQueryModel {
    pub title: Option<String>,
    pub status: Option<String>,
    pub priority: Option<i32>,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub has_due_date: Option<bool>,
    pub is_overdue: Option<bool>,
    pub is_completed: Option<bool>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
}

/// 任务层级结构模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskHierarchyModel {
    pub task: TaskModel,
    pub children: Vec<TaskHierarchyModel>,
    pub depth: i32,
}

/// 任务统计模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct TaskStatsModel {
    pub total_tasks: i64,
    pub active_tasks: i64,
    pub completed_tasks: i64,
    pub overdue_tasks: i64,
    pub tasks_by_priority_low: i64,
    pub tasks_by_priority_medium: i64,
    pub tasks_by_priority_high: i64,
    pub tasks_by_priority_urgent: i64,
    pub average_completion_percentage: f64,
    pub completion_rate: f64,
}

/// 用户任务统计模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserTaskStatsModel {
    pub user_id: String,
    pub total_assigned_tasks: i64,
    pub active_assigned_tasks: i64,
    pub completed_assigned_tasks: i64,
    pub overdue_assigned_tasks: i64,
    pub completion_rate: f64,
    pub average_completion_time_days: Option<f64>,
    pub total_estimated_minutes: i64,
    pub total_actual_minutes: i64,
}

impl TaskModel {
    /// 创建新的任务模型
    pub fn new(
        id: String,
        title: String,
        description: Option<String>,
        priority: i32,
        project_id: Option<String>,
        area_id: Option<String>,
        assigned_to: Option<String>,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            title,
            description,
            status: "todo".to_string(),
            priority,
            parent_task_id: None,
            project_id,
            area_id,
            assigned_to,
            due_date: None,
            estimated_minutes: None,
            actual_minutes: 0,
            completion_percentage: 0,
            sort_order: 0,
            created_at: now,
            updated_at: now,
            completed_at: None,
        }
    }

    /// 检查任务是否已完成
    pub fn is_completed(&self) -> bool {
        self.status == "completed"
    }

    /// 检查任务是否逾期
    pub fn is_overdue(&self) -> bool {
        if let Some(due_date) = self.due_date {
            let today = chrono::Utc::now().date_naive();
            due_date < today && !self.is_completed()
        } else {
            false
        }
    }

    /// 检查是否为子任务
    pub fn is_subtask(&self) -> bool {
        self.parent_task_id.is_some()
    }

    /// 获取任务状态显示文本
    pub fn status_display(&self) -> &str {
        match self.status.as_str() {
            "todo" => "待办",
            "in_progress" => "进行中",
            "waiting" => "等待中",
            "completed" => "已完成",
            "cancelled" => "已取消",
            _ => "未知状态",
        }
    }

    /// 获取优先级显示文本
    pub fn priority_display(&self) -> &str {
        match self.priority {
            1 => "低",
            2 => "中",
            3 => "高",
            4 => "紧急",
            _ => "未知",
        }
    }

    /// 计算剩余天数
    pub fn days_remaining(&self) -> Option<i64> {
        if let Some(due_date) = self.due_date {
            let today = chrono::Utc::now().date_naive();
            Some((due_date - today).num_days())
        } else {
            None
        }
    }

    /// 计算任务持续时间（天数）
    pub fn duration_days(&self) -> i64 {
        (Utc::now() - self.created_at).num_days()
    }

    /// 检查是否有预估时间
    pub fn has_estimated_time(&self) -> bool {
        self.estimated_minutes.is_some() && self.estimated_minutes.unwrap() > 0
    }

    /// 计算时间使用率
    pub fn time_utilization_rate(&self) -> Option<f64> {
        if let Some(estimated) = self.estimated_minutes {
            if estimated > 0 {
                Some(self.actual_minutes as f64 / estimated as f64)
            } else {
                None
            }
        } else {
            None
        }
    }

    /// 检查是否需要关注（逾期或高优先级）
    pub fn needs_attention(&self) -> bool {
        self.is_overdue() || self.priority >= 3
    }
}

impl CreateTaskModel {
    /// 创建新的创建任务模型
    pub fn new(
        id: String,
        title: String,
        description: Option<String>,
        priority: i32,
        project_id: Option<String>,
        area_id: Option<String>,
        assigned_to: Option<String>,
    ) -> Self {
        Self {
            id,
            title,
            description,
            status: "todo".to_string(),
            priority,
            parent_task_id: None,
            project_id,
            area_id,
            assigned_to,
            due_date: None,
            estimated_minutes: None,
            actual_minutes: 0,
            completion_percentage: 0,
            sort_order: 0,
        }
    }

    /// 设置为子任务
    pub fn as_subtask(mut self, parent_task_id: String) -> Self {
        self.parent_task_id = Some(parent_task_id);
        self
    }

    /// 设置截止日期
    pub fn with_due_date(mut self, due_date: NaiveDate) -> Self {
        self.due_date = Some(due_date);
        self
    }

    /// 设置预估时间
    pub fn with_estimated_minutes(mut self, minutes: i32) -> Self {
        self.estimated_minutes = Some(minutes);
        self
    }

    /// 设置排序顺序
    pub fn with_sort_order(mut self, sort_order: i32) -> Self {
        self.sort_order = sort_order;
        self
    }
}

impl UpdateTaskModel {
    /// 创建空的更新模型
    pub fn new() -> Self {
        Self {
            title: None,
            description: None,
            status: None,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_date: None,
            estimated_minutes: None,
            actual_minutes: None,
            completion_percentage: None,
            sort_order: None,
            completed_at: None,
        }
    }

    /// 设置标题
    pub fn with_title(mut self, title: String) -> Self {
        self.title = Some(title);
        self
    }

    /// 设置状态
    pub fn with_status(mut self, status: String) -> Self {
        self.status = Some(status);
        self
    }

    /// 设置完成度
    pub fn with_completion_percentage(mut self, percentage: i32) -> Self {
        self.completion_percentage = Some(percentage);
        self
    }

    /// 标记为完成
    pub fn mark_completed(mut self) -> Self {
        self.status = Some("completed".to_string());
        self.completion_percentage = Some(100);
        self.completed_at = Some(Utc::now());
        self
    }

    /// 检查是否有更新
    pub fn has_updates(&self) -> bool {
        self.title.is_some()
            || self.description.is_some()
            || self.status.is_some()
            || self.priority.is_some()
            || self.parent_task_id.is_some()
            || self.project_id.is_some()
            || self.area_id.is_some()
            || self.assigned_to.is_some()
            || self.due_date.is_some()
            || self.estimated_minutes.is_some()
            || self.actual_minutes.is_some()
            || self.completion_percentage.is_some()
            || self.sort_order.is_some()
            || self.completed_at.is_some()
    }
}

impl TaskQueryModel {
    /// 创建新的查询模型
    pub fn new() -> Self {
        Self::default()
    }

    /// 按标题查询
    pub fn by_title(mut self, title: String) -> Self {
        self.title = Some(title);
        self
    }

    /// 按状态查询
    pub fn by_status(mut self, status: String) -> Self {
        self.status = Some(status);
        self
    }

    /// 按项目查询
    pub fn by_project(mut self, project_id: String) -> Self {
        self.project_id = Some(project_id);
        self
    }

    /// 按分配人查询
    pub fn by_assignee(mut self, assigned_to: String) -> Self {
        self.assigned_to = Some(assigned_to);
        self
    }

    /// 查询逾期任务
    pub fn overdue_only(mut self) -> Self {
        self.is_overdue = Some(true);
        self
    }

    /// 查询已完成任务
    pub fn completed_only(mut self) -> Self {
        self.is_completed = Some(true);
        self
    }

    /// 检查是否有查询条件
    pub fn has_conditions(&self) -> bool {
        self.title.is_some()
            || self.status.is_some()
            || self.priority.is_some()
            || self.parent_task_id.is_some()
            || self.project_id.is_some()
            || self.area_id.is_some()
            || self.assigned_to.is_some()
            || self.has_due_date.is_some()
            || self.is_overdue.is_some()
            || self.is_completed.is_some()
            || self.created_after.is_some()
            || self.created_before.is_some()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_task_model_creation() {
        let task = TaskModel::new(
            "task123".to_string(),
            "Test Task".to_string(),
            Some("A test task".to_string()),
            2,
            Some("proj123".to_string()),
            Some("area123".to_string()),
            Some("user123".to_string()),
        );

        assert_eq!(task.id, "task123");
        assert_eq!(task.title, "Test Task");
        assert_eq!(task.status, "todo");
        assert!(!task.is_completed());
        assert!(!task.is_subtask());
    }

    #[test]
    fn test_task_overdue_check() {
        let mut task = TaskModel::new(
            "task123".to_string(),
            "Test Task".to_string(),
            None,
            2,
            None,
            None,
            None,
        );

        // 设置过去的截止日期
        task.due_date = Some(chrono::Utc::now().date_naive() - chrono::Duration::days(1));
        assert!(task.is_overdue());

        // 设置未来的截止日期
        task.due_date = Some(chrono::Utc::now().date_naive() + chrono::Duration::days(1));
        assert!(!task.is_overdue());

        // 完成的任务不算逾期
        task.status = "completed".to_string();
        task.due_date = Some(chrono::Utc::now().date_naive() - chrono::Duration::days(1));
        assert!(!task.is_overdue());
    }

    #[test]
    fn test_update_task_model() {
        let update_task = UpdateTaskModel::new()
            .with_title("Updated Task".to_string())
            .with_status("in_progress".to_string())
            .with_completion_percentage(50);

        assert!(update_task.has_updates());
        assert_eq!(update_task.title, Some("Updated Task".to_string()));
        assert_eq!(update_task.status, Some("in_progress".to_string()));
        assert_eq!(update_task.completion_percentage, Some(50));
    }
}
