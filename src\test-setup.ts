// 测试环境设置文件
// 配置全局测试环境和模拟

import '@testing-library/jest-dom';

// 模拟Tauri API
const mockTauri = {
  invoke: vi.fn(),
  listen: vi.fn(),
  emit: vi.fn(),
  convertFileSrc: vi.fn((src: string) => src),
};

// 模拟Tauri插件
const mockPlugins = {
  opener: {
    open: vi.fn(),
  },
  globalShortcut: {
    register: vi.fn(),
    unregister: vi.fn(),
  },
  windowState: {
    saveWindowState: vi.fn(),
    restoreStateCurrent: vi.fn(),
  },
  store: {
    get: vi.fn(),
    set: vi.fn(),
    delete: vi.fn(),
    clear: vi.fn(),
  },
  fs: {
    readTextFile: vi.fn(),
    writeTextFile: vi.fn(),
    exists: vi.fn(),
    createDir: vi.fn(),
  },
  dialog: {
    open: vi.fn(),
    save: vi.fn(),
    message: vi.fn(),
    ask: vi.fn(),
    confirm: vi.fn(),
  },
};

// 设置全局模拟
Object.defineProperty(window, '__TAURI__', {
  value: mockTauri,
});

Object.defineProperty(window, '__TAURI_PLUGIN_OPENER__', {
  value: mockPlugins.opener,
});

Object.defineProperty(window, '__TAURI_PLUGIN_GLOBAL_SHORTCUT__', {
  value: mockPlugins.globalShortcut,
});

Object.defineProperty(window, '__TAURI_PLUGIN_WINDOW_STATE__', {
  value: mockPlugins.windowState,
});

Object.defineProperty(window, '__TAURI_PLUGIN_STORE__', {
  value: mockPlugins.store,
});

Object.defineProperty(window, '__TAURI_PLUGIN_FS__', {
  value: mockPlugins.fs,
});

Object.defineProperty(window, '__TAURI_PLUGIN_DIALOG__', {
  value: mockPlugins.dialog,
});

// 模拟ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 模拟IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// 模拟matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// 模拟localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// 模拟sessionStorage
const sessionStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock,
});

// 模拟URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: vi.fn(() => 'mocked-object-url'),
});

// 模拟URL.revokeObjectURL
Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: vi.fn(),
});

// 模拟fetch
global.fetch = vi.fn();

// 设置默认的测试超时时间
vi.setConfig({
  testTimeout: 10000,
});

// 在每个测试后清理模拟
afterEach(() => {
  vi.clearAllMocks();
});

// 导出模拟对象供测试使用
export { mockTauri, mockPlugins };
