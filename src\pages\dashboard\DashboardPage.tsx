// 仪表板页面
// 显示用户的数据概览、统计信息和快速操作

import { Component, createSignal, createEffect, Show, For } from 'solid-js';
import { useNavigate } from '@solidjs/router';
import { MainLayout } from '../../components/layout/MainLayout';
import { Button } from '../../components/ui/Button';
import { api } from '../../utils/api';
import toast from 'solid-toast';

interface DashboardStats {
  total_projects: number;
  active_projects: number;
  completed_projects: number;
  total_tasks: number;
  completed_tasks: number;
  overdue_tasks: number;
  completion_rate: number;
  productivity_score: number;
}

interface RecentActivity {
  id: string;
  type: 'project_created' | 'task_completed' | 'project_completed';
  title: string;
  description: string;
  timestamp: string;
}

interface UpcomingDeadline {
  id: string;
  type: 'project' | 'task';
  title: string;
  deadline: string;
  days_remaining: number;
  priority: string;
}

export const DashboardPage: Component = () => {
  const navigate = useNavigate();
  
  const [stats, setStats] = createSignal<DashboardStats>({
    total_projects: 0,
    active_projects: 0,
    completed_projects: 0,
    total_tasks: 0,
    completed_tasks: 0,
    overdue_tasks: 0,
    completion_rate: 0,
    productivity_score: 0,
  });
  
  const [recentActivities, setRecentActivities] = createSignal<RecentActivity[]>([]);
  const [upcomingDeadlines, setUpcomingDeadlines] = createSignal<UpcomingDeadline[]>([]);
  const [loading, setLoading] = createSignal(true);

  // 加载仪表板数据
  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 模拟API调用 - 在实际应用中这些应该是真实的API调用
      const mockStats: DashboardStats = {
        total_projects: 12,
        active_projects: 8,
        completed_projects: 4,
        total_tasks: 45,
        completed_tasks: 32,
        overdue_tasks: 3,
        completion_rate: 71.1,
        productivity_score: 85.5,
      };

      const mockActivities: RecentActivity[] = [
        {
          id: '1',
          type: 'task_completed',
          title: '完成了任务',
          description: '用户界面设计已完成',
          timestamp: '2024-01-15T10:30:00Z'
        },
        {
          id: '2',
          type: 'project_created',
          title: '创建了项目',
          description: '新建了"移动应用开发"项目',
          timestamp: '2024-01-15T09:15:00Z'
        },
        {
          id: '3',
          type: 'project_completed',
          title: '完成了项目',
          description: '"网站重构"项目已完成',
          timestamp: '2024-01-14T16:45:00Z'
        }
      ];

      const mockDeadlines: UpcomingDeadline[] = [
        {
          id: '1',
          type: 'project',
          title: '移动应用开发',
          deadline: '2024-01-20',
          days_remaining: 5,
          priority: 'high'
        },
        {
          id: '2',
          type: 'task',
          title: '数据库优化',
          deadline: '2024-01-18',
          days_remaining: 3,
          priority: 'urgent'
        },
        {
          id: '3',
          type: 'task',
          title: '用户测试',
          deadline: '2024-01-25',
          days_remaining: 10,
          priority: 'medium'
        }
      ];

      setStats(mockStats);
      setRecentActivities(mockActivities);
      setUpcomingDeadlines(mockDeadlines);
      
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast.error('加载仪表板数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  createEffect(() => {
    loadDashboardData();
  });

  // 格式化时间
  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - time.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return '刚刚';
    if (diffInHours < 24) return `${diffInHours}小时前`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}天前`;
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // 获取活动图标
  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'project_created':
        return (
          <svg class="h-5 w-5 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
        );
      case 'task_completed':
        return (
          <svg class="h-5 w-5 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      case 'project_completed':
        return (
          <svg class="h-5 w-5 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
      default:
        return (
          <svg class="h-5 w-5 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        );
    }
  };

  return (
    <MainLayout title="仪表板">
      <div class="space-y-6">
        {/* 欢迎信息 */}
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg">
          <div class="px-6 py-8 text-white">
            <h1 class="text-2xl font-bold">欢迎回来！</h1>
            <p class="mt-2 text-blue-100">
              今天是 {new Date().toLocaleDateString('zh-CN', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric',
                weekday: 'long'
              })}
            </p>
          </div>
        </div>

        <Show
          when={!loading()}
          fallback={
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
              {Array.from({ length: 4 }).map(() => (
                <div class="bg-white p-6 rounded-lg shadow animate-pulse">
                  <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div class="h-8 bg-gray-200 rounded w-1/2"></div>
                </div>
              ))}
            </div>
          }
        >
          {/* 统计卡片 */}
          <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">总项目数</dt>
                      <dd class="text-lg font-medium text-gray-900">{stats().total_projects}</dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                  <span class="text-green-600 font-medium">{stats().active_projects}</span>
                  <span class="text-gray-500"> 进行中</span>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">总任务数</dt>
                      <dd class="text-lg font-medium text-gray-900">{stats().total_tasks}</dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                  <span class="text-green-600 font-medium">{stats().completed_tasks}</span>
                  <span class="text-gray-500"> 已完成</span>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">完成率</dt>
                      <dd class="text-lg font-medium text-gray-900">{stats().completion_rate.toFixed(1)}%</dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                  <span class="text-red-600 font-medium">{stats().overdue_tasks}</span>
                  <span class="text-gray-500"> 逾期任务</span>
                </div>
              </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
              <div class="p-5">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                  </div>
                  <div class="ml-5 w-0 flex-1">
                    <dl>
                      <dt class="text-sm font-medium text-gray-500 truncate">生产力评分</dt>
                      <dd class="text-lg font-medium text-gray-900">{stats().productivity_score.toFixed(1)}</dd>
                    </dl>
                  </div>
                </div>
              </div>
              <div class="bg-gray-50 px-5 py-3">
                <div class="text-sm">
                  <span class="text-green-600 font-medium">优秀</span>
                  <span class="text-gray-500"> 继续保持</span>
                </div>
              </div>
            </div>
          </div>

          {/* 主要内容区域 */}
          <div class="grid grid-cols-1 gap-6 lg:grid-cols-3">
            {/* 快速操作 */}
            <div class="bg-white shadow rounded-lg p-6">
              <h3 class="text-lg font-medium text-gray-900 mb-4">快速操作</h3>
              <div class="space-y-3">
                <Button
                  class="w-full justify-start"
                  variant="outline"
                  onClick={() => navigate('/projects/new')}
                >
                  <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  新建项目
                </Button>
                <Button
                  class="w-full justify-start"
                  variant="outline"
                  onClick={() => navigate('/tasks/new')}
                >
                  <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  新建任务
                </Button>
                <Button
                  class="w-full justify-start"
                  variant="outline"
                  onClick={() => navigate('/areas/new')}
                >
                  <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  新建领域
                </Button>
                <Button
                  class="w-full justify-start"
                  variant="outline"
                  onClick={() => navigate('/analytics')}
                >
                  <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                  查看统计
                </Button>
              </div>
            </div>

            {/* 最近活动 */}
            <div class="bg-white shadow rounded-lg p-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">最近活动</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/activities')}
                >
                  查看全部
                </Button>
              </div>
              <div class="space-y-4">
                <For each={recentActivities()}>
                  {(activity) => (
                    <div class="flex items-start space-x-3">
                      <div class="flex-shrink-0 mt-0.5">
                        {getActivityIcon(activity.type)}
                      </div>
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">
                          {activity.title}
                        </p>
                        <p class="text-sm text-gray-500">
                          {activity.description}
                        </p>
                        <p class="text-xs text-gray-400 mt-1">
                          {formatTimeAgo(activity.timestamp)}
                        </p>
                      </div>
                    </div>
                  )}
                </For>
              </div>
            </div>

            {/* 即将到期 */}
            <div class="bg-white shadow rounded-lg p-6">
              <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">即将到期</h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => navigate('/tasks?filter=upcoming')}
                >
                  查看全部
                </Button>
              </div>
              <div class="space-y-4">
                <For each={upcomingDeadlines()}>
                  {(deadline) => (
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900 truncate">
                          {deadline.title}
                        </p>
                        <p class="text-xs text-gray-500">
                          {deadline.days_remaining > 0 
                            ? `${deadline.days_remaining} 天后到期`
                            : deadline.days_remaining === 0
                            ? '今天到期'
                            : `逾期 ${Math.abs(deadline.days_remaining)} 天`
                          }
                        </p>
                      </div>
                      <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(deadline.priority)}`}>
                        {deadline.priority === 'urgent' ? '紧急' :
                         deadline.priority === 'high' ? '高' :
                         deadline.priority === 'medium' ? '中' :
                         deadline.priority === 'low' ? '低' : deadline.priority}
                      </span>
                    </div>
                  )}
                </For>
              </div>
            </div>
          </div>

          {/* 进度图表区域 */}
          <div class="bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">本周进度概览</h3>
            <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
              {/* 任务完成趋势 */}
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-3">任务完成趋势</h4>
                <div class="space-y-2">
                  {['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map((day, index) => {
                    const completed = Math.floor(Math.random() * 10) + 1;
                    const total = completed + Math.floor(Math.random() * 5);
                    const percentage = (completed / total) * 100;
                    
                    return (
                      <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600 w-12">{day}</span>
                        <div class="flex-1 mx-3">
                          <div class="w-full bg-gray-200 rounded-full h-2">
                            <div
                              class="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${percentage}%` }}
                            />
                          </div>
                        </div>
                        <span class="text-sm text-gray-600 w-16">{completed}/{total}</span>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* 项目状态分布 */}
              <div>
                <h4 class="text-sm font-medium text-gray-700 mb-3">项目状态分布</h4>
                <div class="space-y-3">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                      <span class="text-sm text-gray-600">已完成</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">{stats().completed_projects}</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
                      <span class="text-sm text-gray-600">进行中</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">{stats().active_projects}</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <div class="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                      <span class="text-sm text-gray-600">暂停</span>
                    </div>
                    <span class="text-sm font-medium text-gray-900">0</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Show>
      </div>
    </MainLayout>
  );
};

export default DashboardPage;
