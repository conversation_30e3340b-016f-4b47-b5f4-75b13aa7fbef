// 领域数据模型
// 对应数据库中的领域表结构

use sqlx::FromRow;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 领域数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AreaModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: i32,
    pub is_active: bool,
    pub created_by: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub archived_at: Option<DateTime<Utc>>,
}

/// 创建领域数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAreaModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: i32,
    pub is_active: bool,
    pub created_by: String,
}

/// 更新领域数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAreaModel {
    pub name: Option<String>,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: Option<i32>,
    pub is_active: Option<bool>,
    pub archived_at: Option<DateTime<Utc>>,
}

/// 领域查询模型
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct AreaQueryModel {
    pub name: Option<String>,
    pub is_active: Option<bool>,
    pub created_by: Option<String>,
    pub has_standard: Option<bool>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
}

/// 领域摘要模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AreaSummaryModel {
    pub area_id: String,
    pub area_name: String,
    pub total_projects: i64,
    pub completed_projects: i64,
    pub active_projects: i64,
    pub total_tasks: i64,
    pub completed_tasks: i64,
    pub active_tasks: i64,
    pub total_habits: i64,
    pub active_habits: i64,
}

/// 领域统计模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct AreaStatsModel {
    pub total_areas: i64,
    pub active_areas: i64,
    pub archived_areas: i64,
    pub areas_with_projects: i64,
    pub areas_with_tasks: i64,
    pub areas_with_habits: i64,
    pub average_projects_per_area: f64,
    pub average_tasks_per_area: f64,
}

impl AreaModel {
    /// 创建新的领域模型
    pub fn new(
        id: String,
        name: String,
        description: Option<String>,
        created_by: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            name,
            description,
            standard: None,
            icon_name: None,
            color_hex: None,
            sort_order: 0,
            is_active: true,
            created_by,
            created_at: now,
            updated_at: now,
            archived_at: None,
        }
    }

    /// 检查领域是否激活
    pub fn is_active(&self) -> bool {
        self.is_active && self.archived_at.is_none()
    }

    /// 检查领域是否已归档
    pub fn is_archived(&self) -> bool {
        self.archived_at.is_some()
    }

    /// 检查是否有标准定义
    pub fn has_standard(&self) -> bool {
        self.standard.is_some() && !self.standard.as_ref().unwrap().trim().is_empty()
    }

    /// 检查是否有图标
    pub fn has_icon(&self) -> bool {
        self.icon_name.is_some() && !self.icon_name.as_ref().unwrap().trim().is_empty()
    }

    /// 检查是否有颜色
    pub fn has_color(&self) -> bool {
        self.color_hex.is_some() && !self.color_hex.as_ref().unwrap().trim().is_empty()
    }

    /// 获取显示名称
    pub fn display_name(&self) -> &str {
        &self.name
    }

    /// 获取颜色或默认颜色
    pub fn color_or_default(&self) -> String {
        self.color_hex.clone().unwrap_or_else(|| "#6B7280".to_string())
    }

    /// 获取图标或默认图标
    pub fn icon_or_default(&self) -> String {
        self.icon_name.clone().unwrap_or_else(|| "folder".to_string())
    }

    /// 计算领域年龄（天数）
    pub fn age_days(&self) -> i64 {
        (Utc::now() - self.created_at).num_days()
    }

    /// 检查是否为新创建的领域（7天内）
    pub fn is_new(&self) -> bool {
        self.age_days() <= 7
    }
}

impl CreateAreaModel {
    /// 创建新的创建领域模型
    pub fn new(
        id: String,
        name: String,
        description: Option<String>,
        created_by: String,
    ) -> Self {
        Self {
            id,
            name,
            description,
            standard: None,
            icon_name: None,
            color_hex: None,
            sort_order: 0,
            is_active: true,
            created_by,
        }
    }

    /// 设置标准
    pub fn with_standard(mut self, standard: String) -> Self {
        self.standard = Some(standard);
        self
    }

    /// 设置图标
    pub fn with_icon(mut self, icon_name: String) -> Self {
        self.icon_name = Some(icon_name);
        self
    }

    /// 设置颜色
    pub fn with_color(mut self, color_hex: String) -> Self {
        self.color_hex = Some(color_hex);
        self
    }

    /// 设置排序顺序
    pub fn with_sort_order(mut self, sort_order: i32) -> Self {
        self.sort_order = sort_order;
        self
    }

    /// 设置为非激活状态
    pub fn inactive(mut self) -> Self {
        self.is_active = false;
        self
    }
}

impl UpdateAreaModel {
    /// 创建空的更新模型
    pub fn new() -> Self {
        Self {
            name: None,
            description: None,
            standard: None,
            icon_name: None,
            color_hex: None,
            sort_order: None,
            is_active: None,
            archived_at: None,
        }
    }

    /// 设置名称
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// 设置描述
    pub fn with_description(mut self, description: Option<String>) -> Self {
        self.description = description;
        self
    }

    /// 设置标准
    pub fn with_standard(mut self, standard: Option<String>) -> Self {
        self.standard = standard;
        self
    }

    /// 设置图标
    pub fn with_icon(mut self, icon_name: Option<String>) -> Self {
        self.icon_name = icon_name;
        self
    }

    /// 设置颜色
    pub fn with_color(mut self, color_hex: Option<String>) -> Self {
        self.color_hex = color_hex;
        self
    }

    /// 设置排序顺序
    pub fn with_sort_order(mut self, sort_order: i32) -> Self {
        self.sort_order = Some(sort_order);
        self
    }

    /// 激活领域
    pub fn activate(mut self) -> Self {
        self.is_active = Some(true);
        self.archived_at = None;
        self
    }

    /// 停用领域
    pub fn deactivate(mut self) -> Self {
        self.is_active = Some(false);
        self
    }

    /// 归档领域
    pub fn archive(mut self) -> Self {
        self.is_active = Some(false);
        self.archived_at = Some(Utc::now());
        self
    }

    /// 检查是否有更新
    pub fn has_updates(&self) -> bool {
        self.name.is_some()
            || self.description.is_some()
            || self.standard.is_some()
            || self.icon_name.is_some()
            || self.color_hex.is_some()
            || self.sort_order.is_some()
            || self.is_active.is_some()
            || self.archived_at.is_some()
    }
}

impl AreaQueryModel {
    /// 创建新的查询模型
    pub fn new() -> Self {
        Self::default()
    }

    /// 按名称查询
    pub fn by_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// 按激活状态查询
    pub fn by_active_status(mut self, is_active: bool) -> Self {
        self.is_active = Some(is_active);
        self
    }

    /// 按创建者查询
    pub fn by_creator(mut self, created_by: String) -> Self {
        self.created_by = Some(created_by);
        self
    }

    /// 查询有标准的领域
    pub fn with_standard(mut self) -> Self {
        self.has_standard = Some(true);
        self
    }

    /// 按创建时间范围查询
    pub fn by_created_range(mut self, after: Option<DateTime<Utc>>, before: Option<DateTime<Utc>>) -> Self {
        self.created_after = after;
        self.created_before = before;
        self
    }

    /// 检查是否有查询条件
    pub fn has_conditions(&self) -> bool {
        self.name.is_some()
            || self.is_active.is_some()
            || self.created_by.is_some()
            || self.has_standard.is_some()
            || self.created_after.is_some()
            || self.created_before.is_some()
    }
}

impl AreaSummaryModel {
    /// 计算项目完成率
    pub fn project_completion_rate(&self) -> f64 {
        if self.total_projects > 0 {
            self.completed_projects as f64 / self.total_projects as f64
        } else {
            0.0
        }
    }

    /// 计算任务完成率
    pub fn task_completion_rate(&self) -> f64 {
        if self.total_tasks > 0 {
            self.completed_tasks as f64 / self.total_tasks as f64
        } else {
            0.0
        }
    }

    /// 检查是否有活动
    pub fn has_activity(&self) -> bool {
        self.total_projects > 0 || self.total_tasks > 0 || self.total_habits > 0
    }

    /// 检查是否需要关注（有活跃项目或任务但完成率低）
    pub fn needs_attention(&self) -> bool {
        (self.active_projects > 0 && self.project_completion_rate() < 0.5)
            || (self.active_tasks > 0 && self.task_completion_rate() < 0.5)
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_area_model_creation() {
        let area = AreaModel::new(
            "area123".to_string(),
            "Work".to_string(),
            Some("Work related activities".to_string()),
            "user123".to_string(),
        );

        assert_eq!(area.id, "area123");
        assert_eq!(area.name, "Work");
        assert!(area.is_active());
        assert!(!area.is_archived());
        assert!(!area.has_standard());
    }

    #[test]
    fn test_create_area_model() {
        let create_area = CreateAreaModel::new(
            "area123".to_string(),
            "Health".to_string(),
            Some("Health and fitness".to_string()),
            "user123".to_string(),
        )
        .with_standard("Maintain good health".to_string())
        .with_icon("heart".to_string())
        .with_color("#FF6B6B".to_string());

        assert_eq!(create_area.name, "Health");
        assert_eq!(create_area.standard, Some("Maintain good health".to_string()));
        assert_eq!(create_area.icon_name, Some("heart".to_string()));
        assert_eq!(create_area.color_hex, Some("#FF6B6B".to_string()));
    }

    #[test]
    fn test_update_area_model() {
        let update_area = UpdateAreaModel::new()
            .with_name("Updated Area".to_string())
            .with_color(Some("#00FF00".to_string()))
            .activate();

        assert!(update_area.has_updates());
        assert_eq!(update_area.name, Some("Updated Area".to_string()));
        assert_eq!(update_area.color_hex, Some("#00FF00".to_string()));
        assert_eq!(update_area.is_active, Some(true));
    }

    #[test]
    fn test_area_summary_calculations() {
        let summary = AreaSummaryModel {
            area_id: "area123".to_string(),
            area_name: "Test Area".to_string(),
            total_projects: 10,
            completed_projects: 7,
            active_projects: 3,
            total_tasks: 20,
            completed_tasks: 15,
            active_tasks: 5,
            total_habits: 5,
            active_habits: 4,
        };

        assert_eq!(summary.project_completion_rate(), 0.7);
        assert_eq!(summary.task_completion_rate(), 0.75);
        assert!(summary.has_activity());
        assert!(!summary.needs_attention()); // 完成率都高于50%
    }
}
