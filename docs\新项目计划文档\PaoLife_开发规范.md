# PaoLife 开发规范

## 项目概述

本文档定义了PaoLife项目的开发规范，包括代码风格、工作流程、提交规范等，确保团队协作的一致性和代码质量。

## 第一部分：代码规范

### 1. Rust 代码规范

#### 1.1 格式化规范
```toml
# rustfmt.toml - Rust 代码格式化配置
max_width = 100
hard_tabs = false
tab_spaces = 4
newline_style = "Unix"
use_small_heuristics = "Default"
reorder_imports = true
reorder_modules = true
remove_nested_parens = true
edition = "2021"
```

#### 1.2 Clippy 配置
```toml
# .clippy.toml - Clippy 静态分析配置
msrv = "1.81.0"
avoid-breaking-exported-api = true
```

#### 1.3 命名规范
```rust
// 模块命名：snake_case
mod user_management;
mod project_service;

// 结构体命名：PascalCase
struct ProjectData {
    id: String,
    name: String,
}

// 函数命名：snake_case
fn create_project(data: ProjectData) -> Result<Project, AppError> {
    // 实现
}

// 常量命名：SCREAMING_SNAKE_CASE
const MAX_PROJECT_COUNT: usize = 1000;
const DEFAULT_TIMEOUT: Duration = Duration::from_secs(30);

// 枚举命名：PascalCase
enum ProjectStatus {
    NotStarted,
    InProgress,
    Completed,
}
```

#### 1.4 错误处理规范
```rust
// 使用 thiserror 定义错误类型
#[derive(Debug, thiserror::Error)]
pub enum AppError {
    #[error("Database error: {0}")]
    Database(#[from] sqlx::Error),
    
    #[error("Validation error: {message}")]
    Validation { message: String },
    
    #[error("Not found: {resource}")]
    NotFound { resource: String },
}

// 统一的结果类型
pub type AppResult<T> = Result<T, AppError>;

// 错误处理最佳实践
async fn get_project(id: &str) -> AppResult<Project> {
    let project = sqlx::query_as!(
        Project,
        "SELECT * FROM projects WHERE id = ?",
        id
    )
    .fetch_optional(&db)
    .await?
    .ok_or_else(|| AppError::NotFound { 
        resource: format!("Project with id: {}", id) 
    })?;
    
    Ok(project)
}
```

#### 1.5 文档注释规范
```rust
/// 创建新项目
/// 
/// # 参数
/// 
/// * `request` - 项目创建请求，包含项目基本信息
/// 
/// # 返回值
/// 
/// 返回创建成功的项目信息，如果创建失败则返回错误
/// 
/// # 错误
/// 
/// * `AppError::Validation` - 输入数据验证失败
/// * `AppError::Database` - 数据库操作失败
/// 
/// # 示例
/// 
/// ```rust
/// let request = CreateProjectRequest {
///     name: "新项目".to_string(),
///     description: Some("项目描述".to_string()),
/// };
/// 
/// let project = create_project(request).await?;
/// println!("创建项目: {}", project.name);
/// ```
#[tracing::instrument(skip(db))]
pub async fn create_project(
    db: &Database,
    request: CreateProjectRequest,
) -> AppResult<Project> {
    // 实现
}
```

### 2. TypeScript/SolidJS 代码规范

#### 2.1 Biome 配置
```json
// biome.json - 代码格式化和检查配置
{
  "$schema": "https://biomejs.dev/schemas/1.9.0/schema.json",
  "organizeImports": {
    "enabled": true
  },
  "linter": {
    "enabled": true,
    "rules": {
      "recommended": true,
      "style": {
        "noNonNullAssertion": "error",
        "useConst": "error"
      },
      "suspicious": {
        "noExplicitAny": "error"
      }
    }
  },
  "formatter": {
    "enabled": true,
    "formatWithErrors": false,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineWidth": 100,
    "lineEnding": "lf"
  },
  "javascript": {
    "formatter": {
      "quoteStyle": "single",
      "trailingCommas": "es5",
      "semicolons": "always"
    }
  }
}
```

#### 2.2 命名规范
```typescript
// 组件命名：PascalCase
function ProjectCard(props: ProjectCardProps) {
  return <div>...</div>;
}

// 函数命名：camelCase
function createProject(data: ProjectData): Promise<Project> {
  return invoke('create_project', { data });
}

// 常量命名：SCREAMING_SNAKE_CASE
const MAX_RETRY_COUNT = 3;
const API_BASE_URL = 'http://localhost:1420';

// 类型命名：PascalCase
interface ProjectData {
  id: string;
  name: string;
  description?: string;
}

type ProjectStatus = 'not_started' | 'in_progress' | 'completed';

// 枚举命名：PascalCase
enum TaskPriority {
  Low = 'low',
  Medium = 'medium',
  High = 'high',
  Urgent = 'urgent',
}
```

#### 2.3 组件规范
```typescript
// 组件文件结构
import { Show, createSignal, type JSX } from 'solid-js';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

// 1. 类型定义
interface ProjectCardProps {
  project: Project;
  onEdit?: (id: string) => void;
  onDelete?: (id: string) => void;
  className?: string;
}

// 2. 组件实现
export function ProjectCard(props: ProjectCardProps) {
  // 3. 状态定义
  const [isLoading, setIsLoading] = createSignal(false);
  
  // 4. 事件处理函数
  const handleEdit = () => {
    props.onEdit?.(props.project.id);
  };
  
  // 5. 渲染逻辑
  return (
    <div class={cn('project-card', props.className)}>
      <h3>{props.project.name}</h3>
      <Show when={props.project.description}>
        <p>{props.project.description}</p>
      </Show>
      <Button onClick={handleEdit} disabled={isLoading()}>
        编辑
      </Button>
    </div>
  );
}
```

#### 2.4 类型安全规范
```typescript
// 使用严格的类型定义
interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

// 避免使用 any，使用具体类型
function processData<T>(data: T): T {
  // 处理逻辑
  return data;
}

// 使用类型守卫
function isProject(obj: unknown): obj is Project {
  return (
    typeof obj === 'object' &&
    obj !== null &&
    'id' in obj &&
    'name' in obj &&
    typeof (obj as Project).id === 'string' &&
    typeof (obj as Project).name === 'string'
  );
}

// 使用联合类型而不是字符串
type ProjectStatus = 'not_started' | 'in_progress' | 'completed';

// 使用 const assertions
const PROJECT_STATUSES = ['not_started', 'in_progress', 'completed'] as const;
```

## 第二部分：Git 工作流程

### 1. 分支策略

#### 1.1 分支命名规范
```bash
# 主分支
main                    # 生产环境分支
develop                 # 开发环境分支

# 功能分支
feature/user-auth       # 新功能开发
feature/project-mgmt    # 项目管理功能

# 修复分支
bugfix/login-error      # Bug 修复
hotfix/security-patch   # 紧急修复

# 发布分支
release/v1.0.0          # 版本发布准备
```

#### 1.2 工作流程
```bash
# 1. 从 develop 创建功能分支
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "feat: add new feature"

# 3. 推送分支
git push origin feature/new-feature

# 4. 创建 Pull Request
# 在 GitHub/GitLab 上创建 PR，目标分支为 develop

# 5. 代码审查和合并
# 审查通过后，使用 Squash and Merge

# 6. 清理分支
git checkout develop
git pull origin develop
git branch -d feature/new-feature
```

### 2. 提交信息规范

#### 2.1 Conventional Commits 格式
```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

#### 2.2 提交类型
```bash
feat:     # 新功能
fix:      # Bug 修复
docs:     # 文档更新
style:    # 代码格式化（不影响功能）
refactor: # 代码重构
perf:     # 性能优化
test:     # 测试相关
chore:    # 构建过程或辅助工具的变动
ci:       # CI/CD 相关
build:    # 构建系统或外部依赖的变动
```

#### 2.3 提交示例
```bash
# 功能开发
git commit -m "feat(auth): add user login functionality"

# Bug 修复
git commit -m "fix(ui): resolve button alignment issue in project card"

# 文档更新
git commit -m "docs: update API documentation for project endpoints"

# 重构
git commit -m "refactor(database): optimize query performance for large datasets"

# 破坏性变更
git commit -m "feat!: change API response format

BREAKING CHANGE: API responses now include metadata object"
```

### 3. Pull Request 规范

#### 3.1 PR 标题格式
```
[Type] Brief description of changes
```

#### 3.2 PR 描述模板
```markdown
## 变更类型
- [ ] 新功能
- [ ] Bug 修复
- [ ] 文档更新
- [ ] 代码重构
- [ ] 性能优化
- [ ] 其他

## 变更描述
简要描述本次变更的内容和目的。

## 相关 Issue
Closes #123

## 测试
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 手动测试已完成

## 检查清单
- [ ] 代码符合项目规范
- [ ] 已添加必要的测试
- [ ] 文档已更新
- [ ] 无破坏性变更（或已在描述中说明）

## 截图（如适用）
添加相关截图或 GIF 展示变更效果。

## 额外说明
其他需要说明的内容。
```

## 第三部分：代码审查规范

### 1. 审查检查点

#### 1.1 代码质量
- [ ] 代码逻辑清晰，易于理解
- [ ] 函数和变量命名有意义
- [ ] 代码复用性良好，避免重复
- [ ] 错误处理完善
- [ ] 性能考虑合理

#### 1.2 安全性
- [ ] 输入验证充分
- [ ] 敏感信息未硬编码
- [ ] SQL 注入防护
- [ ] XSS 防护
- [ ] 权限检查完整

#### 1.3 测试覆盖
- [ ] 关键功能有单元测试
- [ ] 边界条件有测试覆盖
- [ ] 错误场景有测试
- [ ] 测试用例有意义

### 2. 审查流程

#### 2.1 审查者职责
```markdown
1. **功能审查**
   - 验证功能是否符合需求
   - 检查边界条件处理
   - 确认错误处理逻辑

2. **代码质量审查**
   - 检查代码风格一致性
   - 评估代码可读性
   - 确认架构设计合理性

3. **安全审查**
   - 检查安全漏洞
   - 验证权限控制
   - 确认数据验证

4. **性能审查**
   - 评估性能影响
   - 检查资源使用
   - 确认扩展性
```

#### 2.2 反馈规范
```markdown
# 反馈分类
- **Must Fix**: 必须修复的问题
- **Should Fix**: 建议修复的问题  
- **Consider**: 可以考虑的改进
- **Nitpick**: 细节问题

# 反馈示例
**Must Fix**: 这里存在 SQL 注入风险，需要使用参数化查询。

**Should Fix**: 建议将这个函数拆分为更小的函数，提高可读性。

**Consider**: 可以考虑使用缓存来提高这个查询的性能。

**Nitpick**: 变量名 `data` 不够具体，建议改为 `projectData`。
```

## 第四部分：测试规范

### 1. 测试策略

#### 1.1 测试金字塔
```
    /\
   /  \     E2E Tests (少量)
  /____\    
 /      \   Integration Tests (适量)
/________\  Unit Tests (大量)
```

#### 1.2 测试类型
```rust
// 单元测试
#[cfg(test)]
mod tests {
    use super::*;
    
    #[test]
    fn test_create_project_success() {
        // 测试项目创建成功场景
    }
    
    #[test]
    fn test_create_project_validation_error() {
        // 测试输入验证失败场景
    }
}

// 集成测试
#[tokio::test]
async fn test_project_api_integration() {
    // 测试 API 集成
}
```

```typescript
// 前端单元测试
import { render, screen } from '@solidjs/testing-library';
import { ProjectCard } from './ProjectCard';

describe('ProjectCard', () => {
  it('should render project name', () => {
    const project = { id: '1', name: 'Test Project' };
    render(() => <ProjectCard project={project} />);
    
    expect(screen.getByText('Test Project')).toBeInTheDocument();
  });
});
```

### 2. 测试命名规范

#### 2.1 测试函数命名
```rust
// Rust 测试命名：test_<function>_<scenario>_<expected_result>
#[test]
fn test_create_project_with_valid_data_returns_success() {}

#[test]
fn test_create_project_with_empty_name_returns_validation_error() {}

#[test]
fn test_get_project_with_nonexistent_id_returns_not_found() {}
```

```typescript
// TypeScript 测试命名：should <expected behavior> when <condition>
describe('ProjectService', () => {
  it('should create project when valid data provided', () => {});
  
  it('should throw validation error when name is empty', () => {});
  
  it('should return not found when project does not exist', () => {});
});
```

## 第五部分：性能规范

### 1. 性能目标

#### 1.1 响应时间目标
- API 响应时间：< 500ms (95th percentile)
- 页面加载时间：< 2s (首次加载)
- 页面切换时间：< 200ms
- 数据库查询：< 100ms (简单查询)

#### 1.2 资源使用目标
- 内存使用：< 100MB (空闲状态)
- CPU 使用：< 10% (空闲状态)
- 磁盘 I/O：< 10MB/s (正常操作)

### 2. 性能最佳实践

#### 2.1 数据库优化
```rust
// 使用索引优化查询
sqlx::query!(
    "SELECT * FROM projects WHERE user_id = ? AND status = ?",
    user_id, status
).fetch_all(&db).await?;

// 批量操作
let mut tx = db.begin().await?;
for project in projects {
    sqlx::query!(
        "INSERT INTO projects (id, name) VALUES (?, ?)",
        project.id, project.name
    ).execute(&mut *tx).await?;
}
tx.commit().await?;
```

#### 2.2 前端优化
```typescript
// 使用虚拟化处理大列表
import { createVirtualizer } from '@tanstack/solid-virtual';

function VirtualList(props: { items: Item[] }) {
  const virtualizer = createVirtualizer({
    count: props.items.length,
    getScrollElement: () => containerRef(),
    estimateSize: () => 50,
  });
  
  return (
    <div ref={setContainerRef} style={{ height: '400px', overflow: 'auto' }}>
      {/* 虚拟化渲染 */}
    </div>
  );
}

// 使用 memo 优化重复计算
const expensiveValue = createMemo(() => {
  return heavyComputation(props.data);
});
```

## 总结

遵循这些开发规范将确保：

1. **代码质量**: 统一的代码风格和质量标准
2. **团队协作**: 清晰的工作流程和沟通规范
3. **项目维护**: 易于理解和维护的代码库
4. **性能保证**: 满足性能要求的应用程序

所有团队成员都应该熟悉并遵循这些规范，在有疑问时及时沟通讨论。
