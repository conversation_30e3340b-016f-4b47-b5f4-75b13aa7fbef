// 数据库基础设施层
// 实现具体的数据库操作和仓储接口

use crate::shared::errors::AppResult;
use sqlx::{SqlitePool, Row};

pub mod connection;
pub mod migration_manager;
pub mod migration_runner;
pub mod validators;
pub mod repositories;
pub mod models;
pub mod mappers;

// 重新导出
pub use connection::*;
pub use migration_manager::*;
pub use migration_runner::*;
pub use validators::*;
pub use repositories::*;
pub use models::*;
pub use mappers::*;



/// 数据库工具函数
pub struct DatabaseUtils;

impl DatabaseUtils {
    /// 构建WHERE子句
    pub fn build_where_clause(conditions: Vec<String>) -> String {
        if conditions.is_empty() {
            String::new()
        } else {
            format!(" WHERE {}", conditions.join(" AND "))
        }
    }

    /// 构建ORDER BY子句
    pub fn build_order_clause(sorts: &[crate::shared::types::Sort]) -> String {
        if sorts.is_empty() {
            String::new()
        } else {
            let order_parts: Vec<String> = sorts.iter()
                .map(|sort| {
                    let direction = match sort.direction {
                        crate::shared::types::SortDirection::Asc => "ASC",
                        crate::shared::types::SortDirection::Desc => "DESC",
                    };
                    format!("{} {}", sort.field, direction)
                })
                .collect();
            format!(" ORDER BY {}", order_parts.join(", "))
        }
    }

    /// 构建LIMIT和OFFSET子句
    pub fn build_limit_clause(pagination: &Option<crate::shared::types::Pagination>) -> String {
        if let Some(pagination) = pagination {
            format!(" LIMIT {} OFFSET {}", pagination.limit(), pagination.offset())
        } else {
            String::new()
        }
    }

    /// 构建完整的查询语句
    pub fn build_query(
        base_query: &str,
        conditions: Vec<String>,
        sorts: &[crate::shared::types::Sort],
        pagination: &Option<crate::shared::types::Pagination>,
    ) -> String {
        format!(
            "{}{}{}{}",
            base_query,
            Self::build_where_clause(conditions),
            Self::build_order_clause(sorts),
            Self::build_limit_clause(pagination)
        )
    }

    /// 转换过滤条件为SQL条件
    pub fn filter_to_sql_condition(filter: &crate::shared::types::Filter) -> AppResult<String> {
        use crate::shared::types::{FilterOperator, FilterValue};
        
        let condition = match filter.operator {
            FilterOperator::Eq => match &filter.value {
                FilterValue::String(s) => format!("{} = '{}'", filter.field, s),
                FilterValue::Number(n) => format!("{} = {}", filter.field, n),
                FilterValue::Boolean(b) => format!("{} = {}", filter.field, if *b { 1 } else { 0 }),
                FilterValue::Null => format!("{} IS NULL", filter.field),
                FilterValue::Array(_) => return Err(crate::shared::errors::AppError::validation("Array values not supported for equality operator")),
            },
            FilterOperator::Ne => match &filter.value {
                FilterValue::String(s) => format!("{} != '{}'", filter.field, s),
                FilterValue::Number(n) => format!("{} != {}", filter.field, n),
                FilterValue::Boolean(b) => format!("{} != {}", filter.field, if *b { 1 } else { 0 }),
                FilterValue::Null => format!("{} IS NOT NULL", filter.field),
                FilterValue::Array(_) => return Err(crate::shared::errors::AppError::validation("Array values not supported for inequality operator")),
            },
            FilterOperator::Gt => match &filter.value {
                FilterValue::Number(n) => format!("{} > {}", filter.field, n),
                FilterValue::String(s) => format!("{} > '{}'", filter.field, s),
                _ => return Err(crate::shared::errors::AppError::validation("Greater than operator only supports numbers and strings")),
            },
            FilterOperator::Gte => match &filter.value {
                FilterValue::Number(n) => format!("{} >= {}", filter.field, n),
                FilterValue::String(s) => format!("{} >= '{}'", filter.field, s),
                _ => return Err(crate::shared::errors::AppError::validation("Greater than or equal operator only supports numbers and strings")),
            },
            FilterOperator::Lt => match &filter.value {
                FilterValue::Number(n) => format!("{} < {}", filter.field, n),
                FilterValue::String(s) => format!("{} < '{}'", filter.field, s),
                _ => return Err(crate::shared::errors::AppError::validation("Less than operator only supports numbers and strings")),
            },
            FilterOperator::Lte => match &filter.value {
                FilterValue::Number(n) => format!("{} <= {}", filter.field, n),
                FilterValue::String(s) => format!("{} <= '{}'", filter.field, s),
                _ => return Err(crate::shared::errors::AppError::validation("Less than or equal operator only supports numbers and strings")),
            },
            FilterOperator::Like => match &filter.value {
                FilterValue::String(s) => format!("{} LIKE '%{}%'", filter.field, s),
                _ => return Err(crate::shared::errors::AppError::validation("Like operator only supports strings")),
            },
            FilterOperator::In => match &filter.value {
                FilterValue::Array(arr) => {
                    let values = arr.iter()
                        .map(|v| format!("'{}'", v))
                        .collect::<Vec<_>>()
                        .join(", ");
                    format!("{} IN ({})", filter.field, values)
                },
                _ => return Err(crate::shared::errors::AppError::validation("In operator only supports arrays")),
            },
            FilterOperator::NotIn => match &filter.value {
                FilterValue::Array(arr) => {
                    let values = arr.iter()
                        .map(|v| format!("'{}'", v))
                        .collect::<Vec<_>>()
                        .join(", ");
                    format!("{} NOT IN ({})", filter.field, values)
                },
                _ => return Err(crate::shared::errors::AppError::validation("Not in operator only supports arrays")),
            },
            FilterOperator::IsNull => format!("{} IS NULL", filter.field),
            FilterOperator::NotNull => format!("{} IS NOT NULL", filter.field),
        };

        Ok(condition)
    }

    /// 转换查询参数为SQL条件
    pub fn query_params_to_sql_conditions(params: &crate::shared::types::QueryParams) -> AppResult<Vec<String>> {
        let mut conditions = Vec::new();

        if let Some(filters) = &params.filters {
            for filter in filters {
                conditions.push(Self::filter_to_sql_condition(filter)?);
            }
        }

        Ok(conditions)
    }

    /// 安全地转义SQL字符串
    pub fn escape_sql_string(input: &str) -> String {
        input.replace("'", "''")
    }

    /// 验证字段名是否安全（防止SQL注入）
    pub fn validate_field_name(field_name: &str) -> AppResult<()> {
        // 只允许字母、数字、下划线和点号
        if !field_name.chars().all(|c| c.is_alphanumeric() || c == '_' || c == '.') {
            return Err(crate::shared::errors::AppError::validation("Invalid field name"));
        }
        Ok(())
    }

    /// 构建搜索条件
    pub fn build_search_condition(keyword: &str, fields: &[&str]) -> String {
        if keyword.is_empty() || fields.is_empty() {
            return String::new();
        }

        let escaped_keyword = Self::escape_sql_string(keyword);
        let conditions: Vec<String> = fields.iter()
            .map(|field| format!("{} LIKE '%{}%'", field, escaped_keyword))
            .collect();

        format!("({})", conditions.join(" OR "))
    }
}

/// 数据库错误处理宏
#[macro_export]
macro_rules! handle_db_error {
    ($result:expr) => {
        $result.map_err(|e| crate::shared::errors::AppError::database(e.to_string()))
    };
}

/// 数据库事务宏
#[macro_export]
macro_rules! with_transaction {
    ($pool:expr, $block:block) => {{
        let mut tx = $pool.begin().await
            .map_err(|e| crate::shared::errors::AppError::database(format!("Failed to begin transaction: {}", e)))?;
        
        let result = async move $block.await;
        
        match result {
            Ok(value) => {
                tx.commit().await
                    .map_err(|e| crate::shared::errors::AppError::database(format!("Failed to commit transaction: {}", e)))?;
                Ok(value)
            }
            Err(e) => {
                let _ = tx.rollback().await;
                Err(e)
            }
        }
    }};
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_build_where_clause() {
        let conditions = vec!["name = 'test'".to_string(), "active = 1".to_string()];
        let result = DatabaseUtils::build_where_clause(conditions);
        assert_eq!(result, " WHERE name = 'test' AND active = 1");
    }

    #[test]
    fn test_build_where_clause_empty() {
        let conditions = vec![];
        let result = DatabaseUtils::build_where_clause(conditions);
        assert_eq!(result, "");
    }

    #[test]
    fn test_escape_sql_string() {
        let input = "test'string";
        let result = DatabaseUtils::escape_sql_string(input);
        assert_eq!(result, "test''string");
    }

    #[test]
    fn test_validate_field_name() {
        assert!(DatabaseUtils::validate_field_name("valid_field").is_ok());
        assert!(DatabaseUtils::validate_field_name("table.field").is_ok());
        assert!(DatabaseUtils::validate_field_name("field123").is_ok());
        assert!(DatabaseUtils::validate_field_name("invalid-field").is_err());
        assert!(DatabaseUtils::validate_field_name("field; DROP TABLE").is_err());
    }

    #[test]
    fn test_build_search_condition() {
        let keyword = "test";
        let fields = &["name", "description"];
        let result = DatabaseUtils::build_search_condition(keyword, fields);
        assert_eq!(result, "(name LIKE '%test%' OR description LIKE '%test%')");
    }
}
