// 用户数据模型
// 对应数据库中的用户表结构

use sqlx::FromRow;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc};

/// 用户数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserModel {
    pub id: String,
    pub username: String,
    pub email: Option<String>,
    pub password_hash: String,
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    pub timezone: String,
    pub language: String,
    pub is_active: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login_at: Option<DateTime<Utc>>,
}

/// 创建用户数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateUserModel {
    pub id: String,
    pub username: String,
    pub email: Option<String>,
    pub password_hash: String,
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    pub timezone: String,
    pub language: String,
    pub is_active: bool,
}

/// 更新用户数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateUserModel {
    pub email: Option<String>,
    pub password_hash: Option<String>,
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    pub timezone: Option<String>,
    pub language: Option<String>,
    pub is_active: Option<bool>,
    pub last_login_at: Option<DateTime<Utc>>,
}

/// 用户查询模型
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct UserQueryModel {
    pub username: Option<String>,
    pub email: Option<String>,
    pub is_active: Option<bool>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
}

/// 用户统计模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserStatsModel {
    pub total_users: i64,
    pub active_users: i64,
    pub inactive_users: i64,
    pub users_created_today: i64,
    pub users_created_this_week: i64,
    pub users_created_this_month: i64,
}

impl UserModel {
    /// 创建新的用户模型
    pub fn new(
        id: String,
        username: String,
        email: Option<String>,
        password_hash: String,
        full_name: Option<String>,
        timezone: String,
        language: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            username,
            email,
            password_hash,
            full_name,
            avatar_url: None,
            timezone,
            language,
            is_active: true,
            created_at: now,
            updated_at: now,
            last_login_at: None,
        }
    }

    /// 检查用户是否激活
    pub fn is_active(&self) -> bool {
        self.is_active
    }

    /// 获取显示名称
    pub fn display_name(&self) -> &str {
        self.full_name.as_deref().unwrap_or(&self.username)
    }

    /// 检查是否有邮箱
    pub fn has_email(&self) -> bool {
        self.email.is_some()
    }

    /// 检查是否最近登录过
    pub fn has_recent_login(&self, days: i64) -> bool {
        if let Some(last_login) = self.last_login_at {
            let threshold = Utc::now() - chrono::Duration::days(days);
            last_login > threshold
        } else {
            false
        }
    }

    /// 获取账户年龄（天数）
    pub fn account_age_days(&self) -> i64 {
        (Utc::now() - self.created_at).num_days()
    }
}

impl CreateUserModel {
    /// 创建新的创建用户模型
    pub fn new(
        id: String,
        username: String,
        email: Option<String>,
        password_hash: String,
        full_name: Option<String>,
        timezone: Option<String>,
        language: Option<String>,
    ) -> Self {
        Self {
            id,
            username,
            email,
            password_hash,
            full_name,
            avatar_url: None,
            timezone: timezone.unwrap_or_else(|| "UTC".to_string()),
            language: language.unwrap_or_else(|| "en".to_string()),
            is_active: true,
        }
    }
}

impl UpdateUserModel {
    /// 创建空的更新模型
    pub fn new() -> Self {
        Self {
            email: None,
            password_hash: None,
            full_name: None,
            avatar_url: None,
            timezone: None,
            language: None,
            is_active: None,
            last_login_at: None,
        }
    }

    /// 设置邮箱
    pub fn with_email(mut self, email: Option<String>) -> Self {
        self.email = email;
        self
    }

    /// 设置密码哈希
    pub fn with_password_hash(mut self, password_hash: String) -> Self {
        self.password_hash = Some(password_hash);
        self
    }

    /// 设置全名
    pub fn with_full_name(mut self, full_name: Option<String>) -> Self {
        self.full_name = full_name;
        self
    }

    /// 设置头像URL
    pub fn with_avatar_url(mut self, avatar_url: Option<String>) -> Self {
        self.avatar_url = avatar_url;
        self
    }

    /// 设置时区
    pub fn with_timezone(mut self, timezone: String) -> Self {
        self.timezone = Some(timezone);
        self
    }

    /// 设置语言
    pub fn with_language(mut self, language: String) -> Self {
        self.language = Some(language);
        self
    }

    /// 设置激活状态
    pub fn with_active_status(mut self, is_active: bool) -> Self {
        self.is_active = Some(is_active);
        self
    }

    /// 设置最后登录时间
    pub fn with_last_login(mut self, last_login_at: DateTime<Utc>) -> Self {
        self.last_login_at = Some(last_login_at);
        self
    }

    /// 检查是否有更新
    pub fn has_updates(&self) -> bool {
        self.email.is_some()
            || self.password_hash.is_some()
            || self.full_name.is_some()
            || self.avatar_url.is_some()
            || self.timezone.is_some()
            || self.language.is_some()
            || self.is_active.is_some()
            || self.last_login_at.is_some()
    }
}

impl UserQueryModel {
    /// 创建新的查询模型
    pub fn new() -> Self {
        Self::default()
    }

    /// 按用户名查询
    pub fn by_username(mut self, username: String) -> Self {
        self.username = Some(username);
        self
    }

    /// 按邮箱查询
    pub fn by_email(mut self, email: String) -> Self {
        self.email = Some(email);
        self
    }

    /// 按激活状态查询
    pub fn by_active_status(mut self, is_active: bool) -> Self {
        self.is_active = Some(is_active);
        self
    }

    /// 按创建时间范围查询
    pub fn by_created_range(mut self, after: Option<DateTime<Utc>>, before: Option<DateTime<Utc>>) -> Self {
        self.created_after = after;
        self.created_before = before;
        self
    }

    /// 检查是否有查询条件
    pub fn has_conditions(&self) -> bool {
        self.username.is_some()
            || self.email.is_some()
            || self.is_active.is_some()
            || self.created_after.is_some()
            || self.created_before.is_some()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_model_creation() {
        let user = UserModel::new(
            "user123".to_string(),
            "testuser".to_string(),
            Some("<EMAIL>".to_string()),
            "password_hash".to_string(),
            Some("Test User".to_string()),
            "UTC".to_string(),
            "en".to_string(),
        );

        assert_eq!(user.id, "user123");
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, Some("<EMAIL>".to_string()));
        assert!(user.is_active());
        assert_eq!(user.display_name(), "Test User");
        assert!(user.has_email());
    }

    #[test]
    fn test_create_user_model() {
        let create_user = CreateUserModel::new(
            "user123".to_string(),
            "testuser".to_string(),
            Some("<EMAIL>".to_string()),
            "password_hash".to_string(),
            Some("Test User".to_string()),
            Some("Asia/Shanghai".to_string()),
            Some("zh".to_string()),
        );

        assert_eq!(create_user.timezone, "Asia/Shanghai");
        assert_eq!(create_user.language, "zh");
        assert!(create_user.is_active);
    }

    #[test]
    fn test_update_user_model() {
        let update_user = UpdateUserModel::new()
            .with_email(Some("<EMAIL>".to_string()))
            .with_full_name(Some("New Name".to_string()))
            .with_active_status(false);

        assert!(update_user.has_updates());
        assert_eq!(update_user.email, Some("<EMAIL>".to_string()));
        assert_eq!(update_user.full_name, Some("New Name".to_string()));
        assert_eq!(update_user.is_active, Some(false));
    }

    #[test]
    fn test_user_query_model() {
        let query = UserQueryModel::new()
            .by_username("testuser".to_string())
            .by_active_status(true);

        assert!(query.has_conditions());
        assert_eq!(query.username, Some("testuser".to_string()));
        assert_eq!(query.is_active, Some(true));
    }
}
