// API中间件
// 实现请求预处理、认证、日志记录等中间件功能

use crate::application::services::AuthService;
use crate::shared::errors::{AppError, AppResult};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::collections::HashMap;
use uuid::Uuid;

/// 请求上下文
#[derive(Debug, Clone)]
pub struct RequestContext {
    pub request_id: String,
    pub user_id: Option<String>,
    pub start_time: chrono::DateTime<chrono::Utc>,
    pub metadata: HashMap<String, String>,
}

impl RequestContext {
    pub fn new() -> Self {
        Self {
            request_id: Uuid::new_v4().to_string(),
            user_id: None,
            start_time: chrono::Utc::now(),
            metadata: HashMap::new(),
        }
    }

    pub fn with_user(mut self, user_id: String) -> Self {
        self.user_id = Some(user_id);
        self
    }

    pub fn with_metadata(mut self, key: String, value: String) -> Self {
        self.metadata.insert(key, value);
        self
    }

    pub fn elapsed(&self) -> chrono::Duration {
        chrono::Utc::now() - self.start_time
    }
}

/// 认证中间件
pub struct AuthMiddleware {
    auth_service: Arc<AuthService>,
}

impl AuthMiddleware {
    pub fn new(auth_service: Arc<AuthService>) -> Self {
        Self { auth_service }
    }

    /// 验证访问令牌
    pub async fn authenticate(&self, token: &str) -> AppResult<RequestContext> {
        match self.auth_service.verify_access_token(token).await {
            Ok(user) => {
                let context = RequestContext::new()
                    .with_user(user.id().clone())
                    .with_metadata("username".to_string(), user.username().clone());
                
                tracing::debug!(
                    request_id = %context.request_id,
                    user_id = %user.id(),
                    "User authenticated successfully"
                );
                
                Ok(context)
            }
            Err(e) => {
                tracing::warn!("Authentication failed: {}", e);
                Err(AppError::unauthorized("认证失败"))
            }
        }
    }

    /// 可选认证（允许匿名访问）
    pub async fn authenticate_optional(&self, token: Option<&str>) -> RequestContext {
        match token {
            Some(token) => {
                match self.authenticate(token).await {
                    Ok(context) => context,
                    Err(_) => RequestContext::new(),
                }
            }
            None => RequestContext::new(),
        }
    }

    /// 检查用户权限
    pub async fn check_permission(
        &self,
        context: &RequestContext,
        resource: &str,
        action: &str,
    ) -> AppResult<()> {
        // 简单的权限检查实现
        // 在实际应用中，这里应该查询用户角色和权限
        
        if context.user_id.is_none() {
            return Err(AppError::unauthorized("需要登录"));
        }

        // 这里可以实现更复杂的权限逻辑
        // 例如：基于角色的访问控制(RBAC)
        
        tracing::debug!(
            request_id = %context.request_id,
            user_id = ?context.user_id,
            resource = %resource,
            action = %action,
            "Permission check passed"
        );

        Ok(())
    }
}

/// 日志中间件
pub struct LoggingMiddleware;

impl LoggingMiddleware {
    /// 记录请求开始
    pub fn log_request_start(context: &RequestContext, command: &str, params: &serde_json::Value) {
        tracing::info!(
            request_id = %context.request_id,
            user_id = ?context.user_id,
            command = %command,
            params = %params,
            "API request started"
        );
    }

    /// 记录请求完成
    pub fn log_request_success(
        context: &RequestContext,
        command: &str,
        duration_ms: i64,
    ) {
        tracing::info!(
            request_id = %context.request_id,
            user_id = ?context.user_id,
            command = %command,
            duration_ms = %duration_ms,
            "API request completed successfully"
        );
    }

    /// 记录请求错误
    pub fn log_request_error(
        context: &RequestContext,
        command: &str,
        error: &AppError,
        duration_ms: i64,
    ) {
        tracing::error!(
            request_id = %context.request_id,
            user_id = ?context.user_id,
            command = %command,
            error = %error,
            duration_ms = %duration_ms,
            "API request failed"
        );
    }

    /// 记录性能指标
    pub fn log_performance_metrics(
        context: &RequestContext,
        command: &str,
        duration_ms: i64,
        memory_usage: Option<u64>,
    ) {
        if duration_ms > 1000 {
            tracing::warn!(
                request_id = %context.request_id,
                command = %command,
                duration_ms = %duration_ms,
                memory_usage = ?memory_usage,
                "Slow API request detected"
            );
        } else {
            tracing::debug!(
                request_id = %context.request_id,
                command = %command,
                duration_ms = %duration_ms,
                memory_usage = ?memory_usage,
                "API request performance metrics"
            );
        }
    }
}

/// 限流中间件
pub struct RateLimitMiddleware {
    requests: Arc<std::sync::RwLock<HashMap<String, RateLimitState>>>,
    max_requests: u32,
    window_seconds: u64,
}

#[derive(Debug, Clone)]
struct RateLimitState {
    count: u32,
    window_start: chrono::DateTime<chrono::Utc>,
}

impl RateLimitMiddleware {
    pub fn new(max_requests: u32, window_seconds: u64) -> Self {
        Self {
            requests: Arc::new(std::sync::RwLock::new(HashMap::new())),
            max_requests,
            window_seconds,
        }
    }

    /// 检查限流
    pub fn check_rate_limit(&self, user_id: &str) -> AppResult<()> {
        let now = chrono::Utc::now();
        let mut requests = self.requests.write().unwrap();

        let state = requests.entry(user_id.to_string()).or_insert(RateLimitState {
            count: 0,
            window_start: now,
        });

        // 检查是否需要重置窗口
        if (now - state.window_start).num_seconds() >= self.window_seconds as i64 {
            state.count = 0;
            state.window_start = now;
        }

        // 检查是否超过限制
        if state.count >= self.max_requests {
            tracing::warn!(
                user_id = %user_id,
                count = %state.count,
                max_requests = %self.max_requests,
                "Rate limit exceeded"
            );
            return Err(AppError::validation("请求过于频繁，请稍后再试"));
        }

        state.count += 1;
        Ok(())
    }

    /// 清理过期的限流记录
    pub fn cleanup_expired(&self) {
        let now = chrono::Utc::now();
        let mut requests = self.requests.write().unwrap();
        
        requests.retain(|_, state| {
            (now - state.window_start).num_seconds() < self.window_seconds as i64
        });
    }

    /// 获取限流统计
    pub fn get_stats(&self) -> RateLimitStats {
        let requests = self.requests.read().unwrap();
        
        RateLimitStats {
            total_users: requests.len(),
            active_limits: requests.values().filter(|s| s.count > 0).count(),
            max_requests: self.max_requests,
            window_seconds: self.window_seconds,
        }
    }
}

/// 限流统计
#[derive(Debug, Clone, Serialize)]
pub struct RateLimitStats {
    pub total_users: usize,
    pub active_limits: usize,
    pub max_requests: u32,
    pub window_seconds: u64,
}

/// 验证中间件
pub struct ValidationMiddleware;

impl ValidationMiddleware {
    /// 验证请求参数
    pub fn validate_params(params: &serde_json::Value) -> AppResult<()> {
        // 基本的参数验证
        if params.is_null() {
            return Err(AppError::validation("请求参数不能为空"));
        }

        // 检查参数大小
        let params_str = params.to_string();
        if params_str.len() > 1024 * 1024 {
            return Err(AppError::validation("请求参数过大"));
        }

        Ok(())
    }

    /// 验证字符串参数
    pub fn validate_string_param(
        value: &str,
        field_name: &str,
        min_length: Option<usize>,
        max_length: Option<usize>,
    ) -> AppResult<()> {
        if let Some(min) = min_length {
            if value.len() < min {
                return Err(AppError::validation(&format!(
                    "{} 至少需要 {} 个字符",
                    field_name, min
                )));
            }
        }

        if let Some(max) = max_length {
            if value.len() > max {
                return Err(AppError::validation(&format!(
                    "{} 不能超过 {} 个字符",
                    field_name, max
                )));
            }
        }

        Ok(())
    }

    /// 验证ID参数
    pub fn validate_id(id: &str, field_name: &str) -> AppResult<()> {
        if id.is_empty() {
            return Err(AppError::validation(&format!("{} 不能为空", field_name)));
        }

        // 简单的UUID格式验证
        if id.len() != 36 || id.chars().filter(|&c| c == '-').count() != 4 {
            return Err(AppError::validation(&format!("{} 格式不正确", field_name)));
        }

        Ok(())
    }
}

/// 缓存中间件
pub struct CacheMiddleware {
    cache: Arc<std::sync::RwLock<HashMap<String, CacheEntry>>>,
    default_ttl: chrono::Duration,
}

#[derive(Debug, Clone)]
struct CacheEntry {
    data: serde_json::Value,
    expires_at: chrono::DateTime<chrono::Utc>,
}

impl CacheMiddleware {
    pub fn new(default_ttl_seconds: i64) -> Self {
        Self {
            cache: Arc::new(std::sync::RwLock::new(HashMap::new())),
            default_ttl: chrono::Duration::seconds(default_ttl_seconds),
        }
    }

    /// 获取缓存
    pub fn get(&self, key: &str) -> Option<serde_json::Value> {
        let cache = self.cache.read().unwrap();
        
        if let Some(entry) = cache.get(key) {
            if chrono::Utc::now() < entry.expires_at {
                return Some(entry.data.clone());
            }
        }

        None
    }

    /// 设置缓存
    pub fn set(&self, key: String, data: serde_json::Value, ttl: Option<chrono::Duration>) {
        let ttl = ttl.unwrap_or(self.default_ttl);
        let expires_at = chrono::Utc::now() + ttl;
        
        let entry = CacheEntry { data, expires_at };
        
        let mut cache = self.cache.write().unwrap();
        cache.insert(key, entry);
    }

    /// 删除缓存
    pub fn remove(&self, key: &str) {
        let mut cache = self.cache.write().unwrap();
        cache.remove(key);
    }

    /// 清理过期缓存
    pub fn cleanup_expired(&self) {
        let now = chrono::Utc::now();
        let mut cache = self.cache.write().unwrap();
        
        cache.retain(|_, entry| now < entry.expires_at);
    }

    /// 获取缓存统计
    pub fn get_stats(&self) -> CacheStats {
        let cache = self.cache.read().unwrap();
        let now = chrono::Utc::now();
        
        let total_entries = cache.len();
        let expired_entries = cache.values()
            .filter(|entry| now >= entry.expires_at)
            .count();
        
        CacheStats {
            total_entries,
            active_entries: total_entries - expired_entries,
            expired_entries,
        }
    }
}

/// 缓存统计
#[derive(Debug, Clone, Serialize)]
pub struct CacheStats {
    pub total_entries: usize,
    pub active_entries: usize,
    pub expired_entries: usize,
}

/// 中间件管理器
pub struct MiddlewareManager {
    pub auth: AuthMiddleware,
    pub logging: LoggingMiddleware,
    pub rate_limit: RateLimitMiddleware,
    pub validation: ValidationMiddleware,
    pub cache: CacheMiddleware,
}

impl MiddlewareManager {
    pub fn new(auth_service: Arc<AuthService>) -> Self {
        Self {
            auth: AuthMiddleware::new(auth_service),
            logging: LoggingMiddleware,
            rate_limit: RateLimitMiddleware::new(100, 60), // 100 requests per minute
            validation: ValidationMiddleware,
            cache: CacheMiddleware::new(300), // 5 minutes default TTL
        }
    }

    /// 执行完整的中间件链
    pub async fn process_request(
        &self,
        command: &str,
        params: &serde_json::Value,
        token: Option<&str>,
        require_auth: bool,
    ) -> AppResult<RequestContext> {
        // 1. 验证参数
        ValidationMiddleware::validate_params(params)?;

        // 2. 认证
        let context = if require_auth {
            if let Some(token) = token {
                self.auth.authenticate(token).await?
            } else {
                return Err(AppError::unauthorized("需要提供访问令牌"));
            }
        } else {
            self.auth.authenticate_optional(token).await
        };

        // 3. 限流检查
        if let Some(user_id) = &context.user_id {
            self.rate_limit.check_rate_limit(user_id)?;
        }

        // 4. 记录请求开始
        LoggingMiddleware::log_request_start(&context, command, params);

        Ok(context)
    }

    /// 定期清理任务
    pub fn cleanup(&self) {
        self.rate_limit.cleanup_expired();
        self.cache.cleanup_expired();
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_request_context() {
        let context = RequestContext::new()
            .with_user("user123".to_string())
            .with_metadata("key".to_string(), "value".to_string());
        
        assert_eq!(context.user_id, Some("user123".to_string()));
        assert_eq!(context.metadata.get("key"), Some(&"value".to_string()));
    }

    #[test]
    fn test_rate_limit_middleware() {
        let middleware = RateLimitMiddleware::new(2, 60);
        let user_id = "test_user";
        
        // 前两次请求应该成功
        assert!(middleware.check_rate_limit(user_id).is_ok());
        assert!(middleware.check_rate_limit(user_id).is_ok());
        
        // 第三次请求应该被限制
        assert!(middleware.check_rate_limit(user_id).is_err());
    }

    #[test]
    fn test_validation_middleware() {
        // 有效参数
        let valid_params = serde_json::json!({"key": "value"});
        assert!(ValidationMiddleware::validate_params(&valid_params).is_ok());
        
        // 空参数
        let null_params = serde_json::Value::Null;
        assert!(ValidationMiddleware::validate_params(&null_params).is_err());
        
        // 字符串验证
        assert!(ValidationMiddleware::validate_string_param("test", "field", Some(3), Some(10)).is_ok());
        assert!(ValidationMiddleware::validate_string_param("ab", "field", Some(3), Some(10)).is_err());
    }

    #[test]
    fn test_cache_middleware() {
        let cache = CacheMiddleware::new(60);
        let key = "test_key";
        let data = serde_json::json!({"test": "data"});
        
        // 设置缓存
        cache.set(key.to_string(), data.clone(), None);
        
        // 获取缓存
        assert_eq!(cache.get(key), Some(data));
        
        // 删除缓存
        cache.remove(key);
        assert_eq!(cache.get(key), None);
    }
}
