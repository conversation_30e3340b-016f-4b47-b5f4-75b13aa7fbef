// 应用服务
// 实现具体的业务用例，协调领域对象和仓储

pub mod user_service;
pub mod project_service;
pub mod task_service;
pub mod area_service;
pub mod auth_service;
pub mod analytics_service;
pub mod service_container;

// 重新导出
pub use user_service::*;
pub use project_service::*;
pub use task_service::*;
pub use area_service::*;
pub use auth_service::*;
pub use analytics_service::*;
pub use service_container::*;

use crate::shared::errors::AppResult;
use std::sync::Arc;

/// 服务基础trait
pub trait Service: Send + Sync {
    /// 服务名称
    fn name(&self) -> &'static str;
    
    /// 健康检查
    async fn health_check(&self) -> AppResult<()>;
}

/// 服务配置
#[derive(Debug, Clone)]
pub struct ServiceConfig {
    pub enable_caching: bool,
    pub cache_ttl_seconds: u64,
    pub enable_metrics: bool,
    pub enable_audit_log: bool,
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self {
            enable_caching: true,
            cache_ttl_seconds: 300, // 5分钟
            enable_metrics: true,
            enable_audit_log: true,
        }
    }
}

/// 服务上下文
#[derive(Debug, Clone)]
pub struct ServiceContext {
    pub user_id: Option<String>,
    pub request_id: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub client_info: Option<ClientInfo>,
}

/// 客户端信息
#[derive(Debug, Clone)]
pub struct ClientInfo {
    pub user_agent: Option<String>,
    pub ip_address: Option<String>,
    pub platform: Option<String>,
}

impl ServiceContext {
    /// 创建新的服务上下文
    pub fn new(user_id: Option<String>) -> Self {
        Self {
            user_id,
            request_id: crate::shared::utils::generate_id(),
            timestamp: chrono::Utc::now(),
            client_info: None,
        }
    }

    /// 设置客户端信息
    pub fn with_client_info(mut self, client_info: ClientInfo) -> Self {
        self.client_info = Some(client_info);
        self
    }

    /// 检查是否已认证
    pub fn is_authenticated(&self) -> bool {
        self.user_id.is_some()
    }

    /// 获取用户ID
    pub fn user_id(&self) -> AppResult<&String> {
        self.user_id.as_ref().ok_or_else(|| {
            crate::shared::errors::AppError::unauthorized("用户未认证")
        })
    }
}

/// 服务结果包装器
#[derive(Debug, Clone)]
pub struct ServiceResult<T> {
    pub data: T,
    pub metadata: ServiceMetadata,
}

/// 服务元数据
#[derive(Debug, Clone)]
pub struct ServiceMetadata {
    pub execution_time_ms: u64,
    pub cache_hit: bool,
    pub affected_entities: Vec<String>,
    pub warnings: Vec<String>,
}

impl<T> ServiceResult<T> {
    /// 创建新的服务结果
    pub fn new(data: T) -> Self {
        Self {
            data,
            metadata: ServiceMetadata {
                execution_time_ms: 0,
                cache_hit: false,
                affected_entities: Vec::new(),
                warnings: Vec::new(),
            },
        }
    }

    /// 设置执行时间
    pub fn with_execution_time(mut self, execution_time_ms: u64) -> Self {
        self.metadata.execution_time_ms = execution_time_ms;
        self
    }

    /// 设置缓存命中
    pub fn with_cache_hit(mut self, cache_hit: bool) -> Self {
        self.metadata.cache_hit = cache_hit;
        self
    }

    /// 添加受影响的实体
    pub fn with_affected_entity(mut self, entity_id: String) -> Self {
        self.metadata.affected_entities.push(entity_id);
        self
    }

    /// 添加警告
    pub fn with_warning(mut self, warning: String) -> Self {
        self.metadata.warnings.push(warning);
        self
    }

    /// 解包数据
    pub fn into_data(self) -> T {
        self.data
    }
}

/// 分页结果
#[derive(Debug, Clone)]
pub struct PagedResult<T> {
    pub items: Vec<T>,
    pub total_count: u64,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_previous: bool,
}

impl<T> PagedResult<T> {
    /// 创建新的分页结果
    pub fn new(items: Vec<T>, total_count: u64, page: u32, page_size: u32) -> Self {
        let total_pages = if page_size > 0 {
            ((total_count as f64) / (page_size as f64)).ceil() as u32
        } else {
            0
        };

        Self {
            items,
            total_count,
            page,
            page_size,
            total_pages,
            has_next: page < total_pages,
            has_previous: page > 1,
        }
    }

    /// 检查是否为空
    pub fn is_empty(&self) -> bool {
        self.items.is_empty()
    }

    /// 获取项目数量
    pub fn len(&self) -> usize {
        self.items.len()
    }
}

/// 服务工具函数
pub struct ServiceUtils;

impl ServiceUtils {
    /// 测量执行时间
    pub async fn measure_time<F, T>(operation: F) -> (T, u64)
    where
        F: std::future::Future<Output = T>,
    {
        let start = std::time::Instant::now();
        let result = operation.await;
        let duration = start.elapsed().as_millis() as u64;
        (result, duration)
    }

    /// 验证权限
    pub fn check_permission(
        context: &ServiceContext,
        required_user_id: &str,
    ) -> AppResult<()> {
        let current_user_id = context.user_id()?;
        if current_user_id != required_user_id {
            return Err(crate::shared::errors::AppError::forbidden("权限不足"));
        }
        Ok(())
    }

    /// 记录审计日志
    pub fn log_audit_event(
        context: &ServiceContext,
        action: &str,
        entity_type: &str,
        entity_id: &str,
        details: Option<&str>,
    ) {
        tracing::info!(
            user_id = ?context.user_id,
            request_id = %context.request_id,
            action = %action,
            entity_type = %entity_type,
            entity_id = %entity_id,
            details = ?details,
            "Audit event"
        );
    }

    /// 记录性能指标
    pub fn log_performance_metric(
        service_name: &str,
        operation: &str,
        execution_time_ms: u64,
        success: bool,
    ) {
        tracing::debug!(
            service = %service_name,
            operation = %operation,
            execution_time_ms = %execution_time_ms,
            success = %success,
            "Performance metric"
        );
    }
}

/// 服务错误处理宏
#[macro_export]
macro_rules! handle_service_error {
    ($result:expr, $context:expr, $operation:expr) => {
        match $result {
            Ok(value) => {
                ServiceUtils::log_audit_event(
                    $context,
                    $operation,
                    "unknown",
                    "unknown",
                    Some("success"),
                );
                Ok(value)
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    user_id = ?$context.user_id,
                    request_id = %$context.request_id,
                    operation = %$operation,
                    "Service operation failed"
                );
                Err(e)
            }
        }
    };
}

/// 服务性能监控宏
#[macro_export]
macro_rules! monitor_service_performance {
    ($service_name:expr, $operation:expr, $block:block) => {{
        let start = std::time::Instant::now();
        let result = $block;
        let duration = start.elapsed().as_millis() as u64;
        
        let success = result.is_ok();
        ServiceUtils::log_performance_metric($service_name, $operation, duration, success);
        
        result
    }};
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_service_context_creation() {
        let context = ServiceContext::new(Some("user123".to_string()));
        assert!(context.is_authenticated());
        assert_eq!(context.user_id().unwrap(), "user123");
    }

    #[test]
    fn test_service_context_unauthenticated() {
        let context = ServiceContext::new(None);
        assert!(!context.is_authenticated());
        assert!(context.user_id().is_err());
    }

    #[test]
    fn test_paged_result() {
        let items = vec![1, 2, 3, 4, 5];
        let result = PagedResult::new(items, 20, 2, 5);
        
        assert_eq!(result.total_count, 20);
        assert_eq!(result.page, 2);
        assert_eq!(result.page_size, 5);
        assert_eq!(result.total_pages, 4);
        assert!(result.has_next);
        assert!(result.has_previous);
        assert_eq!(result.len(), 5);
    }

    #[test]
    fn test_service_result() {
        let result = ServiceResult::new("test data".to_string())
            .with_execution_time(100)
            .with_cache_hit(true)
            .with_affected_entity("entity1".to_string())
            .with_warning("test warning".to_string());

        assert_eq!(result.data, "test data");
        assert_eq!(result.metadata.execution_time_ms, 100);
        assert!(result.metadata.cache_hit);
        assert_eq!(result.metadata.affected_entities.len(), 1);
        assert_eq!(result.metadata.warnings.len(), 1);
    }
}
