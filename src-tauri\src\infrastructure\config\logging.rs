// 日志系统配置和初始化
// 基于tracing框架的结构化日志系统

use crate::shared::errors::{AppError, AppResult};
use super::{LoggingConfig, LogFormat, LogOutput};
use std::path::PathBuf;
use tracing::{Level, Subscriber};
use tracing_subscriber::{
    fmt::{self, format::FmtSpan, time::ChronoUtc},
    layer::SubscriberExt,
    util::SubscriberInitExt,
    EnvFilter, Layer, Registry,
};

/// 日志系统管理器
pub struct LoggingManager {
    config: LoggingConfig,
}

impl LoggingManager {
    /// 创建新的日志管理器
    pub fn new(config: LoggingConfig) -> Self {
        Self { config }
    }

    /// 初始化日志系统
    pub fn initialize(&self) -> AppResult<()> {
        // 创建日志目录
        if self.config.enable_file {
            if let Some(log_path) = &self.config.file_path {
                if let Some(parent) = log_path.parent() {
                    std::fs::create_dir_all(parent).map_err(|e| {
                        AppError::configuration(format!("Failed to create log directory: {}", e))
                    })?;
                }
            }
        }

        // 创建环境过滤器
        let env_filter = self.create_env_filter()?;

        // 创建订阅者
        let subscriber = self.create_subscriber(env_filter)?;

        // 设置全局订阅者
        subscriber.try_init().map_err(|e| {
            AppError::configuration(format!("Failed to initialize tracing subscriber: {}", e))
        })?;

        tracing::info!("Logging system initialized successfully");
        tracing::debug!("Log configuration: {:?}", self.config);

        Ok(())
    }

    /// 创建环境过滤器
    fn create_env_filter(&self) -> AppResult<EnvFilter> {
        let level = self.config.to_tracing_level()?;
        
        let mut filter = EnvFilter::new(format!("{}={}", env!("CARGO_PKG_NAME"), level));

        // 添加目标过滤器
        for target in &self.config.filter_targets {
            filter = filter.add_directive(format!("{}={}", target, level).parse().map_err(|e| {
                AppError::configuration(format!("Invalid filter directive for {}: {}", target, e))
            })?);
        }

        // 从环境变量读取额外的过滤器
        if let Ok(env_filter) = std::env::var("RUST_LOG") {
            filter = EnvFilter::try_new(env_filter).map_err(|e| {
                AppError::configuration(format!("Invalid RUST_LOG environment variable: {}", e))
            })?;
        }

        Ok(filter)
    }

    /// 创建订阅者
    fn create_subscriber(&self, env_filter: EnvFilter) -> AppResult<impl Subscriber + Send + Sync> {
        let registry = Registry::default().with(env_filter);

        match self.config.output {
            LogOutput::Console => {
                let console_layer = self.create_console_layer()?;
                Ok(registry.with(console_layer))
            }
            LogOutput::File => {
                let file_layer = self.create_file_layer()?;
                Ok(registry.with(file_layer))
            }
            LogOutput::Both => {
                let console_layer = self.create_console_layer()?;
                let file_layer = self.create_file_layer()?;
                Ok(registry.with(console_layer).with(file_layer))
            }
        }
    }

    /// 创建控制台日志层
    fn create_console_layer(&self) -> AppResult<impl Layer<Registry>> {
        if !self.config.enable_console {
            return Err(AppError::configuration("Console logging is disabled"));
        }

        let layer = match self.config.format {
            LogFormat::Json => fmt::layer()
                .json()
                .with_timer(ChronoUtc::rfc_3339())
                .with_target(true)
                .with_thread_ids(true)
                .with_thread_names(true)
                .with_span_events(FmtSpan::CLOSE)
                .boxed(),
            LogFormat::Pretty => fmt::layer()
                .pretty()
                .with_timer(ChronoUtc::rfc_3339())
                .with_target(true)
                .with_thread_ids(false)
                .with_thread_names(false)
                .with_span_events(FmtSpan::CLOSE)
                .boxed(),
            LogFormat::Compact => fmt::layer()
                .compact()
                .with_timer(ChronoUtc::rfc_3339())
                .with_target(false)
                .with_thread_ids(false)
                .with_thread_names(false)
                .with_span_events(FmtSpan::NONE)
                .boxed(),
        };

        Ok(layer)
    }

    /// 创建文件日志层
    fn create_file_layer(&self) -> AppResult<impl Layer<Registry>> {
        if !self.config.enable_file {
            return Err(AppError::configuration("File logging is disabled"));
        }

        let log_path = self.config.get_log_file_path()?;
        
        // 创建文件追加器
        let file_appender = tracing_appender::rolling::daily(
            log_path.parent().unwrap_or_else(|| std::path::Path::new(".")),
            log_path.file_name().unwrap_or_else(|| std::ffi::OsStr::new("app.log")),
        );

        let (non_blocking, _guard) = tracing_appender::non_blocking(file_appender);

        let layer = fmt::layer()
            .json()
            .with_writer(non_blocking)
            .with_timer(ChronoUtc::rfc_3339())
            .with_target(true)
            .with_thread_ids(true)
            .with_thread_names(true)
            .with_span_events(FmtSpan::CLOSE);

        Ok(layer)
    }

    /// 轮转日志文件
    pub fn rotate_logs(&self) -> AppResult<()> {
        if !self.config.enable_file {
            return Ok(());
        }

        let log_path = self.config.get_log_file_path()?;
        let log_dir = log_path.parent().ok_or_else(|| {
            AppError::configuration("Invalid log file path")
        })?;

        // 获取所有日志文件
        let mut log_files = Vec::new();
        for entry in std::fs::read_dir(log_dir).map_err(|e| {
            AppError::configuration(format!("Failed to read log directory: {}", e))
        })? {
            let entry = entry.map_err(|e| {
                AppError::configuration(format!("Failed to read directory entry: {}", e))
            })?;
            
            let path = entry.path();
            if path.is_file() && path.extension().map_or(false, |ext| ext == "log") {
                if let Ok(metadata) = entry.metadata() {
                    log_files.push((path, metadata.modified().unwrap_or(std::time::UNIX_EPOCH)));
                }
            }
        }

        // 按修改时间排序
        log_files.sort_by(|a, b| b.1.cmp(&a.1));

        // 删除超出数量限制的文件
        if log_files.len() > self.config.max_files as usize {
            for (path, _) in log_files.iter().skip(self.config.max_files as usize) {
                if let Err(e) = std::fs::remove_file(path) {
                    tracing::warn!("Failed to remove old log file {:?}: {}", path, e);
                }
            }
        }

        // 检查文件大小并轮转
        for (path, _) in log_files.iter().take(self.config.max_files as usize) {
            if let Ok(metadata) = std::fs::metadata(path) {
                let size_mb = metadata.len() / (1024 * 1024);
                if size_mb > self.config.max_file_size_mb {
                    let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
                    let new_name = format!("{}.{}.old", path.display(), timestamp);
                    if let Err(e) = std::fs::rename(path, &new_name) {
                        tracing::warn!("Failed to rotate log file {:?}: {}", path, e);
                    }
                }
            }
        }

        Ok(())
    }

    /// 获取日志统计信息
    pub fn get_log_stats(&self) -> AppResult<LogStats> {
        if !self.config.enable_file {
            return Ok(LogStats::default());
        }

        let log_path = self.config.get_log_file_path()?;
        let log_dir = log_path.parent().ok_or_else(|| {
            AppError::configuration("Invalid log file path")
        })?;

        let mut total_size = 0;
        let mut file_count = 0;
        let mut oldest_file = None;
        let mut newest_file = None;

        for entry in std::fs::read_dir(log_dir).map_err(|e| {
            AppError::configuration(format!("Failed to read log directory: {}", e))
        })? {
            let entry = entry.map_err(|e| {
                AppError::configuration(format!("Failed to read directory entry: {}", e))
            })?;
            
            let path = entry.path();
            if path.is_file() && path.extension().map_or(false, |ext| ext == "log") {
                if let Ok(metadata) = entry.metadata() {
                    total_size += metadata.len();
                    file_count += 1;
                    
                    let modified = metadata.modified().unwrap_or(std::time::UNIX_EPOCH);
                    if oldest_file.is_none() || modified < oldest_file.unwrap() {
                        oldest_file = Some(modified);
                    }
                    if newest_file.is_none() || modified > newest_file.unwrap() {
                        newest_file = Some(modified);
                    }
                }
            }
        }

        Ok(LogStats {
            total_size_bytes: total_size,
            file_count,
            oldest_file_time: oldest_file,
            newest_file_time: newest_file,
        })
    }
}

/// 日志统计信息
#[derive(Debug, Clone)]
pub struct LogStats {
    pub total_size_bytes: u64,
    pub file_count: u32,
    pub oldest_file_time: Option<std::time::SystemTime>,
    pub newest_file_time: Option<std::time::SystemTime>,
}

impl Default for LogStats {
    fn default() -> Self {
        Self {
            total_size_bytes: 0,
            file_count: 0,
            oldest_file_time: None,
            newest_file_time: None,
        }
    }
}

/// 日志宏扩展
#[macro_export]
macro_rules! log_error {
    ($($arg:tt)*) => {
        tracing::error!($($arg)*);
    };
}

#[macro_export]
macro_rules! log_warn {
    ($($arg:tt)*) => {
        tracing::warn!($($arg)*);
    };
}

#[macro_export]
macro_rules! log_info {
    ($($arg:tt)*) => {
        tracing::info!($($arg)*);
    };
}

#[macro_export]
macro_rules! log_debug {
    ($($arg:tt)*) => {
        tracing::debug!($($arg)*);
    };
}

#[macro_export]
macro_rules! log_trace {
    ($($arg:tt)*) => {
        tracing::trace!($($arg)*);
    };
}

/// 性能监控宏
#[macro_export]
macro_rules! measure_time {
    ($name:expr, $block:block) => {{
        let start = std::time::Instant::now();
        let result = $block;
        let duration = start.elapsed();
        tracing::debug!("Operation '{}' took {:?}", $name, duration);
        result
    }};
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_logging_manager_creation() {
        let config = LoggingConfig::default();
        let manager = LoggingManager::new(config);
        assert_eq!(manager.config.level, "info");
    }

    #[test]
    fn test_env_filter_creation() {
        let config = LoggingConfig::default();
        let manager = LoggingManager::new(config);
        let filter = manager.create_env_filter();
        assert!(filter.is_ok());
    }

    #[test]
    fn test_log_stats_default() {
        let stats = LogStats::default();
        assert_eq!(stats.total_size_bytes, 0);
        assert_eq!(stats.file_count, 0);
        assert!(stats.oldest_file_time.is_none());
    }
}
