// 任务列表页面
// 显示用户的所有任务，支持筛选、搜索和排序

import { Component, createSignal, createEffect, Show, For } from 'solid-js';
import { useNavigate, useSearchParams } from '@solidjs/router';
import { MainLayout } from '../../components/layout/MainLayout';
import { Button } from '../../components/ui/Button';
import { Input, Checkbox } from '../../components/ui/Input';
import { api } from '../../utils/api';
import toast from 'solid-toast';

interface Task {
  id: string;
  title: string;
  description?: string;
  status: string;
  priority: string;
  parent_task_id?: string;
  project_id?: string;
  project_name?: string;
  area_id?: string;
  area_name?: string;
  assigned_to?: string;
  due_date?: string;
  estimated_minutes?: number;
  actual_minutes: number;
  completion_percentage: number;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  subtask_count?: number;
  completed_subtask_count?: number;
}

export const TaskListPage: Component = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  const [tasks, setTasks] = createSignal<Task[]>([]);
  const [loading, setLoading] = createSignal(true);
  const [searchQuery, setSearchQuery] = createSignal(searchParams.search || '');
  const [statusFilter, setStatusFilter] = createSignal(searchParams.status || 'all');
  const [priorityFilter, setPriorityFilter] = createSignal(searchParams.priority || 'all');
  const [projectFilter, setProjectFilter] = createSignal(searchParams.project || 'all');
  const [sortBy, setSortBy] = createSignal(searchParams.sort || 'updated_at');
  const [sortOrder, setSortOrder] = createSignal(searchParams.order || 'desc');
  const [currentPage, setCurrentPage] = createSignal(parseInt(searchParams.page || '1'));
  const [totalPages, setTotalPages] = createSignal(1);
  const [selectedTasks, setSelectedTasks] = createSignal<Set<string>>(new Set());
  const [viewMode, setViewMode] = createSignal<'list' | 'kanban'>('list');

  // 加载任务列表
  const loadTasks = async () => {
    try {
      setLoading(true);
      
      const filters: any = {};
      if (statusFilter() !== 'all') filters.status = statusFilter();
      if (priorityFilter() !== 'all') filters.priority = priorityFilter();
      if (projectFilter() !== 'all') filters.project_id = projectFilter();
      if (searchQuery()) filters.search = searchQuery();

      const response = await api.listTasks(
        { page: currentPage(), page_size: 20 },
        filters
      );

      setTasks(response.items);
      setTotalPages(response.total_pages);
    } catch (error) {
      console.error('Failed to load tasks:', error);
      toast.error('加载任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载和参数变化时重新加载
  createEffect(() => {
    loadTasks();
  });

  // 更新任务状态
  const updateTaskStatus = async (taskId: string, status: string) => {
    try {
      await api.updateTaskStatus(taskId, status);
      await loadTasks();
      toast.success('任务状态已更新');
    } catch (error) {
      toast.error('更新任务状态失败');
    }
  };

  // 更新任务完成度
  const updateTaskCompletion = async (taskId: string, completion: number) => {
    try {
      await api.updateTaskCompletion(taskId, completion);
      await loadTasks();
      toast.success('任务完成度已更新');
    } catch (error) {
      toast.error('更新任务完成度失败');
    }
  };

  // 批量操作
  const handleBatchAction = async (action: string) => {
    const selected = Array.from(selectedTasks());
    if (selected.length === 0) {
      toast.error('请先选择要操作的任务');
      return;
    }

    try {
      for (const taskId of selected) {
        if (action === 'complete') {
          await api.updateTaskStatus(taskId, 'completed');
        } else if (action === 'delete') {
          await api.deleteTask(taskId);
        }
      }
      
      setSelectedTasks(new Set());
      await loadTasks();
      toast.success(`批量${action === 'complete' ? '完成' : '删除'}任务成功`);
    } catch (error) {
      toast.error(`批量操作失败`);
    }
  };

  // 切换任务选择
  const toggleTaskSelection = (taskId: string) => {
    const selected = new Set(selectedTasks());
    if (selected.has(taskId)) {
      selected.delete(taskId);
    } else {
      selected.add(taskId);
    }
    setSelectedTasks(selected);
  };

  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedTasks().size === tasks().length) {
      setSelectedTasks(new Set());
    } else {
      setSelectedTasks(new Set(tasks().map(t => t.id)));
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'todo': return 'bg-gray-100 text-gray-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 获取优先级颜色
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // 格式化日期
  const formatDate = (dateString?: string) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) {
      return `逾期 ${Math.abs(diffDays)} 天`;
    } else if (diffDays === 0) {
      return '今天';
    } else if (diffDays === 1) {
      return '明天';
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  // 检查是否逾期
  const isOverdue = (dueDate?: string) => {
    if (!dueDate) return false;
    return new Date(dueDate) < new Date();
  };

  return (
    <MainLayout title="任务管理">
      <div class="space-y-6">
        {/* 页面头部 */}
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">任务列表</h1>
            <p class="mt-2 text-sm text-gray-700">
              管理您的所有任务，跟踪进度和状态
            </p>
          </div>
          <div class="mt-4 sm:mt-0 flex items-center space-x-3">
            {/* 视图切换 */}
            <div class="flex rounded-lg border border-gray-300">
              <button
                class={`px-3 py-2 text-sm font-medium rounded-l-lg ${
                  viewMode() === 'list'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setViewMode('list')}
              >
                列表
              </button>
              <button
                class={`px-3 py-2 text-sm font-medium rounded-r-lg border-l ${
                  viewMode() === 'kanban'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setViewMode('kanban')}
              >
                看板
              </button>
            </div>
            
            <Button onClick={() => navigate('/tasks/new')}>
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              新建任务
            </Button>
          </div>
        </div>

        {/* 搜索和筛选 */}
        <div class="bg-white p-4 rounded-lg shadow">
          <div class="flex flex-col lg:flex-row gap-4">
            {/* 搜索框 */}
            <div class="flex-1">
              <Input
                type="search"
                placeholder="搜索任务标题或描述..."
                value={searchQuery()}
                onInput={(e) => setSearchQuery(e.currentTarget.value)}
                leftIcon={() => (
                  <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                )}
              />
            </div>

            {/* 筛选器 */}
            <div class="flex flex-wrap gap-2">
              {/* 状态筛选 */}
              <select
                class="px-3 py-2 border border-gray-300 rounded-md text-sm"
                value={statusFilter()}
                onChange={(e) => setStatusFilter(e.currentTarget.value)}
              >
                <option value="all">所有状态</option>
                <option value="todo">待办</option>
                <option value="in_progress">进行中</option>
                <option value="completed">已完成</option>
              </select>

              {/* 优先级筛选 */}
              <select
                class="px-3 py-2 border border-gray-300 rounded-md text-sm"
                value={priorityFilter()}
                onChange={(e) => setPriorityFilter(e.currentTarget.value)}
              >
                <option value="all">所有优先级</option>
                <option value="urgent">紧急</option>
                <option value="high">高</option>
                <option value="medium">中</option>
                <option value="low">低</option>
              </select>
            </div>
          </div>

          {/* 批量操作 */}
          <Show when={selectedTasks().size > 0}>
            <div class="mt-4 flex items-center justify-between p-3 bg-blue-50 rounded-lg">
              <span class="text-sm text-blue-700">
                已选择 {selectedTasks().size} 个任务
              </span>
              <div class="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => handleBatchAction('complete')}
                >
                  批量完成
                </Button>
                <Button
                  size="sm"
                  variant="danger"
                  onClick={() => handleBatchAction('delete')}
                >
                  批量删除
                </Button>
              </div>
            </div>
          </Show>
        </div>

        {/* 任务列表 */}
        <div class="bg-white shadow rounded-lg">
          <Show
            when={!loading()}
            fallback={
              <div class="p-8 text-center">
                <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p class="mt-2 text-gray-500">加载中...</p>
              </div>
            }
          >
            <Show
              when={tasks().length > 0}
              fallback={
                <div class="p-8 text-center">
                  <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                  </svg>
                  <h3 class="mt-2 text-sm font-medium text-gray-900">暂无任务</h3>
                  <p class="mt-1 text-sm text-gray-500">开始创建您的第一个任务吧</p>
                  <div class="mt-6">
                    <Button onClick={() => navigate('/tasks/new')}>
                      新建任务
                    </Button>
                  </div>
                </div>
              }
            >
              <div class="overflow-hidden">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left">
                        <Checkbox
                          checked={selectedTasks().size === tasks().length && tasks().length > 0}
                          indeterminate={selectedTasks().size > 0 && selectedTasks().size < tasks().length}
                          onChange={toggleSelectAll}
                        />
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        任务
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        状态
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        优先级
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        项目
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        截止日期
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        进度
                      </th>
                      <th scope="col" class="relative px-6 py-3">
                        <span class="sr-only">操作</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <For each={tasks()}>
                      {(task) => (
                        <tr class={`hover:bg-gray-50 ${selectedTasks().has(task.id) ? 'bg-blue-50' : ''}`}>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <Checkbox
                              checked={selectedTasks().has(task.id)}
                              onChange={() => toggleTaskSelection(task.id)}
                            />
                          </td>
                          <td class="px-6 py-4">
                            <div class="flex items-center">
                              <div class="flex-1">
                                <div class="text-sm font-medium text-gray-900">
                                  <button
                                    type="button"
                                    class="hover:text-blue-600 text-left"
                                    onClick={() => navigate(`/tasks/${task.id}`)}
                                  >
                                    {task.title}
                                  </button>
                                </div>
                                <Show when={task.description}>
                                  <div class="text-sm text-gray-500 truncate max-w-xs">
                                    {task.description}
                                  </div>
                                </Show>
                                <div class="flex items-center mt-1 space-x-2">
                                  <Show when={task.area_name}>
                                    <span class="text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded">
                                      {task.area_name}
                                    </span>
                                  </Show>
                                  <Show when={task.subtask_count && task.subtask_count > 0}>
                                    <span class="text-xs text-gray-500">
                                      子任务: {task.completed_subtask_count || 0}/{task.subtask_count}
                                    </span>
                                  </Show>
                                </div>
                              </div>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <select
                              class={`text-xs font-semibold rounded-full px-2 py-1 border-0 ${getStatusColor(task.status)}`}
                              value={task.status}
                              onChange={(e) => updateTaskStatus(task.id, e.currentTarget.value)}
                            >
                              <option value="todo">待办</option>
                              <option value="in_progress">进行中</option>
                              <option value="completed">已完成</option>
                              <option value="cancelled">已取消</option>
                            </select>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <span class={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(task.priority)}`}>
                              {task.priority === 'urgent' ? '紧急' :
                               task.priority === 'high' ? '高' :
                               task.priority === 'medium' ? '中' :
                               task.priority === 'low' ? '低' : task.priority}
                            </span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <Show when={task.project_name} fallback="-">
                              <button
                                type="button"
                                class="text-blue-600 hover:text-blue-800"
                                onClick={() => navigate(`/projects/${task.project_id}`)}
                              >
                                {task.project_name}
                              </button>
                            </Show>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-sm">
                            <span class={isOverdue(task.due_date) ? 'text-red-600 font-medium' : 'text-gray-900'}>
                              {formatDate(task.due_date)}
                            </span>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                              <div class="w-16 bg-gray-200 rounded-full h-2 mr-2">
                                <div
                                  class="bg-blue-600 h-2 rounded-full"
                                  style={{ width: `${task.completion_percentage}%` }}
                                />
                              </div>
                              <input
                                type="number"
                                min="0"
                                max="100"
                                class="w-12 text-xs border border-gray-300 rounded px-1 py-0.5"
                                value={task.completion_percentage}
                                onChange={(e) => {
                                  const value = parseInt(e.currentTarget.value) || 0;
                                  updateTaskCompletion(task.id, Math.min(100, Math.max(0, value)));
                                }}
                              />
                              <span class="text-xs text-gray-500 ml-1">%</span>
                            </div>
                          </td>
                          <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex items-center space-x-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => navigate(`/tasks/${task.id}`)}
                              >
                                查看
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => navigate(`/tasks/${task.id}/edit`)}
                              >
                                编辑
                              </Button>
                            </div>
                          </td>
                        </tr>
                      )}
                    </For>
                  </tbody>
                </table>
              </div>
            </Show>
          </Show>
        </div>
      </div>
    </MainLayout>
  );
};

export default TaskListPage;
