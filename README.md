# PaoLife - 个人生活管理系统

![PaoLife Logo](https://img.shields.io/badge/PaoLife-v1.0.0-blue.svg)
![Tauri](https://img.shields.io/badge/Tauri-2.0-orange.svg)
![SolidJS](https://img.shields.io/badge/SolidJS-1.8-blue.svg)
![Rust](https://img.shields.io/badge/Rust-1.75-red.svg)
![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)

PaoLife 是一个基于 Tauri 2 + SolidJS + Rust 构建的现代化个人生活管理系统，采用领域驱动设计（DDD）架构，提供项目管理、任务跟踪、生活领域规划等功能。

## ✨ 特性

### 🎯 核心功能
- **项目管理** - 创建、跟踪和管理个人项目
- **任务系统** - 层级化任务管理，支持子任务和依赖关系
- **领域规划** - 基于生活领域的目标管理
- **用户认证** - 安全的用户注册、登录和权限管理
- **数据统计** - 项目和任务的进度统计与可视化

### 🏗️ 技术特性
- **现代化架构** - 基于 DDD（领域驱动设计）的清洁架构
- **类型安全** - 全栈 TypeScript + Rust 类型安全
- **响应式设计** - 支持深色模式的现代化 UI
- **高性能** - Tauri 原生性能 + SolidJS 细粒度响应式
- **数据持久化** - SQLite 数据库 + 自动迁移

## 🚀 快速开始

### 环境要求

- **Node.js** >= 18.0.0
- **Rust** >= 1.75.0
- **pnpm** >= 8.0.0 (推荐)

### 安装依赖

```bash
# 安装前端依赖
pnpm install

# 安装 Tauri CLI
cargo install tauri-cli
```

### 开发模式

```bash
# 启动开发服务器
pnpm tauri dev
```

### 构建应用

```bash
# 构建生产版本
pnpm tauri build
```

## 📁 项目结构

```
PaoLife/
├── src/                          # 前端源码 (SolidJS)
│   ├── components/               # 可复用组件
│   │   ├── Layout.tsx           # 主布局组件
│   │   └── ProtectedRoute.tsx   # 路由保护组件
│   ├── contexts/                # React Context
│   │   ├── AuthContext.tsx      # 认证上下文
│   │   └── ThemeContext.tsx     # 主题上下文
│   ├── pages/                   # 页面组件
│   │   ├── auth/               # 认证相关页面
│   │   ├── projects/           # 项目管理页面
│   │   ├── tasks/              # 任务管理页面
│   │   ├── areas/              # 领域管理页面
│   │   ├── DashboardPage.tsx   # 仪表板
│   │   └── SettingsPage.tsx    # 设置页面
│   └── App.tsx                 # 应用入口
├── src-tauri/                   # 后端源码 (Rust)
│   ├── src/
│   │   ├── api/                # API 接口层
│   │   │   ├── commands/       # Tauri 命令
│   │   │   └── mod.rs          # API 模块
│   │   ├── application/        # 应用层
│   │   │   ├── services/       # 应用服务
│   │   │   ├── dto/            # 数据传输对象
│   │   │   └── use_cases/      # 用例
│   │   ├── domain/             # 领域层
│   │   │   ├── entities/       # 领域实体
│   │   │   ├── repositories/   # 仓储接口
│   │   │   └── services/       # 领域服务
│   │   ├── infrastructure/     # 基础设施层
│   │   │   ├── database/       # 数据库实现
│   │   │   │   ├── models/     # 数据模型
│   │   │   │   ├── mappers/    # 对象映射
│   │   │   │   ├── repositories/ # 仓储实现
│   │   │   │   └── migrations/ # 数据库迁移
│   │   │   └── config/         # 配置管理
│   │   ├── shared/             # 共享模块
│   │   │   ├── types/          # 类型定义
│   │   │   ├── errors/         # 错误处理
│   │   │   └── utils/          # 工具函数
│   │   └── main.rs             # 应用入口
│   ├── Cargo.toml              # Rust 依赖配置
│   └── tauri.conf.json         # Tauri 配置
├── package.json                # 前端依赖配置
├── vite.config.ts              # Vite 配置
├── tailwind.config.js          # Tailwind CSS 配置
└── README.md                   # 项目文档
```

## 🏛️ 架构设计

### DDD 分层架构

PaoLife 采用领域驱动设计（DDD）的分层架构：

#### 1. 领域层 (Domain Layer)
- **实体 (Entities)**: 用户、项目、任务、领域等核心业务对象
- **值对象 (Value Objects)**: 优先级、状态等不可变对象
- **仓储接口 (Repository Interfaces)**: 数据访问抽象
- **领域服务 (Domain Services)**: 复杂业务逻辑

#### 2. 应用层 (Application Layer)
- **应用服务 (Application Services)**: 用例编排和业务流程
- **DTO (Data Transfer Objects)**: 数据传输对象
- **命令和查询 (Commands & Queries)**: CQRS 模式

#### 3. 基础设施层 (Infrastructure Layer)
- **数据库实现**: SQLite + sqlx ORM
- **仓储实现**: 具体的数据访问实现
- **配置管理**: 应用配置和环境变量

#### 4. API 接口层 (API Layer)
- **Tauri 命令**: 前后端通信接口
- **认证处理**: 用户认证和授权
- **错误处理**: 统一的错误响应

### 前端架构

- **SolidJS**: 细粒度响应式 UI 框架
- **Context API**: 全局状态管理
- **Tailwind CSS**: 原子化 CSS 框架
- **TypeScript**: 类型安全的 JavaScript

## 🗄️ 数据库设计

### 核心表结构

```sql
-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE,
    password_hash TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    timezone TEXT DEFAULT 'UTC',
    language TEXT DEFAULT 'en',
    is_active BOOLEAN DEFAULT true,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_login_at DATETIME
);

-- 领域表
CREATE TABLE areas (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    standard TEXT,
    icon_name TEXT,
    color_hex TEXT,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    archived_at DATETIME,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 项目表
CREATE TABLE projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'not_started',
    progress INTEGER DEFAULT 0,
    priority INTEGER DEFAULT 2,
    start_date DATE,
    deadline DATE,
    estimated_hours INTEGER,
    actual_hours INTEGER DEFAULT 0,
    area_id TEXT,
    created_by TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    archived_at DATETIME,
    FOREIGN KEY (area_id) REFERENCES areas(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 任务表
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT DEFAULT 'todo',
    priority INTEGER DEFAULT 2,
    parent_task_id TEXT,
    project_id TEXT,
    area_id TEXT,
    assigned_to TEXT,
    due_date DATE,
    estimated_minutes INTEGER,
    actual_minutes INTEGER DEFAULT 0,
    completion_percentage INTEGER DEFAULT 0,
    sort_order INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    completed_at DATETIME,
    FOREIGN KEY (parent_task_id) REFERENCES tasks(id),
    FOREIGN KEY (project_id) REFERENCES projects(id),
    FOREIGN KEY (area_id) REFERENCES areas(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id)
);
```

## 🔧 开发指南

### 添加新功能

1. **领域层**: 在 `domain/entities/` 中定义实体
2. **仓储接口**: 在 `domain/repositories/` 中定义接口
3. **仓储实现**: 在 `infrastructure/database/repositories/` 中实现
4. **应用服务**: 在 `application/services/` 中编写业务逻辑
5. **API 命令**: 在 `api/commands/` 中添加 Tauri 命令
6. **前端页面**: 在 `src/pages/` 中创建 UI 组件

### 数据库迁移

```bash
# 创建新迁移
cargo run --bin create_migration <migration_name>

# 运行迁移
cargo run --bin migrate
```

### 代码规范

- **Rust**: 使用 `cargo fmt` 和 `cargo clippy`
- **TypeScript**: 使用 ESLint 和 Prettier
- **提交信息**: 遵循 Conventional Commits 规范

## 🧪 测试

```bash
# 运行 Rust 测试
cargo test

# 运行前端测试
pnpm test
```

## 📦 部署

### 构建发布版本

```bash
# 构建应用
pnpm tauri build

# 生成的文件位于
# target/release/bundle/
```

### 支持平台

- ✅ Windows (x64)
- ✅ macOS (Intel & Apple Silicon)
- ✅ Linux (x64)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Tauri](https://tauri.app/) - 跨平台应用框架
- [SolidJS](https://www.solidjs.com/) - 响应式 UI 框架
- [Rust](https://www.rust-lang.org/) - 系统编程语言
- [SQLite](https://www.sqlite.org/) - 嵌入式数据库
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 创建 [Issue](https://github.com/your-username/paolife/issues)
- 发送邮件至: <EMAIL>

---

**PaoLife** - 让生活更有序，让目标更清晰 🎯
