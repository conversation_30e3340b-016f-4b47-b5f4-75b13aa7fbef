# PaoLife项目详细技术文档

## 第五部分：领域管理页面详细分析

### 页面概览

领域管理模块是PaoLife应用基于P.A.R.A.方法论的核心功能，包含领域列表页面 (`AreasPage.tsx`) 和领域详情页面 (`AreaDetailPage.tsx`)。实现了完整的生活领域管理，包括习惯追踪、领域KPI管理、定期维护任务、清单模板库等功能。

### 核心架构设计

#### 1. 领域列表页面 (AreasPage.tsx)

**页面布局结构**:
```typescript
// 领域页面状态管理
const [searchQuery, setSearchQuery] = useState('')
const [statusFilter, setStatusFilter] = useState('all')
const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
const [editingArea, setEditingArea] = useState<Area | null>(null)

// 领域状态枚举
const AreaStatus = {
  ACTIVE: 'Active',                    // 活跃状态
  NEEDS_ATTENTION: 'Needs Attention', // 需要关注
  ON_HOLD: 'On Hold',                 // 暂停状态
  REVIEW_REQUIRED: 'Review Required'   // 需要回顾
}
```

**领域筛选和搜索**:
```typescript
// 高级筛选逻辑
const filteredAreas = useMemo(() => {
  return areas
    .filter(area => !area.archived) // 排除归档领域
    .filter(area => {
      // 搜索过滤
      const matchesSearch = !searchQuery || 
        area.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        area.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        area.standard?.toLowerCase().includes(searchQuery.toLowerCase())

      // 状态过滤
      const matchesStatus = statusFilter === 'all' || area.status === statusFilter

      return matchesSearch && matchesStatus
    })
    .sort((a, b) => {
      // 排序优先级: 活跃 > 需要关注 > 需要回顾 > 暂停
      const statusPriority = {
        'Active': 1,
        'Needs Attention': 2,
        'Review Required': 3,
        'On Hold': 4
      }
      
      const aPriority = statusPriority[a.status] || 999
      const bPriority = statusPriority[b.status] || 999
      
      if (aPriority !== bPriority) {
        return aPriority - bPriority
      }
      
      // 相同状态按更新时间倒序
      return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
    })
}, [areas, searchQuery, statusFilter])
```

**领域统计计算**:
```typescript
// 获取领域关联项目数量
const getRelatedProjectsCount = (areaId: string): number => {
  return projects.filter(project => 
    project.areaId === areaId && !project.archived
  ).length
}

// 计算领域习惯完成率
const getHabitCompletionRate = (area: Area): number => {
  const areaHabits = habits.filter(h => h.areaId === area.id)
  if (areaHabits.length === 0) return 0

  let totalPossibleCompletions = 0
  let actualCompletions = 0

  // 计算本周的完成情况
  const now = new Date()
  const weekStart = new Date(now)
  weekStart.setDate(now.getDate() - now.getDay()) // 本周开始
  const daysInWeek = 7

  areaHabits.forEach(habit => {
    // 根据习惯频率计算可能的完成次数
    if (habit.frequency === 'daily') {
      totalPossibleCompletions += daysInWeek
    } else if (habit.frequency === 'weekly') {
      totalPossibleCompletions += 1
    }

    // 统计实际完成次数
    for (let i = 0; i < daysInWeek; i++) {
      const checkDate = new Date(weekStart)
      checkDate.setDate(weekStart.getDate() + i)
      const dateStr = checkDate.toISOString().split('T')[0]

      const record = habitRecords.find(r => {
        const recordDateStr = typeof r.date === 'string' 
          ? r.date 
          : new Date(r.date).toISOString().split('T')[0]
        return r.habitId === habit.id && recordDateStr === dateStr && 
               (r.completed || (r.value && r.value > 0))
      })

      if (record) {
        if (habit.frequency === 'daily') {
          actualCompletions += 1
        } else if (habit.frequency === 'weekly' && i === 0) {
          actualCompletions += 1
        }
      }
    }
  })

  return totalPossibleCompletions > 0 
    ? Math.round((actualCompletions / totalPossibleCompletions) * 100) 
    : 0
}
```

#### 2. 领域卡片组件 (AreaCard.tsx)

**卡片信息展示**:
```typescript
// 领域状态颜色映射
const getStatusColor = (status: string) => {
  const colors = {
    'Active': 'bg-green-100 text-green-800 border-green-200',
    'Needs Attention': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'On Hold': 'bg-gray-100 text-gray-800 border-gray-200',
    'Review Required': 'bg-blue-100 text-blue-800 border-blue-200'
  }
  return colors[status] || 'bg-gray-100 text-gray-800 border-gray-200'
}

// 领域卡片组件
const AreaCard = ({ area, onEdit, onDelete, onArchive, relatedProjectsCount, habitCompletionRate }) => (
  <Card className="group transition-all duration-200 hover:shadow-md">
    <CardHeader className="pb-3">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            {/* 领域图标 */}
            <div 
              className="w-8 h-8 rounded-lg flex items-center justify-center text-white text-sm font-medium"
              style={{ backgroundColor: area.color || '#6366f1' }}
            >
              {area.icon || area.name.charAt(0)}
            </div>
            
            <div className="flex-1 min-w-0">
              <CardTitle className="text-lg truncate">
                <Link
                  to={`/areas/${area.id}`}
                  className="hover:text-primary transition-colors"
                >
                  {area.name}
                </Link>
              </CardTitle>
              <Badge variant="outline" className={getStatusColor(area.status)}>
                {getStatusText(area.status)}
              </Badge>
            </div>
          </div>

          {/* 领域标准 */}
          {area.standard && (
            <CardDescription className="text-sm line-clamp-2 italic">
              "{area.standard}"
            </CardDescription>
          )}
        </div>

        {/* 操作菜单 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onEdit(area)}>
              <Edit className="h-4 w-4 mr-2" />
              编辑
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onArchive(area)}>
              <Archive className="h-4 w-4 mr-2" />
              归档
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => onDelete(area)}
              className="text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </CardHeader>

    <CardContent className="pt-0">
      {/* 习惯完成率 */}
      <div className="space-y-2 mb-4">
        <div className="flex justify-between text-sm">
          <span className="text-muted-foreground">本周习惯完成率</span>
          <span className="font-medium">{habitCompletionRate}%</span>
        </div>
        <Progress value={habitCompletionRate} className="h-2" />
      </div>

      {/* 统计信息 */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <div className="flex items-center gap-1">
          <FolderOpen className="h-4 w-4" />
          <span>{relatedProjectsCount} 个项目</span>
        </div>
        <div className="flex items-center gap-1">
          <Calendar className="h-4 w-4" />
          <span>{area.reviewFrequency || '月度'} 回顾</span>
        </div>
      </div>
    </CardContent>
  </Card>
)
```

#### 3. 领域详情页面 (AreaDetailPage.tsx)

**页面结构布局**:
```typescript
// 两栏布局设计
const AreaDetailLayout = () => (
  <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
    {/* 左侧执行区 (2/3宽度) */}
    <div className="lg:col-span-2 space-y-6">
      {/* 习惯追踪器 */}
      <MiniHabitTracker
        areaId={areaId}
        onAddHabit={() => setIsCreateHabitDialogOpen(true)}
      />
      
      {/* 关键指标 */}
      <AreaKPIManagement areaId={areaId} />
      
      {/* 定期维护任务 */}
      <RecurringMaintenanceTasks
        areaId={areaId}
        onAddTask={() => setIsCreateRecurringTaskDialogOpen(true)}
      />
      
      {/* 进行中的清单 */}
      <ActiveChecklists areaId={areaId} />
    </div>

    {/* 右侧规划与资源区 (1/3宽度) */}
    <div className="space-y-6">
      {/* 关联项目 */}
      <CompactProjectList
        areaId={areaId}
        onCreateProject={() => setIsCreateProjectDialogOpen(true)}
      />
      
      {/* 关联资源 */}
      <UnifiedResources
        areaId={areaId}
        compact={true}
      />
      
      {/* 清单模板库 */}
      <CompactChecklistTemplates
        areaId={areaId}
        onCreateTemplate={() => setIsTemplateManagerOpen(true)}
      />
    </div>
  </div>
)
```

**领域数据加载策略**:
```typescript
// 支持归档领域的数据加载
const loadAreaData = async () => {
  setIsLoadingArea(true)
  try {
    if (isArchived) {
      // 归档领域通过API直接获取
      const result = await databaseApi.getAreaById(areaId, true)
      if (result.success && result.data) {
        setAreaData(result.data)
      } else {
        addNotification({
          type: 'error',
          title: '领域未找到',
          message: '未找到该领域，可能已被删除。'
        })
        navigate('/archive')
      }
    } else {
      // 活跃领域从store获取
      const area = areas.find(a => a.id === areaId)
      if (area) {
        setAreaData(area)
      } else {
        // store中没有则通过API获取
        const result = await databaseApi.getAreaById(areaId, false)
        if (result.success && result.data) {
          setAreaData(result.data)
        } else {
          addNotification({
            type: 'error',
            title: '领域未找到',
            message: '未找到该领域，可能已被删除。'
          })
          navigate('/areas')
        }
      }
    }
  } catch (error) {
    console.error('Failed to load area:', error)
    addNotification({
      type: 'error',
      title: '加载失败',
      message: '领域数据加载失败，请重试。'
    })
  } finally {
    setIsLoadingArea(false)
  }
}
```

#### 4. 习惯追踪系统 (MiniHabitTracker)

**习惯数据结构**:
```typescript
interface Habit {
  id: string
  name: string
  description?: string
  color?: string
  areaId: string
  frequency: 'daily' | 'weekly' | 'monthly'
  target: number
  unit?: string
  records?: HabitRecord[]
  createdAt: Date
  updatedAt?: Date
}

interface HabitRecord {
  date: string | Date
  completed: boolean
  value?: number  // 数值型习惯的记录值
  note?: string
}
```

**习惯类型识别**:
```typescript
// 判断是否为数值型习惯
const isNumericHabit = (habit: Habit): boolean => {
  return habit.target > 1 || !!habit.unit
}

// 获取习惯状态
const getHabitStatus = (habit: Habit, date: Date): boolean => {
  const dateStr = date.toISOString().split('T')[0]
  const record = habit.records?.find(r => {
    const recordDateStr = typeof r.date === 'string'
      ? r.date
      : new Date(r.date).toISOString().split('T')[0]
    return recordDateStr === dateStr
  })

  if (isNumericHabit(habit)) {
    // 数值型习惯：检查是否达到目标值
    return record ? (record.value || 0) >= habit.target : false
  } else {
    // 布尔型习惯：检查完成状态
    return record?.completed || false
  }
}

// 获取习惯进度百分比
const getHabitProgress = (habit: Habit, date: Date): number => {
  if (!isNumericHabit(habit)) return getHabitStatus(habit, date) ? 100 : 0

  const dateStr = date.toISOString().split('T')[0]
  const record = habit.records?.find(r => {
    const recordDateStr = typeof r.date === 'string'
      ? r.date
      : new Date(r.date).toISOString().split('T')[0]
    return recordDateStr === dateStr
  })

  const value = record?.value || 0
  return Math.min(Math.round((value / habit.target) * 100), 100)
}
```

**月历热力图实现**:
```typescript
// 生成当前月份的日历数据
const getCurrentMonthData = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = now.getMonth()

  // 获取月份的第一天和最后一天
  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)

  // 获取第一天是星期几 (0=周日, 1=周一, ...)
  const firstDayOfWeek = firstDay.getDay()

  // 生成所有日期
  const days: Date[] = []
  for (let day = 1; day <= lastDay.getDate(); day++) {
    days.push(new Date(year, month, day))
  }

  return {
    year,
    month,
    days,
    firstDayOfWeek,
    monthName: firstDay.toLocaleDateString('zh-CN', { month: 'long' })
  }
}

// 热力图渲染
const HeatmapGrid = ({ habit, currentMonth, onHabitClick }) => (
  <div className="grid grid-cols-7 gap-0.5 flex-1">
    {/* 星期标题 */}
    {['日', '一', '二', '三', '四', '五', '六'].map((day) => (
      <div
        key={day}
        className="text-xs text-center text-muted-foreground font-medium h-3 flex items-center justify-center"
      >
        {day}
      </div>
    ))}

    {/* 空白填充（月初前的空格） */}
    {Array.from({ length: currentMonth.firstDayOfWeek }).map((_, index) => (
      <div key={`empty-${index}`} className="aspect-square" />
    ))}

    {/* 日期格子 */}
    {currentMonth.days.map((date) => {
      const isCompleted = getHabitStatus(habit, date)
      const isTodayDate = isToday(date)
      const isFutureDate = isFuture(date)
      const progress = getHabitProgress(habit, date)
      const value = getHabitValue(habit, date)

      return (
        <Tooltip key={date.toISOString()}>
          <TooltipTrigger asChild>
            <button
              type="button"
              onClick={() => handleHabitClick(habit, date)}
              disabled={isFutureDate}
              className={cn(
                'aspect-square text-xs rounded-sm transition-all duration-200 border flex-shrink-0 min-w-0 relative overflow-hidden',
                'hover:scale-110 focus:outline-none focus:ring-1 focus:ring-offset-0',
                {
                  // 未来日期
                  'bg-gray-50 text-gray-400 border-gray-200 cursor-not-allowed opacity-60': isFutureDate,
                  // 数值型习惯100%完成
                  'bg-green-500 text-white border-green-600 hover:bg-green-600': !isFutureDate && isNumericHabit(habit) && progress >= 100,
                  // 数值型习惯部分完成
                  'bg-blue-500 text-white border-blue-600 hover:bg-blue-600': !isFutureDate && isNumericHabit(habit) && progress > 0 && progress < 100,
                  // 布尔型习惯已完成
                  'bg-green-500 text-white border-green-600 hover:bg-green-600': !isFutureDate && !isNumericHabit(habit) && isCompleted,
                  // 今天未完成
                  'bg-yellow-100 text-yellow-700 border-yellow-300 hover:bg-yellow-200': !isFutureDate && isTodayDate && !isCompleted,
                  // 其他未完成
                  'bg-gray-100 text-gray-500 border-gray-200 hover:bg-gray-200': !isFutureDate && !isTodayDate && !isCompleted
                }
              )}
            >
              {/* 显示日期 */}
              <span className="absolute inset-0 flex items-center justify-center text-xs font-medium">
                {date.getDate()}
              </span>

              {/* 数值型习惯的进度条 */}
              {isNumericHabit(habit) && progress > 0 && progress < 100 && (
                <div
                  className="absolute bottom-0 left-0 bg-white/30 h-1"
                  style={{ width: `${progress}%` }}
                />
              )}
            </button>
          </TooltipTrigger>
          <TooltipContent>
            <div className="text-center">
              <div className="font-medium">{habit.name}</div>
              <div className="text-sm">
                {date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
              </div>
              {isNumericHabit(habit) ? (
                <div className="text-sm">
                  {value}/{habit.target} {habit.unit || ''}
                  {progress > 0 && ` (${progress}%)`}
                </div>
              ) : (
                <div className="text-sm">
                  {isCompleted ? '已完成' : '未完成'}
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      )
    })}
  </div>
)
```

**习惯交互处理**:
```typescript
// 处理习惯点击
const handleHabitClick = async (habit: Habit, date: Date) => {
  if (isFuture(date)) return

  if (isNumericHabit(habit)) {
    // 数值型习惯：打开输入弹窗
    setSelectedHabit(habit)
    setSelectedDate(date)
    setValueDialogOpen(true)
  } else {
    // 布尔型习惯：直接切换状态
    await toggleHabitRecord(habit.id, date)
  }
}

// 保存数值型习惯的值
const handleSaveHabitValue = async (value: number) => {
  if (!selectedHabit || !selectedDate) return

  try {
    await setHabitValue(selectedHabit.id, selectedDate, value)
    setValueDialogOpen(false)
    setSelectedHabit(null)
    setSelectedDate(null)
  } catch (error) {
    console.error('Failed to save habit value:', error)
    addNotification({
      type: 'error',
      title: '保存失败',
      message: '习惯记录保存失败，请重试'
    })
  }
}

// 计算连续天数
const getCurrentStreak = (habit: Habit): number => {
  let streak = 0
  const today = new Date()

  for (let i = 0; i < 30; i++) {
    const date = new Date(today)
    date.setDate(today.getDate() - i)

    if (getHabitStatus(habit, date)) {
      streak++
    } else {
      break
    }
  }

  return streak
}
```

#### 5. 领域KPI管理系统 (AreaKPIManagement)

**统一KPI管理架构**:
```typescript
// 领域指标API适配器
class AreaMetricApiAdapter implements KPIApiInterface<AreaMetric, AreaMetricRecord> {
  async create(data: CreateKPIData & { parentId: string }): Promise<AreaMetric> {
    const result = await databaseApi.createAreaMetric({
      areaId: data.parentId,
      name: data.name,
      value: data.value,
      target: data.target,
      unit: data.unit,
      trackingType: data.trackingType || 'manual',
      direction: data.direction
    })

    if (!result.success) {
      throw new Error(result.error || 'Failed to create area metric')
    }

    return result.data
  }

  async getStatistics(areaId: string): Promise<KPIStatistics> {
    const result = await databaseApi.getAreaMetricStatistics(areaId)
    if (!result.success) {
      throw new Error(result.error || 'Failed to get metric statistics')
    }
    return result.data
  }
}

// 创建领域指标管理器
const createAreaMetricManager = () => new AreaMetricApiAdapter()
```

**KPI组件实现**:
```typescript
const AreaKPIManagement = ({ areaId }) => {
  const [metrics, setMetrics] = useState<AreaMetric[]>([])
  const [statistics, setStatistics] = useState<KPIStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [showCreateDialog, setShowCreateDialog] = useState(false)

  // 使用统一的KPI管理器
  const metricManager = createAreaMetricManager()

  // 加载指标数据
  const loadMetrics = async () => {
    try {
      setLoading(true)
      const [metricsData, statsData] = await Promise.all([
        metricManager.getAll(areaId),
        metricManager.getStatistics(areaId)
      ])
      setMetrics(metricsData)
      setStatistics(statsData)
    } catch (error) {
      console.error('Failed to load metrics:', error)
      addNotification({
        type: 'error',
        title: '加载失败',
        message: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              领域关键指标
            </CardTitle>
            <CardDescription>
              跟踪领域的重要指标和目标
            </CardDescription>
          </div>
          <Button onClick={() => setShowCreateDialog(true)}>
            <Plus className="h-4 w-4 mr-2" />
            添加指标
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {/* 指标统计概览 */}
        {statistics && (
          <div className="grid grid-cols-2 gap-4 mb-6">
            <div className="text-center p-3 bg-accent/20 rounded-lg">
              <div className="text-2xl font-bold text-green-600">
                {statistics.onTrackCount}
              </div>
              <div className="text-sm text-muted-foreground">达标指标</div>
            </div>
            <div className="text-center p-3 bg-accent/20 rounded-lg">
              <div className="text-2xl font-bold text-red-600">
                {statistics.offTrackCount}
              </div>
              <div className="text-sm text-muted-foreground">未达标指标</div>
            </div>
          </div>
        )}

        {/* 指标列表 */}
        {metrics.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">📊</div>
            <p className="text-sm">暂无关键指标</p>
            <p className="text-xs mt-1">添加指标来跟踪领域表现</p>
          </div>
        ) : (
          <div className="space-y-3">
            {metrics.map(metric => (
              <MetricItem
                key={metric.id}
                metric={metric}
                onEdit={setEditingMetric}
                onDelete={handleDeleteMetric}
                onRecord={handleRecordMetric}
              />
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```

#### 6. 定期维护任务系统 (RecurringMaintenanceTasks)

**任务数据结构**:
```typescript
interface RecurringTask {
  id: string
  title: string
  description?: string
  repeatRule: 'daily' | 'weekly' | 'monthly' | 'yearly'
  repeatInterval?: number // 每隔多少个周期
  specificDay?: number    // 每月的第几天，或每周的第几天
  nextDueDate: Date
  lastCompletedDate?: Date
  areaId: string
  createdAt: Date
}
```

**下次到期日期计算**:
```typescript
// 计算下一次截止日期
const calculateNextDueDate = (
  repeatRule: string,
  repeatInterval: number = 1,
  specificDay?: number,
  fromDate: Date = new Date()
): Date => {
  const nextDate = new Date(fromDate)

  switch (repeatRule) {
    case 'daily':
      nextDate.setDate(nextDate.getDate() + repeatInterval)
      break

    case 'weekly':
      if (specificDay !== undefined) {
        // 指定星期几 (0=周日, 1=周一, ...)
        const currentDay = nextDate.getDay()
        const daysUntilTarget = (specificDay - currentDay + 7) % 7
        nextDate.setDate(nextDate.getDate() + daysUntilTarget + (repeatInterval - 1) * 7)
      } else {
        nextDate.setDate(nextDate.getDate() + repeatInterval * 7)
      }
      break

    case 'monthly':
      if (specificDay !== undefined) {
        // 指定每月的第几天
        nextDate.setMonth(nextDate.getMonth() + repeatInterval)
        nextDate.setDate(specificDay)
      } else {
        nextDate.setMonth(nextDate.getMonth() + repeatInterval)
      }
      break

    case 'yearly':
      nextDate.setFullYear(nextDate.getFullYear() + repeatInterval)
      break
  }

  return nextDate
}

// 格式化重复规则显示
const formatRepeatRule = (task: RecurringTask, t: any): string => {
  const { repeatRule, repeatInterval = 1, specificDay } = task

  switch (repeatRule) {
    case 'daily':
      return repeatInterval === 1 ? t('common.daily') : t('common.everyNDays', { n: repeatInterval })
    case 'weekly':
      if (specificDay !== undefined) {
        const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
        return `每${repeatInterval === 1 ? '' : repeatInterval}周${dayNames[specificDay]}`
      }
      return repeatInterval === 1 ? t('common.weekly') : t('common.everyNWeeks', { n: repeatInterval })
    case 'monthly':
      if (specificDay !== undefined) {
        return `每${repeatInterval === 1 ? '' : repeatInterval}月${specificDay}日`
      }
      return repeatInterval === 1 ? t('common.monthly') : t('common.everyNMonths', { n: repeatInterval })
    case 'yearly':
      return repeatInterval === 1 ? t('common.yearly') : t('common.everyNYears', { n: repeatInterval })
    default:
      return repeatRule
  }
}
```

**任务管理组件**:
```typescript
const RecurringMaintenanceTasks = ({ areaId, onAddTask }) => {
  const [recurringTasks, setRecurringTasks] = useState<RecurringTask[]>([])
  const [loading, setLoading] = useState(true)

  // 从数据库加载任务
  const loadTasks = async () => {
    try {
      setLoading(true)
      const result = await databaseApi.getRecurringTasks(areaId)
      if (result.success) {
        setRecurringTasks(result.data || [])
      }
    } catch (error) {
      console.error('Failed to load recurring tasks:', error)
    } finally {
      setLoading(false)
    }
  }

  // 完成任务
  const handleCompleteTask = async (taskId: string) => {
    try {
      const task = recurringTasks.find(t => t.id === taskId)
      if (!task) return

      // 计算下次到期日期
      const nextDueDate = calculateNextDueDate(
        task.repeatRule,
        task.repeatInterval,
        task.specificDay,
        new Date()
      )

      // 更新任务
      const result = await databaseApi.updateRecurringTask({
        id: taskId,
        updates: {
          lastCompletedDate: new Date().toISOString(),
          nextDueDate: nextDueDate.toISOString()
        }
      })

      if (result.success) {
        setRecurringTasks(prev =>
          prev.map(t => t.id === taskId ? result.data : t)
        )

        addNotification({
          type: 'success',
          title: '任务已完成',
          message: `下次执行时间: ${nextDueDate.toLocaleDateString()}`
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '完成失败',
        message: error.message
      })
    }
  }

  // 任务排序：到期优先
  const sortedTasks = useMemo(() => {
    return [...recurringTasks].sort((a, b) => {
      const aDate = new Date(a.nextDueDate).getTime()
      const bDate = new Date(b.nextDueDate).getTime()
      return aDate - bDate
    })
  }, [recurringTasks])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <RotateCcw className="h-5 w-5" />
              定期维护任务
            </CardTitle>
            <CardDescription>
              管理领域的定期维护和检查任务
            </CardDescription>
          </div>
          <Button onClick={onAddTask}>
            <Plus className="h-4 w-4 mr-2" />
            添加任务
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {sortedTasks.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <div className="text-4xl mb-2">🔄</div>
            <p className="text-sm">暂无定期任务</p>
            <p className="text-xs mt-1">添加定期维护任务</p>
          </div>
        ) : (
          <div className="space-y-3">
            {sortedTasks.map((task) => (
              <div
                key={task.id}
                className="flex items-center gap-3 p-3 rounded-lg border hover:bg-accent/50 transition-colors"
              >
                <Checkbox
                  id={`task-${task.id}`}
                  onCheckedChange={() => handleCompleteTask(task.id)}
                />

                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <label
                      htmlFor={`task-${task.id}`}
                      className="text-sm font-medium cursor-pointer truncate"
                    >
                      {task.title}
                    </label>
                    <Badge variant="outline" className="text-xs">
                      {formatRepeatRule(task, t)}
                    </Badge>
                  </div>
                  {task.description && (
                    <p className="text-xs text-muted-foreground truncate">
                      {task.description}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground">
                    下次执行: {new Date(task.nextDueDate).toLocaleDateString()}
                  </p>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```

#### 7. 关联项目管理系统 (CompactProjectList)

**紧凑项目列表组件**:
```typescript
const CompactProjectList = ({ areaId, onCreateProject }) => {
  const { projects } = useProjectStore()
  const [loading, setLoading] = useState(false)

  // 获取领域关联的项目
  const areaProjects = useMemo(() => {
    return projects
      .filter(project => project.areaId === areaId && !project.archived)
      .sort((a, b) => {
        // 排序: 进行中 > 计划中 > 暂停 > 完成
        const statusPriority = {
          'In Progress': 1,
          'Not Started': 2,
          'At Risk': 3,
          'Paused': 4,
          'Completed': 5
        }

        const aPriority = statusPriority[a.status] || 999
        const bPriority = statusPriority[b.status] || 999

        if (aPriority !== bPriority) {
          return aPriority - bPriority
        }

        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      })
  }, [projects, areaId])

  // 项目状态统计
  const projectStats = useMemo(() => {
    const total = areaProjects.length
    const inProgress = areaProjects.filter(p => p.status === 'In Progress').length
    const completed = areaProjects.filter(p => p.status === 'Completed').length
    const notStarted = areaProjects.filter(p => p.status === 'Not Started').length

    return { total, inProgress, completed, notStarted }
  }, [areaProjects])

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FolderOpen className="h-5 w-5" />
              关联项目
              {projectStats.total > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {projectStats.total}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              管理此领域的相关项目
            </CardDescription>
          </div>
          <Button onClick={onCreateProject} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            新建项目
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {/* 项目统计 */}
        {projectStats.total > 0 && (
          <div className="grid grid-cols-3 gap-2 mb-4">
            <div className="text-center p-2 bg-blue-50 rounded-lg">
              <div className="text-lg font-bold text-blue-600">
                {projectStats.inProgress}
              </div>
              <div className="text-xs text-muted-foreground">进行中</div>
            </div>
            <div className="text-center p-2 bg-green-50 rounded-lg">
              <div className="text-lg font-bold text-green-600">
                {projectStats.completed}
              </div>
              <div className="text-xs text-muted-foreground">已完成</div>
            </div>
            <div className="text-center p-2 bg-gray-50 rounded-lg">
              <div className="text-lg font-bold text-gray-600">
                {projectStats.notStarted}
              </div>
              <div className="text-xs text-muted-foreground">未开始</div>
            </div>
          </div>
        )}

        {/* 项目列表 */}
        {areaProjects.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <div className="text-3xl mb-2">📋</div>
            <p className="text-sm">暂无关联项目</p>
            <p className="text-xs mt-1">创建项目来推进此领域</p>
          </div>
        ) : (
          <div className="space-y-2">
            {areaProjects.slice(0, 5).map(project => (
              <CompactProjectItem
                key={project.id}
                project={project}
                areaId={areaId}
              />
            ))}

            {areaProjects.length > 5 && (
              <div className="text-center pt-2">
                <Button variant="ghost" size="sm" asChild>
                  <Link to="/projects" state={{ areaFilter: areaId }}>
                    查看全部 {areaProjects.length} 个项目
                  </Link>
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  )
}

// 紧凑项目项组件
const CompactProjectItem = ({ project, areaId }) => {
  const { tasks } = useTaskStore()

  // 计算项目进度
  const projectTasks = tasks.filter(task => task.projectId === project.id)
  const completedTasks = projectTasks.filter(task => task.completed)
  const progress = projectTasks.length > 0
    ? Math.round((completedTasks.length / projectTasks.length) * 100)
    : 0

  return (
    <div className="p-3 border rounded-lg hover:bg-accent/50 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <Link
              to={`/projects/${project.id}`}
              state={{ from: 'area', areaId, areaName: project.areaName }}
              className="font-medium text-sm hover:text-primary transition-colors truncate"
            >
              {project.name}
            </Link>
            <Badge
              variant="outline"
              className={cn("text-xs", getStatusColor(project.status))}
            >
              {getStatusText(project.status)}
            </Badge>
          </div>

          {project.description && (
            <p className="text-xs text-muted-foreground line-clamp-1 mb-2">
              {project.description}
            </p>
          )}

          {/* 进度条 */}
          <div className="flex items-center gap-2">
            <Progress value={progress} className="h-1 flex-1" />
            <span className="text-xs text-muted-foreground">
              {progress}%
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
```

#### 8. 清单模板库系统 (CompactChecklistTemplates)

**清单模板数据结构**:
```typescript
interface ChecklistTemplate {
  id: string
  name: string
  description?: string
  template: string  // JSON格式存储清单项
  areaId: string
  createdAt: Date
  updatedAt?: Date
}

interface ChecklistItem {
  id: string
  text: string
  completed: boolean
  order: number
}

interface ChecklistInstance {
  id: string
  templateId: string
  status: string  // JSON格式存储实例状态
  createdAt: Date
  completedAt?: Date
}
```

**清单模板管理组件**:
```typescript
const CompactChecklistTemplates = ({ areaId, onCreateTemplate }) => {
  const [templates, setTemplates] = useState<ChecklistTemplate[]>([])
  const [instances, setInstances] = useState<ChecklistInstance[]>([])
  const [loading, setLoading] = useState(true)

  // 加载清单模板和实例
  const loadChecklistData = async () => {
    try {
      setLoading(true)
      const [templatesResult, instancesResult] = await Promise.all([
        databaseApi.getChecklistTemplates(areaId),
        databaseApi.getActiveChecklistInstances(areaId)
      ])

      if (templatesResult.success) {
        setTemplates(templatesResult.data || [])
      }

      if (instancesResult.success) {
        setInstances(instancesResult.data || [])
      }
    } catch (error) {
      console.error('Failed to load checklist data:', error)
    } finally {
      setLoading(false)
    }
  }

  // 创建清单实例
  const handleCreateInstance = async (templateId: string) => {
    try {
      const template = templates.find(t => t.id === templateId)
      if (!template) return

      const result = await databaseApi.createChecklistInstance({
        templateId,
        status: template.template // 复制模板状态
      })

      if (result.success) {
        setInstances(prev => [result.data, ...prev])
        addNotification({
          type: 'success',
          title: '清单已创建',
          message: `清单 "${template.name}" 实例已创建`
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '创建失败',
        message: error.message
      })
    }
  }

  // 解析清单模板
  const parseTemplate = (templateStr: string): ChecklistItem[] => {
    try {
      return JSON.parse(templateStr)
    } catch {
      return []
    }
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <CheckSquare className="h-5 w-5" />
              清单模板库
              {templates.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {templates.length}
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              管理可重复使用的清单模板
            </CardDescription>
          </div>
          <Button onClick={onCreateTemplate} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            新建模板
          </Button>
        </div>
      </CardHeader>

      <CardContent>
        {templates.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <div className="text-3xl mb-2">📋</div>
            <p className="text-sm">暂无清单模板</p>
            <p className="text-xs mt-1">创建可重复使用的清单模板</p>
          </div>
        ) : (
          <div className="space-y-3">
            {templates.map(template => {
              const templateItems = parseTemplate(template.template)
              const activeInstances = instances.filter(i => i.templateId === template.id)

              return (
                <div
                  key={template.id}
                  className="p-3 border rounded-lg hover:bg-accent/50 transition-colors"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate">
                        {template.name}
                      </h4>
                      {template.description && (
                        <p className="text-xs text-muted-foreground line-clamp-1">
                          {template.description}
                        </p>
                      )}
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleCreateInstance(template.id)}
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      使用
                    </Button>
                  </div>

                  {/* 模板预览 */}
                  <div className="text-xs text-muted-foreground">
                    <div className="flex items-center gap-2">
                      <span>{templateItems.length} 个项目</span>
                      {activeInstances.length > 0 && (
                        <Badge variant="secondary" className="text-xs">
                          {activeInstances.length} 个进行中
                        </Badge>
                      )}
                    </div>

                    {/* 显示前3个清单项 */}
                    {templateItems.slice(0, 3).map((item, index) => (
                      <div key={index} className="flex items-center gap-1 mt-1">
                        <div className="w-2 h-2 border border-gray-300 rounded-sm" />
                        <span className="truncate">{item.text}</span>
                      </div>
                    ))}

                    {templateItems.length > 3 && (
                      <div className="text-xs text-muted-foreground mt-1">
                        还有 {templateItems.length - 3} 个项目...
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```

#### 9. 进行中的清单系统 (ActiveChecklists)

**活跃清单管理组件**:
```typescript
const ActiveChecklists = ({ areaId }) => {
  const [activeInstances, setActiveInstances] = useState<ChecklistInstance[]>([])
  const [templates, setTemplates] = useState<ChecklistTemplate[]>([])
  const [loading, setLoading] = useState(true)

  // 加载活跃清单实例
  const loadActiveChecklists = async () => {
    try {
      setLoading(true)
      const [instancesResult, templatesResult] = await Promise.all([
        databaseApi.getActiveChecklistInstances(areaId),
        databaseApi.getChecklistTemplates(areaId)
      ])

      if (instancesResult.success) {
        setActiveInstances(instancesResult.data || [])
      }

      if (templatesResult.success) {
        setTemplates(templatesResult.data || [])
      }
    } catch (error) {
      console.error('Failed to load active checklists:', error)
    } finally {
      setLoading(false)
    }
  }

  // 更新清单项状态
  const handleToggleItem = async (instanceId: string, itemIndex: number) => {
    try {
      const instance = activeInstances.find(i => i.id === instanceId)
      if (!instance) return

      const status = JSON.parse(instance.status)
      status[itemIndex].completed = !status[itemIndex].completed

      const result = await databaseApi.updateChecklistInstance({
        id: instanceId,
        updates: { status: JSON.stringify(status) }
      })

      if (result.success) {
        setActiveInstances(prev =>
          prev.map(i => i.id === instanceId ? result.data : i)
        )
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '更新失败',
        message: error.message
      })
    }
  }

  // 完成整个清单
  const handleCompleteChecklist = async (instanceId: string) => {
    try {
      const result = await databaseApi.updateChecklistInstance({
        id: instanceId,
        updates: {
          completedAt: new Date().toISOString()
        }
      })

      if (result.success) {
        setActiveInstances(prev => prev.filter(i => i.id !== instanceId))
        addNotification({
          type: 'success',
          title: '清单已完成',
          message: '清单已标记为完成'
        })
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '完成失败',
        message: error.message
      })
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ListChecks className="h-5 w-5" />
          进行中的清单
          {activeInstances.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {activeInstances.length}
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          当前正在执行的清单任务
        </CardDescription>
      </CardHeader>

      <CardContent>
        {activeInstances.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground">
            <div className="text-3xl mb-2">✅</div>
            <p className="text-sm">暂无进行中的清单</p>
            <p className="text-xs mt-1">从模板库创建清单开始执行</p>
          </div>
        ) : (
          <div className="space-y-4">
            {activeInstances.map(instance => {
              const template = templates.find(t => t.id === instance.templateId)
              const status = JSON.parse(instance.status)
              const completedCount = status.filter(item => item.completed).length
              const totalCount = status.length
              const progress = Math.round((completedCount / totalCount) * 100)

              return (
                <div
                  key={instance.id}
                  className="p-4 border rounded-lg space-y-3"
                >
                  {/* 清单头部 */}
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium text-sm">
                        {template?.name || '未知清单'}
                      </h4>
                      <div className="flex items-center gap-2 mt-1">
                        <Progress value={progress} className="h-1 w-20" />
                        <span className="text-xs text-muted-foreground">
                          {completedCount}/{totalCount}
                        </span>
                      </div>
                    </div>

                    {progress === 100 && (
                      <Button
                        size="sm"
                        onClick={() => handleCompleteChecklist(instance.id)}
                      >
                        完成清单
                      </Button>
                    )}
                  </div>

                  {/* 清单项列表 */}
                  <div className="space-y-2">
                    {status.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center gap-2"
                      >
                        <Checkbox
                          checked={item.completed}
                          onCheckedChange={() => handleToggleItem(instance.id, index)}
                        />
                        <span
                          className={cn(
                            "text-sm flex-1",
                            item.completed && "line-through text-muted-foreground"
                          )}
                        >
                          {item.text}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
```

### 总结

领域管理模块作为PaoLife应用基于P.A.R.A.方法论的核心功能，具有以下特点：

1. **完整的生活领域管理**: 从创建到归档的全流程支持
2. **智能习惯追踪**: 月历热力图、连续天数、完成率统计
3. **灵活的KPI系统**: 支持数值型和布尔型指标管理
4. **定期维护任务**: 自动计算下次执行时间的循环任务
5. **多维度统计**: 项目关联、习惯完成率、指标达成情况
6. **优秀的用户体验**: 直观的可视化、实时反馈、便捷操作

这个设计为用户提供了专业级的生活领域管理能力，支持复杂的习惯养成和目标追踪。
```
```
