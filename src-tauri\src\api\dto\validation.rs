// 验证DTO定义
// 定义数据验证规则和验证器

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use regex::Regex;

/// 验证规则
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ValidationRule {
    pub field: String,
    pub rules: Vec<Rule>,
    pub message: Option<String>,
}

/// 具体验证规则
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "params")]
pub enum Rule {
    Required,
    MinLength(usize),
    MaxLength(usize),
    Pattern(String),
    Email,
    Url,
    Numeric,
    Integer,
    Range { min: f64, max: f64 },
    In(Vec<String>),
    NotIn(Vec<String>),
    Custom(String),
}

/// 验证结果
#[derive(Debug, Clone, Serialize)]
pub struct ValidationResult {
    pub valid: bool,
    pub errors: Vec<ValidationError>,
}

/// 验证错误
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>)]
pub struct ValidationError {
    pub field: String,
    pub rule: String,
    pub message: String,
    pub value: Option<serde_json::Value>,
}

/// 验证器
pub struct Validator {
    rules: Vec<ValidationRule>,
    email_regex: Regex,
    url_regex: Regex,
}

impl Validator {
    /// 创建新的验证器
    pub fn new() -> Self {
        let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
            .expect("Invalid email regex");
        let url_regex = Regex::new(r"^https?://[^\s/$.?#].[^\s]*$")
            .expect("Invalid URL regex");

        Self {
            rules: Vec::new(),
            email_regex,
            url_regex,
        }
    }

    /// 添加验证规则
    pub fn add_rule(mut self, rule: ValidationRule) -> Self {
        self.rules.push(rule);
        self
    }

    /// 验证数据
    pub fn validate(&self, data: &serde_json::Value) -> ValidationResult {
        let mut errors = Vec::new();

        for rule in &self.rules {
            let field_value = self.get_field_value(data, &rule.field);
            
            for validation_rule in &rule.rules {
                if let Some(error) = self.validate_rule(&rule.field, &field_value, validation_rule) {
                    errors.push(error);
                }
            }
        }

        ValidationResult {
            valid: errors.is_empty(),
            errors,
        }
    }

    /// 获取字段值
    fn get_field_value(&self, data: &serde_json::Value, field: &str) -> Option<serde_json::Value> {
        // 支持嵌套字段，如 "user.name"
        let parts: Vec<&str> = field.split('.').collect();
        let mut current = data;

        for part in parts {
            match current.get(part) {
                Some(value) => current = value,
                None => return None,
            }
        }

        Some(current.clone())
    }

    /// 验证单个规则
    fn validate_rule(
        &self,
        field: &str,
        value: &Option<serde_json::Value>,
        rule: &Rule,
    ) -> Option<ValidationError> {
        match rule {
            Rule::Required => {
                if value.is_none() || value.as_ref().unwrap().is_null() {
                    Some(ValidationError {
                        field: field.to_string(),
                        rule: "required".to_string(),
                        message: format!("{} 是必填字段", field),
                        value: value.clone(),
                    })
                } else {
                    None
                }
            }
            Rule::MinLength(min_len) => {
                if let Some(val) = value {
                    if let Some(s) = val.as_str() {
                        if s.len() < *min_len {
                            return Some(ValidationError {
                                field: field.to_string(),
                                rule: "min_length".to_string(),
                                message: format!("{} 至少需要 {} 个字符", field, min_len),
                                value: value.clone(),
                            });
                        }
                    }
                }
                None
            }
            Rule::MaxLength(max_len) => {
                if let Some(val) = value {
                    if let Some(s) = val.as_str() {
                        if s.len() > *max_len {
                            return Some(ValidationError {
                                field: field.to_string(),
                                rule: "max_length".to_string(),
                                message: format!("{} 不能超过 {} 个字符", field, max_len),
                                value: value.clone(),
                            });
                        }
                    }
                }
                None
            }
            Rule::Pattern(pattern) => {
                if let Some(val) = value {
                    if let Some(s) = val.as_str() {
                        if let Ok(regex) = Regex::new(pattern) {
                            if !regex.is_match(s) {
                                return Some(ValidationError {
                                    field: field.to_string(),
                                    rule: "pattern".to_string(),
                                    message: format!("{} 格式不正确", field),
                                    value: value.clone(),
                                });
                            }
                        }
                    }
                }
                None
            }
            Rule::Email => {
                if let Some(val) = value {
                    if let Some(s) = val.as_str() {
                        if !self.email_regex.is_match(s) {
                            return Some(ValidationError {
                                field: field.to_string(),
                                rule: "email".to_string(),
                                message: format!("{} 必须是有效的邮箱地址", field),
                                value: value.clone(),
                            });
                        }
                    }
                }
                None
            }
            Rule::Url => {
                if let Some(val) = value {
                    if let Some(s) = val.as_str() {
                        if !self.url_regex.is_match(s) {
                            return Some(ValidationError {
                                field: field.to_string(),
                                rule: "url".to_string(),
                                message: format!("{} 必须是有效的URL", field),
                                value: value.clone(),
                            });
                        }
                    }
                }
                None
            }
            Rule::Numeric => {
                if let Some(val) = value {
                    if !val.is_number() {
                        return Some(ValidationError {
                            field: field.to_string(),
                            rule: "numeric".to_string(),
                            message: format!("{} 必须是数字", field),
                            value: value.clone(),
                        });
                    }
                }
                None
            }
            Rule::Integer => {
                if let Some(val) = value {
                    if !val.is_i64() && !val.is_u64() {
                        return Some(ValidationError {
                            field: field.to_string(),
                            rule: "integer".to_string(),
                            message: format!("{} 必须是整数", field),
                            value: value.clone(),
                        });
                    }
                }
                None
            }
            Rule::Range { min, max } => {
                if let Some(val) = value {
                    if let Some(num) = val.as_f64() {
                        if num < *min || num > *max {
                            return Some(ValidationError {
                                field: field.to_string(),
                                rule: "range".to_string(),
                                message: format!("{} 必须在 {} 到 {} 之间", field, min, max),
                                value: value.clone(),
                            });
                        }
                    }
                }
                None
            }
            Rule::In(allowed_values) => {
                if let Some(val) = value {
                    if let Some(s) = val.as_str() {
                        if !allowed_values.contains(&s.to_string()) {
                            return Some(ValidationError {
                                field: field.to_string(),
                                rule: "in".to_string(),
                                message: format!("{} 必须是以下值之一: {}", field, allowed_values.join(", ")),
                                value: value.clone(),
                            });
                        }
                    }
                }
                None
            }
            Rule::NotIn(forbidden_values) => {
                if let Some(val) = value {
                    if let Some(s) = val.as_str() {
                        if forbidden_values.contains(&s.to_string()) {
                            return Some(ValidationError {
                                field: field.to_string(),
                                rule: "not_in".to_string(),
                                message: format!("{} 不能是以下值: {}", field, forbidden_values.join(", ")),
                                value: value.clone(),
                            });
                        }
                    }
                }
                None
            }
            Rule::Custom(_custom_rule) => {
                // 自定义验证规则需要额外实现
                None
            }
        }
    }
}

/// 预定义验证器
pub struct PredefinedValidators;

impl PredefinedValidators {
    /// 用户注册验证器
    pub fn user_registration() -> Validator {
        Validator::new()
            .add_rule(ValidationRule {
                field: "username".to_string(),
                rules: vec![
                    Rule::Required,
                    Rule::MinLength(3),
                    Rule::MaxLength(50),
                    Rule::Pattern(r"^[a-zA-Z0-9_]+$".to_string()),
                ],
                message: None,
            })
            .add_rule(ValidationRule {
                field: "email".to_string(),
                rules: vec![Rule::Email],
                message: None,
            })
            .add_rule(ValidationRule {
                field: "password".to_string(),
                rules: vec![
                    Rule::Required,
                    Rule::MinLength(8),
                    Rule::MaxLength(128),
                ],
                message: None,
            })
    }

    /// 项目创建验证器
    pub fn project_creation() -> Validator {
        Validator::new()
            .add_rule(ValidationRule {
                field: "name".to_string(),
                rules: vec![
                    Rule::Required,
                    Rule::MinLength(1),
                    Rule::MaxLength(200),
                ],
                message: None,
            })
            .add_rule(ValidationRule {
                field: "priority".to_string(),
                rules: vec![Rule::In(vec![
                    "low".to_string(),
                    "medium".to_string(),
                    "high".to_string(),
                    "urgent".to_string(),
                ])],
                message: None,
            })
            .add_rule(ValidationRule {
                field: "estimated_hours".to_string(),
                rules: vec![Rule::Range { min: 0.0, max: 10000.0 }],
                message: None,
            })
    }

    /// 任务创建验证器
    pub fn task_creation() -> Validator {
        Validator::new()
            .add_rule(ValidationRule {
                field: "title".to_string(),
                rules: vec![
                    Rule::Required,
                    Rule::MinLength(1),
                    Rule::MaxLength(200),
                ],
                message: None,
            })
            .add_rule(ValidationRule {
                field: "priority".to_string(),
                rules: vec![Rule::In(vec![
                    "low".to_string(),
                    "medium".to_string(),
                    "high".to_string(),
                    "urgent".to_string(),
                ])],
                message: None,
            })
            .add_rule(ValidationRule {
                field: "completion_percentage".to_string(),
                rules: vec![Rule::Range { min: 0.0, max: 100.0 }],
                message: None,
            })
    }

    /// 领域创建验证器
    pub fn area_creation() -> Validator {
        Validator::new()
            .add_rule(ValidationRule {
                field: "name".to_string(),
                rules: vec![
                    Rule::Required,
                    Rule::MinLength(1),
                    Rule::MaxLength(100),
                ],
                message: None,
            })
            .add_rule(ValidationRule {
                field: "color".to_string(),
                rules: vec![Rule::Pattern(r"^#[0-9A-Fa-f]{6}$".to_string())],
                message: None,
            })
    }

    /// 搜索验证器
    pub fn search() -> Validator {
        Validator::new()
            .add_rule(ValidationRule {
                field: "keyword".to_string(),
                rules: vec![
                    Rule::Required,
                    Rule::MinLength(1),
                    Rule::MaxLength(100),
                ],
                message: None,
            })
            .add_rule(ValidationRule {
                field: "entity_type".to_string(),
                rules: vec![Rule::In(vec![
                    "project".to_string(),
                    "task".to_string(),
                    "area".to_string(),
                    "user".to_string(),
                ])],
                message: None,
            })
    }
}

/// 验证工具类
pub struct ValidationUtils;

impl ValidationUtils {
    /// 验证ID格式
    pub fn validate_id(id: &str) -> Result<(), String> {
        if id.trim().is_empty() {
            return Err("ID不能为空".to_string());
        }
        if id.len() > 50 {
            return Err("ID长度不能超过50个字符".to_string());
        }
        // 简单的UUID格式验证
        if id.len() == 36 && id.chars().filter(|&c| c == '-').count() == 4 {
            Ok(())
        } else {
            Err("ID格式不正确".to_string())
        }
    }

    /// 验证分页参数
    pub fn validate_pagination(page: u32, page_size: u32) -> Result<(), String> {
        if page == 0 {
            return Err("页码必须大于0".to_string());
        }
        if page_size == 0 || page_size > 100 {
            return Err("页面大小必须在1-100之间".to_string());
        }
        Ok(())
    }

    /// 验证日期范围
    pub fn validate_date_range(
        start_date: Option<chrono::NaiveDate>,
        end_date: Option<chrono::NaiveDate>,
    ) -> Result<(), String> {
        if let (Some(start), Some(end)) = (start_date, end_date) {
            if start > end {
                return Err("开始日期不能晚于结束日期".to_string());
            }
        }
        Ok(())
    }

    /// 验证文件上传
    pub fn validate_file_upload(
        file_name: &str,
        file_size: u64,
        content_type: &str,
        max_size: u64,
        allowed_types: &[&str],
    ) -> Result<(), String> {
        if file_name.trim().is_empty() {
            return Err("文件名不能为空".to_string());
        }
        if file_size == 0 {
            return Err("文件大小不能为0".to_string());
        }
        if file_size > max_size {
            return Err(format!("文件大小不能超过{}字节", max_size));
        }
        if !allowed_types.contains(&content_type) {
            return Err("不支持的文件类型".to_string());
        }
        Ok(())
    }

    /// 清理和验证HTML内容
    pub fn sanitize_html(input: &str) -> String {
        // 简单的HTML清理，移除潜在的XSS攻击
        input
            .replace('<', "&lt;")
            .replace('>', "&gt;")
            .replace('"', "&quot;")
            .replace('\'', "&#x27;")
            .replace('&', "&amp;")
    }

    /// 验证密码强度
    pub fn validate_password_strength(password: &str) -> PasswordStrength {
        let mut score = 0;
        let mut feedback = Vec::new();

        // 长度检查
        if password.len() >= 8 {
            score += 1;
        } else {
            feedback.push("密码至少需要8个字符".to_string());
        }

        // 包含大写字母
        if password.chars().any(|c| c.is_uppercase()) {
            score += 1;
        } else {
            feedback.push("密码应包含大写字母".to_string());
        }

        // 包含小写字母
        if password.chars().any(|c| c.is_lowercase()) {
            score += 1;
        } else {
            feedback.push("密码应包含小写字母".to_string());
        }

        // 包含数字
        if password.chars().any(|c| c.is_numeric()) {
            score += 1;
        } else {
            feedback.push("密码应包含数字".to_string());
        }

        // 包含特殊字符
        if password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c)) {
            score += 1;
        } else {
            feedback.push("密码应包含特殊字符".to_string());
        }

        let level = match score {
            0..=2 => PasswordStrengthLevel::Weak,
            3..=4 => PasswordStrengthLevel::Medium,
            5 => PasswordStrengthLevel::Strong,
            _ => PasswordStrengthLevel::Strong,
        };

        PasswordStrength {
            score,
            level,
            feedback,
        }
    }
}

/// 密码强度
#[derive(Debug, Clone, Serialize)]
pub struct PasswordStrength {
    pub score: u8,
    pub level: PasswordStrengthLevel,
    pub feedback: Vec<String>,
}

/// 密码强度等级
#[derive(Debug, Clone, Serialize)]
pub enum PasswordStrengthLevel {
    Weak,
    Medium,
    Strong,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validator_required_rule() {
        let validator = Validator::new()
            .add_rule(ValidationRule {
                field: "name".to_string(),
                rules: vec![Rule::Required],
                message: None,
            });

        let valid_data = serde_json::json!({"name": "test"});
        let result = validator.validate(&valid_data);
        assert!(result.valid);

        let invalid_data = serde_json::json!({});
        let result = validator.validate(&invalid_data);
        assert!(!result.valid);
        assert_eq!(result.errors.len(), 1);
    }

    #[test]
    fn test_validator_email_rule() {
        let validator = Validator::new()
            .add_rule(ValidationRule {
                field: "email".to_string(),
                rules: vec![Rule::Email],
                message: None,
            });

        let valid_data = serde_json::json!({"email": "<EMAIL>"});
        let result = validator.validate(&valid_data);
        assert!(result.valid);

        let invalid_data = serde_json::json!({"email": "invalid-email"});
        let result = validator.validate(&invalid_data);
        assert!(!result.valid);
    }

    #[test]
    fn test_password_strength_validation() {
        let weak_password = "123";
        let strength = ValidationUtils::validate_password_strength(weak_password);
        assert!(matches!(strength.level, PasswordStrengthLevel::Weak));
        assert!(!strength.feedback.is_empty());

        let strong_password = "Password123!";
        let strength = ValidationUtils::validate_password_strength(strong_password);
        assert!(matches!(strength.level, PasswordStrengthLevel::Strong));
        assert!(strength.feedback.is_empty());
    }

    #[test]
    fn test_validation_utils() {
        assert!(ValidationUtils::validate_id("550e8400-e29b-41d4-a716-446655440000").is_ok());
        assert!(ValidationUtils::validate_id("").is_err());
        assert!(ValidationUtils::validate_id("invalid-id").is_err());

        assert!(ValidationUtils::validate_pagination(1, 20).is_ok());
        assert!(ValidationUtils::validate_pagination(0, 20).is_err());
        assert!(ValidationUtils::validate_pagination(1, 0).is_err());

        let sanitized = ValidationUtils::sanitize_html("<script>alert('xss')</script>");
        assert!(!sanitized.contains("<script>"));
    }
}
