// 服务容器
// 实现依赖注入和服务管理

use crate::application::services::{
    UserService, ProjectService, TaskService, AreaService, 
    AuthService, AnalyticsService
};
use crate::domain::repositories::{
    user_repository::UserRepository,
    project_repository::ProjectRepository,
    task_repository::TaskRepository,
    area_repository::AreaRepository,
};
use crate::infrastructure::database::{
    repositories::{
        UserRepositoryImpl, ProjectRepositoryImpl, 
        TaskRepositoryImpl, AreaRepositoryImpl
    },
    DatabaseConnection
};
use crate::shared::errors::{AppError, AppResult};
use std::sync::Arc;
use sqlx::SqlitePool;

/// 服务容器
/// 管理所有应用服务的生命周期和依赖关系
pub struct ServiceContainer {
    // 数据库连接
    db_pool: SqlitePool,
    
    // 仓储层
    user_repository: Arc<dyn UserRepository>,
    project_repository: Arc<dyn ProjectRepository>,
    task_repository: Arc<dyn TaskRepository>,
    area_repository: Arc<dyn AreaRepository>,
    
    // 服务层
    user_service: Arc<UserService>,
    project_service: Arc<ProjectService>,
    task_service: Arc<TaskService>,
    area_service: Arc<AreaService>,
    auth_service: Arc<AuthService>,
    analytics_service: Arc<AnalyticsService>,
}

impl ServiceContainer {
    /// 创建新的服务容器
    pub async fn new(database_url: &str, jwt_secret: String) -> AppResult<Self> {
        // 初始化数据库连接
        let db_connection = DatabaseConnection::new(database_url).await?;
        let db_pool = db_connection.pool().clone();

        // 运行数据库迁移
        db_connection.migrate().await?;

        // 创建仓储层实例
        let user_repository: Arc<dyn UserRepository> = 
            Arc::new(UserRepositoryImpl::new(db_pool.clone()));
        let project_repository: Arc<dyn ProjectRepository> = 
            Arc::new(ProjectRepositoryImpl::new(db_pool.clone()));
        let task_repository: Arc<dyn TaskRepository> = 
            Arc::new(TaskRepositoryImpl::new(db_pool.clone()));
        let area_repository: Arc<dyn AreaRepository> = 
            Arc::new(AreaRepositoryImpl::new(db_pool.clone()));

        // 创建服务层实例
        let user_service = Arc::new(UserService::new(
            user_repository.clone(),
        ));

        let project_service = Arc::new(ProjectService::new(
            project_repository.clone(),
            area_repository.clone(),
            user_repository.clone(),
        ));

        let task_service = Arc::new(TaskService::new(
            task_repository.clone(),
            project_repository.clone(),
            area_repository.clone(),
            user_repository.clone(),
        ));

        let area_service = Arc::new(AreaService::new(
            area_repository.clone(),
            user_repository.clone(),
        ));

        let auth_service = Arc::new(AuthService::new(
            user_repository.clone(),
            jwt_secret,
        ));

        let analytics_service = Arc::new(AnalyticsService::new(
            project_repository.clone(),
            task_repository.clone(),
            area_repository.clone(),
            user_repository.clone(),
        ));

        Ok(Self {
            db_pool,
            user_repository,
            project_repository,
            task_repository,
            area_repository,
            user_service,
            project_service,
            task_service,
            area_service,
            auth_service,
            analytics_service,
        })
    }

    /// 获取数据库连接池
    pub fn db_pool(&self) -> &SqlitePool {
        &self.db_pool
    }

    /// 获取用户仓储
    pub fn user_repository(&self) -> Arc<dyn UserRepository> {
        self.user_repository.clone()
    }

    /// 获取项目仓储
    pub fn project_repository(&self) -> Arc<dyn ProjectRepository> {
        self.project_repository.clone()
    }

    /// 获取任务仓储
    pub fn task_repository(&self) -> Arc<dyn TaskRepository> {
        self.task_repository.clone()
    }

    /// 获取领域仓储
    pub fn area_repository(&self) -> Arc<dyn AreaRepository> {
        self.area_repository.clone()
    }

    /// 获取用户服务
    pub fn user_service(&self) -> Arc<UserService> {
        self.user_service.clone()
    }

    /// 获取项目服务
    pub fn project_service(&self) -> Arc<ProjectService> {
        self.project_service.clone()
    }

    /// 获取任务服务
    pub fn task_service(&self) -> Arc<TaskService> {
        self.task_service.clone()
    }

    /// 获取领域服务
    pub fn area_service(&self) -> Arc<AreaService> {
        self.area_service.clone()
    }

    /// 获取认证服务
    pub fn auth_service(&self) -> Arc<AuthService> {
        self.auth_service.clone()
    }

    /// 获取分析服务
    pub fn analytics_service(&self) -> Arc<AnalyticsService> {
        self.analytics_service.clone()
    }

    /// 健康检查
    pub async fn health_check(&self) -> AppResult<HealthStatus> {
        let mut status = HealthStatus::new();

        // 检查数据库连接
        match sqlx::query("SELECT 1").fetch_one(&self.db_pool).await {
            Ok(_) => {
                status.database = ComponentStatus::Healthy;
            }
            Err(e) => {
                status.database = ComponentStatus::Unhealthy(e.to_string());
                status.overall = OverallStatus::Unhealthy;
            }
        }

        // 检查各个服务
        status.services.insert("user_service".to_string(), ComponentStatus::Healthy);
        status.services.insert("project_service".to_string(), ComponentStatus::Healthy);
        status.services.insert("task_service".to_string(), ComponentStatus::Healthy);
        status.services.insert("area_service".to_string(), ComponentStatus::Healthy);
        status.services.insert("auth_service".to_string(), ComponentStatus::Healthy);
        status.services.insert("analytics_service".to_string(), ComponentStatus::Healthy);

        Ok(status)
    }

    /// 关闭服务容器
    pub async fn shutdown(&self) -> AppResult<()> {
        tracing::info!("Shutting down service container...");
        
        // 关闭数据库连接池
        self.db_pool.close().await;
        
        tracing::info!("Service container shutdown completed");
        Ok(())
    }
}

/// 健康状态
#[derive(Debug, Clone)]
pub struct HealthStatus {
    pub overall: OverallStatus,
    pub database: ComponentStatus,
    pub services: std::collections::HashMap<String, ComponentStatus>,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl HealthStatus {
    fn new() -> Self {
        Self {
            overall: OverallStatus::Healthy,
            database: ComponentStatus::Unknown,
            services: std::collections::HashMap::new(),
            timestamp: chrono::Utc::now(),
        }
    }
}

/// 整体状态
#[derive(Debug, Clone)]
pub enum OverallStatus {
    Healthy,
    Degraded,
    Unhealthy,
}

/// 组件状态
#[derive(Debug, Clone)]
pub enum ComponentStatus {
    Healthy,
    Degraded(String),
    Unhealthy(String),
    Unknown,
}

/// 服务配置
#[derive(Debug, Clone)]
pub struct ServiceConfig {
    pub database_url: String,
    pub jwt_secret: String,
    pub jwt_access_token_duration_hours: i64,
    pub jwt_refresh_token_duration_days: i64,
    pub bcrypt_cost: u32,
    pub max_login_attempts: u32,
    pub login_attempt_window_minutes: i64,
}

impl Default for ServiceConfig {
    fn default() -> Self {
        Self {
            database_url: "sqlite:./data/paolife.db".to_string(),
            jwt_secret: "your-secret-key".to_string(),
            jwt_access_token_duration_hours: 1,
            jwt_refresh_token_duration_days: 30,
            bcrypt_cost: 12,
            max_login_attempts: 5,
            login_attempt_window_minutes: 15,
        }
    }
}

impl ServiceConfig {
    /// 从环境变量加载配置
    pub fn from_env() -> Self {
        Self {
            database_url: std::env::var("DATABASE_URL")
                .unwrap_or_else(|_| "sqlite:./data/paolife.db".to_string()),
            jwt_secret: std::env::var("JWT_SECRET")
                .unwrap_or_else(|_| "your-secret-key".to_string()),
            jwt_access_token_duration_hours: std::env::var("JWT_ACCESS_TOKEN_DURATION_HOURS")
                .unwrap_or_else(|_| "1".to_string())
                .parse()
                .unwrap_or(1),
            jwt_refresh_token_duration_days: std::env::var("JWT_REFRESH_TOKEN_DURATION_DAYS")
                .unwrap_or_else(|_| "30".to_string())
                .parse()
                .unwrap_or(30),
            bcrypt_cost: std::env::var("BCRYPT_COST")
                .unwrap_or_else(|_| "12".to_string())
                .parse()
                .unwrap_or(12),
            max_login_attempts: std::env::var("MAX_LOGIN_ATTEMPTS")
                .unwrap_or_else(|_| "5".to_string())
                .parse()
                .unwrap_or(5),
            login_attempt_window_minutes: std::env::var("LOGIN_ATTEMPT_WINDOW_MINUTES")
                .unwrap_or_else(|_| "15".to_string())
                .parse()
                .unwrap_or(15),
        }
    }

    /// 验证配置
    pub fn validate(&self) -> AppResult<()> {
        if self.jwt_secret.len() < 32 {
            return Err(AppError::configuration("JWT密钥长度至少需要32个字符"));
        }

        if self.jwt_access_token_duration_hours < 1 || self.jwt_access_token_duration_hours > 24 {
            return Err(AppError::configuration("访问令牌有效期应在1-24小时之间"));
        }

        if self.jwt_refresh_token_duration_days < 1 || self.jwt_refresh_token_duration_days > 365 {
            return Err(AppError::configuration("刷新令牌有效期应在1-365天之间"));
        }

        if self.bcrypt_cost < 10 || self.bcrypt_cost > 15 {
            return Err(AppError::configuration("Bcrypt成本应在10-15之间"));
        }

        Ok(())
    }
}

/// 服务容器构建器
pub struct ServiceContainerBuilder {
    config: ServiceConfig,
}

impl ServiceContainerBuilder {
    /// 创建新的构建器
    pub fn new() -> Self {
        Self {
            config: ServiceConfig::default(),
        }
    }

    /// 使用配置
    pub fn with_config(mut self, config: ServiceConfig) -> Self {
        self.config = config;
        self
    }

    /// 设置数据库URL
    pub fn with_database_url(mut self, url: String) -> Self {
        self.config.database_url = url;
        self
    }

    /// 设置JWT密钥
    pub fn with_jwt_secret(mut self, secret: String) -> Self {
        self.config.jwt_secret = secret;
        self
    }

    /// 构建服务容器
    pub async fn build(self) -> AppResult<ServiceContainer> {
        // 验证配置
        self.config.validate()?;

        // 创建服务容器
        ServiceContainer::new(&self.config.database_url, self.config.jwt_secret).await
    }
}

impl Default for ServiceContainerBuilder {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_service_config_validation() {
        let mut config = ServiceConfig::default();
        
        // 有效配置
        config.jwt_secret = "a".repeat(32);
        assert!(config.validate().is_ok());

        // 无效JWT密钥
        config.jwt_secret = "short".to_string();
        assert!(config.validate().is_err());

        // 无效访问令牌有效期
        config.jwt_secret = "a".repeat(32);
        config.jwt_access_token_duration_hours = 0;
        assert!(config.validate().is_err());
    }

    #[tokio::test]
    async fn test_service_container_builder() {
        let config = ServiceConfig {
            database_url: "sqlite::memory:".to_string(),
            jwt_secret: "a".repeat(32),
            ..Default::default()
        };

        let result = ServiceContainerBuilder::new()
            .with_config(config)
            .build()
            .await;

        // 在测试环境中可能会失败，因为需要实际的数据库
        // 这里只测试构建器的API
        assert!(result.is_ok() || result.is_err());
    }
}
