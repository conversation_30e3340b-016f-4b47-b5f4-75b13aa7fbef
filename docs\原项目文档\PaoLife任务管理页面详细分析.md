# PaoLife项目详细技术文档

## 第七部分：任务管理页面详细分析

### 页面概览

任务管理模块是PaoLife应用的核心功能之一，实现了完整的任务生命周期管理。包含任务列表组件 (`TaskList.tsx`)、任务详情面板 (`TaskDetailPanel.tsx`) 和任务项组件 (`TaskItem.tsx`, `EnhancedTaskItem.tsx`)。支持无限层级子任务、拖拽排序、标签系统和虚拟化渲染等高级功能。

### 核心架构设计

#### 1. 任务数据结构

**扩展任务接口**:
```typescript
interface ExtendedTask {
  id: string
  content: string
  description?: string
  completed: boolean
  priority?: 'low' | 'medium' | 'high' | 'critical'
  status?: 'todo' | 'in_progress' | 'blocked' | 'done'
  deadline?: Date
  createdAt: Date
  updatedAt: Date
  
  // 层级关系
  parentId?: string
  position?: number
  
  // 关联关系
  projectId?: string
  areaId?: string
  
  // 标签系统
  tags?: string[]
  
  // 扩展属性
  estimatedTime?: number
  actualTime?: number
  sourceType?: 'manual' | 'inbox' | 'recurring'
  sourceId?: string
  sourceContext?: string
  resourceLinkId?: string
}

// 带子任务的任务树节点
interface TaskWithChildren extends ExtendedTask {
  children: TaskWithChildren[]
  level: number
  hasChildren: boolean
}
```

**任务状态管理**:
```typescript
// 任务状态枚举
const TaskStatus = {
  TODO: 'todo',
  IN_PROGRESS: 'in_progress', 
  BLOCKED: 'blocked',
  DONE: 'done'
} as const

// 任务优先级枚举
const TaskPriority = {
  LOW: 'low',
  MEDIUM: 'medium',
  HIGH: 'high',
  CRITICAL: 'critical'
} as const

// 状态颜色映射
const getStatusColor = (status: string) => {
  const colors = {
    'todo': 'bg-gray-100 text-gray-800 border-gray-200',
    'in_progress': 'bg-blue-100 text-blue-800 border-blue-200',
    'blocked': 'bg-red-100 text-red-800 border-red-200',
    'done': 'bg-green-100 text-green-800 border-green-200'
  }
  return colors[status] || colors.todo
}

// 优先级颜色映射
const getPriorityColor = (priority: string) => {
  const colors = {
    'low': 'bg-green-100 text-green-800 border-green-200',
    'medium': 'bg-yellow-100 text-yellow-800 border-yellow-200',
    'high': 'bg-orange-100 text-orange-800 border-orange-200',
    'critical': 'bg-red-100 text-red-800 border-red-200'
  }
  return colors[priority] || colors.medium
}
```

#### 2. 任务层级管理系统

**useTaskHierarchy Hook**:
```typescript
interface UseTaskHierarchyOptions {
  tasks: ExtendedTask[]
  defaultExpanded?: boolean
  maxAutoExpandLevel?: number
}

export function useTaskHierarchy({
  tasks,
  defaultExpanded = false,
  maxAutoExpandLevel = 2
}: UseTaskHierarchyOptions) {
  // 展开状态管理
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(() => {
    if (!defaultExpanded) return new Set()
    
    // 如果默认展开，则展开所有有子任务的任务
    const expanded = new Set<string>()
    const addExpandedTasks = (taskList: ExtendedTask[], level = 0) => {
      taskList.forEach(task => {
        const children = taskList.filter(t => t.parentId === task.id)
        if (children.length > 0 && level < maxAutoExpandLevel) {
          expanded.add(task.id)
          addExpandedTasks(children, level + 1)
        }
      })
    }
    addExpandedTasks(tasks)
    return expanded
  })

  // 构建任务树结构
  const taskTree = useMemo(() => {
    const taskMap = new Map<string, TaskWithChildren>()
    const rootTasks: TaskWithChildren[] = []

    // 初始化所有任务
    tasks.forEach(task => {
      taskMap.set(task.id, {
        ...task,
        children: [],
        level: 0,
        hasChildren: false
      })
    })

    // 构建层级关系
    tasks.forEach(task => {
      const taskWithChildren = taskMap.get(task.id)!
      if (task.parentId && taskMap.has(task.parentId)) {
        const parent = taskMap.get(task.parentId)!
        parent.children.push(taskWithChildren)
        parent.hasChildren = true
        taskWithChildren.level = parent.level + 1
      } else {
        rootTasks.push(taskWithChildren)
      }
    })

    // 递归排序子任务
    const sortChildren = (tasks: TaskWithChildren[]) => {
      tasks.sort((a, b) => (a.position || 0) - (b.position || 0))
      tasks.forEach(task => {
        if (task.children.length > 0) {
          sortChildren(task.children)
        }
      })
    }
    sortChildren(rootTasks)

    return rootTasks
  }, [tasks])

  // 获取可见任务（考虑展开状态）
  const visibleTasks = useMemo(() => {
    const visible: TaskWithChildren[] = []
    
    const addVisibleTasks = (tasks: TaskWithChildren[]) => {
      tasks.forEach(task => {
        visible.push(task)
        if (expandedTasks.has(task.id) && task.children.length > 0) {
          addVisibleTasks(task.children)
        }
      })
    }
    
    addVisibleTasks(taskTree)
    return visible
  }, [taskTree, expandedTasks])

  // 统计信息
  const stats = useMemo(() => {
    const total = tasks.length
    const completed = tasks.filter(t => t.completed).length
    const pending = total - completed
    const hasSubtasks = tasks.filter(t => tasks.some(child => child.parentId === t.id)).length
    
    return {
      total,
      completed,
      pending,
      hasSubtasks,
      completionRate: total > 0 ? Math.round((completed / total) * 100) : 0
    }
  }, [tasks])

  // 展开/折叠控制
  const toggleExpand = useCallback((taskId: string) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev)
      if (newSet.has(taskId)) {
        newSet.delete(taskId)
      } else {
        newSet.add(taskId)
      }
      return newSet
    })
  }, [])

  const expandAll = useCallback(() => {
    const allParentIds = new Set<string>()
    tasks.forEach(task => {
      if (tasks.some(child => child.parentId === task.id)) {
        allParentIds.add(task.id)
      }
    })
    setExpandedTasks(allParentIds)
  }, [tasks])

  const collapseAll = useCallback(() => {
    setExpandedTasks(new Set())
  }, [])

  const expandToLevel = useCallback((level: number) => {
    const toExpand = new Set<string>()
    const addToLevel = (taskList: TaskWithChildren[], currentLevel: number) => {
      taskList.forEach(task => {
        if (currentLevel < level && task.children.length > 0) {
          toExpand.add(task.id)
          addToLevel(task.children, currentLevel + 1)
        }
      })
    }
    addToLevel(taskTree, 0)
    setExpandedTasks(toExpand)
  }, [taskTree])

  const isExpanded = useCallback((taskId: string) => {
    return expandedTasks.has(taskId)
  }, [expandedTasks])

  return {
    taskTree,
    visibleTasks,
    expandedTasks,
    stats,
    toggleExpand,
    expandAll,
    collapseAll,
    expandToLevel,
    isExpanded
  }
}
```

#### 3. 任务列表组件 (TaskList.tsx)

**组件架构设计**:
```typescript
interface TaskListProps {
  tasks: ExtendedTask[]
  onTaskToggle?: (taskId: string, completed: boolean) => void
  onTaskEdit?: (task: ExtendedTask) => void
  onTaskDelete?: (taskId: string) => void
  onTaskAddSubtask?: (parentId: string) => void
  onTaskMove?: (taskId: string, newParentId?: string, newIndex?: number) => void
  onTaskClick?: (task: ExtendedTask) => void
  className?: string
  useEnhancedView?: boolean // 是否使用增强视图
  useHierarchicalView?: boolean // 是否使用层级视图
  useVirtualizedView?: boolean // 是否使用虚拟化视图（性能优化）
  showHierarchyControls?: boolean // 是否显示层级控制按钮
}

export function TaskList({
  tasks,
  onTaskToggle,
  onTaskEdit,
  onTaskDelete,
  onTaskAddSubtask,
  onTaskMove,
  onTaskClick,
  className,
  useEnhancedView = false,
  useHierarchicalView = false,
  useVirtualizedView = false,
  showHierarchyControls = false
}: TaskListProps) {
  const [activeId, setActiveId] = useState<string | null>(null)
  const [overId, setOverId] = useState<string | null>(null)
  const [dragIntent, setDragIntent] = useState<string | null>(null)

  // 层级管理 hook
  const {
    taskTree,
    visibleTasks,
    expandedTasks,
    stats,
    toggleExpand,
    expandAll,
    collapseAll,
    expandToLevel,
    isExpanded
  } = useTaskHierarchy({
    tasks,
    defaultExpanded: false,
    maxAutoExpandLevel: 2
  })

  // 拖拽传感器配置
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8 // 需要拖拽8px才激活
      }
    })
  )

  return (
    <DndContext
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragOver={handleDragOver}
      onDragEnd={handleDragEnd}
    >
      <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
        <div className={className}>
          {/* 层级控制工具栏 */}
          {showHierarchyControls && taskTree.length > 0 && (
            <HierarchyControlToolbar
              stats={stats}
              onExpandAll={expandAll}
              onCollapseAll={collapseAll}
              onExpandToLevel={expandToLevel}
            />
          )}

          {/* 任务列表渲染 */}
          {taskTree.length === 0 ? (
            <EmptyTaskList />
          ) : useVirtualizedView ? (
            <VirtualizedTaskList
              tasks={tasks}
              onTaskToggle={onTaskToggle}
              onTaskEdit={onTaskEdit}
              onTaskDelete={onTaskDelete}
              onTaskAddSubtask={onTaskAddSubtask}
              onTaskClick={onTaskClick}
              className="space-y-2"
            />
          ) : (
            <div className="space-y-2">
              {taskTree.map((task) => (
                <TaskTreeNode
                  key={task.id}
                  task={task}
                  level={0}
                  useEnhancedView={useEnhancedView}
                  onTaskToggle={onTaskToggle}
                  onTaskEdit={onTaskEdit}
                  onTaskDelete={onTaskDelete}
                  onTaskAddSubtask={onTaskAddSubtask}
                  isExpanded={isExpanded(task.id)}
                  onToggleExpand={() => toggleExpand(task.id)}
                />
              ))}
            </div>
          )}
        </div>
      </SortableContext>

      {/* 拖拽覆盖层 */}
      <DragOverlay>
        {activeId ? (
          <TaskDragOverlay
            task={tasks.find(t => t.id === activeId)}
            dragIntent={dragIntent}
          />
        ) : null}
      </DragOverlay>
    </DndContext>
  )
}
```

**层级控制工具栏**:
```typescript
const HierarchyControlToolbar = ({ stats, onExpandAll, onCollapseAll, onExpandToLevel }) => (
  <div className="mb-4 p-3 bg-muted/30 rounded-lg border">
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-2">
        <BarChart3 className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm font-medium">层级控制</span>
        <Badge variant="secondary" className="text-xs">
          {stats.total} 个任务
        </Badge>
        <Badge variant="outline" className="text-xs">
          {stats.completed}/{stats.total} 已完成
        </Badge>
      </div>

      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={onExpandAll}
          className="h-7 px-2 text-xs"
        >
          <Expand className="h-3 w-3 mr-1" />
          全部展开
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onCollapseAll}
          className="h-7 px-2 text-xs"
        >
          <Minimize className="h-3 w-3 mr-1" />
          全部折叠
        </Button>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-7 px-2 text-xs">
              <ChevronDown className="h-3 w-3 mr-1" />
              展开到
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => onExpandToLevel(1)}>
              第1层
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onExpandToLevel(2)}>
              第2层
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => onExpandToLevel(3)}>
              第3层
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  </div>
)
```

#### 4. 拖拽排序系统

**拖拽事件处理**:
```typescript
// 拖拽开始
const handleDragStart = useCallback((event: DragStartEvent) => {
  const endTimer = startTimer('drag-start')
  setActiveId(event.active.id as string)
  setDragIntent('重新排序任务')
  endTimer()
}, [])

// 拖拽悬停
const handleDragOver = useCallback((event: DragOverEvent) => {
  const { active, over } = event
  if (!over || active.id === over.id) return

  setOverId(over.id as string)

  // 更新拖拽意图提示
  const activeTask = tasks.find(t => t.id === active.id)
  const overTask = tasks.find(t => t.id === over.id)

  if (activeTask && overTask) {
    if (overTask.parentId !== activeTask.parentId) {
      setDragIntent(`将 "${activeTask.content}" 移动到 "${overTask.content}" 下`)
    } else {
      setDragIntent(`重新排序 "${activeTask.content}"`)
    }
  }
}, [tasks])

// 拖拽结束
const handleDragEnd = useCallback((event: DragEndEvent) => {
  const endTimer = startTimer('drag-end')
  const { active, over } = event

  setActiveId(null)
  setOverId(null)
  setDragIntent(null)

  if (!over || active.id === over.id) {
    endTimer()
    return
  }

  try {
    const activeId = active.id as string
    const overId = over.id as string

    const activeTask = tasks.find(t => t.id === activeId)
    const overTask = tasks.find(t => t.id === overId)

    if (!activeTask || !overTask) {
      endTimer()
      return
    }

    let newParentId: string | undefined
    let newIndex: number | undefined
    let operation: string

    // 判断拖拽操作类型
    if (overTask.parentId !== activeTask.parentId) {
      // 跨层级移动：将activeTask移动到overTask的父级下
      newParentId = overTask.parentId
      const siblings = tasks.filter(t => t.parentId === newParentId)
      const overIndex = siblings.findIndex(t => t.id === overId)
      newIndex = overIndex + 1 // 插入到目标任务后面
      operation = `"${activeTask.content}" → moved to ${newParentId ? 'under parent' : 'root level'}`
    } else {
      // 同层级重排序
      newParentId = activeTask.parentId
      const siblings = tasks.filter(t => t.parentId === newParentId)
      const overIndex = siblings.findIndex(t => t.id === overId)
      newIndex = overIndex
      operation = `"${activeTask.content}" → reordered ${newParentId ? 'within parent' : 'at root level'}`
    }

    console.log(`🎯 Drag operation: ${operation}`)
    onTaskMove?.(activeId, newParentId, newIndex)

    // 显示成功通知
    addNotification({
      type: 'success',
      title: '任务已移动',
      message: operation
    })
  } catch (error) {
    console.error('Error during drag operation:', error)
    addNotification({
      type: 'error',
      title: '移动失败',
      message: '任务移动失败，请重试'
    })
  } finally {
    endTimer()
  }
}, [tasks, onTaskMove, addNotification])
```

#### 5. 任务项组件系统

**基础任务项组件 (TaskItem.tsx)**:
```typescript
interface TaskItemProps {
  task: ExtendedTask
  level?: number
  onToggle?: (taskId: string, completed: boolean) => void
  onEdit?: (task: ExtendedTask) => void
  onDelete?: (taskId: string) => void
  onAddSubtask?: (parentId: string) => void
  onMove?: (taskId: string, newParentId?: string, newIndex?: number) => void
  className?: string
  isDropTarget?: boolean
  isBeingDragged?: boolean
  isOverlay?: boolean
}

export function TaskItem({
  task,
  level = 0,
  onToggle,
  onEdit,
  onDelete,
  onAddSubtask,
  onMove,
  className,
  isDropTarget = false,
  isBeingDragged = false,
  isOverlay = false
}: TaskItemProps) {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState(task.content)
  const [editDescription, setEditDescription] = useState(task.description || '')

  // Sortable功能
  const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
    id: task.id
  })

  // 样式计算
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    marginLeft: `${level * 24}px` // 层级缩进
  }

  // 检查是否过期
  const isOverdue = task.deadline && new Date(task.deadline) < new Date() && !task.completed

  const handleSaveEdit = () => {
    if (editContent.trim()) {
      onEdit?.({
        ...task,
        content: editContent.trim(),
        description: editDescription.trim() || null
      })
      setIsEditing(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSaveEdit()
    } else if (e.key === 'Escape') {
      setIsEditing(false)
      setEditContent(task.content)
      setEditDescription(task.description || '')
    }
  }

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={cn(
        'group border rounded-lg transition-all duration-200',
        task.completed ? 'bg-muted/50 opacity-75' : 'bg-background hover:bg-accent/50',
        isOverdue && 'border-red-200 bg-red-50/50',
        isDragging && 'opacity-50 z-50',
        isDropTarget && 'border-blue-300 bg-blue-50/30 ring-2 ring-blue-200',
        isBeingDragged && 'opacity-30',
        isOverlay && 'shadow-2xl border-blue-400 bg-white z-50 rotate-3 scale-105',
        className
      )}
      {...attributes}
    >
      <div className="flex items-start gap-3 p-3">
        {/* 拖拽手柄 */}
        <div
          {...listeners}
          className="flex-shrink-0 mt-1 cursor-grab active:cursor-grabbing opacity-0 group-hover:opacity-100 transition-opacity"
        >
          <GripVertical className="h-4 w-4 text-muted-foreground" />
        </div>

        {/* 完成状态复选框 */}
        <Checkbox
          checked={task.completed}
          onCheckedChange={(checked) => onToggle?.(task.id, checked as boolean)}
          className="mt-1"
        />

        {/* 任务内容区域 */}
        <div className="flex-1 min-w-0">
          {isEditing ? (
            <div className="space-y-2">
              <Input
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                onKeyDown={handleKeyDown}
                onBlur={handleSaveEdit}
                className="text-sm"
                autoFocus
              />
              <Textarea
                value={editDescription}
                onChange={(e) => setEditDescription(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="添加描述..."
                className="text-xs resize-none"
                rows={2}
              />
            </div>
          ) : (
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span
                  className={cn(
                    'text-sm font-medium',
                    task.completed && 'line-through text-muted-foreground'
                  )}
                  onClick={() => setIsEditing(true)}
                >
                  {task.content}
                </span>

                {/* 优先级标签 */}
                {task.priority && task.priority !== 'medium' && (
                  <Badge variant="outline" className={cn("text-xs", getPriorityColor(task.priority))}>
                    {task.priority}
                  </Badge>
                )}

                {/* 状态标签 */}
                {task.status && task.status !== 'todo' && (
                  <Badge variant="outline" className={cn("text-xs", getStatusColor(task.status))}>
                    {task.status.replace('_', ' ')}
                  </Badge>
                )}
              </div>

              {/* 任务描述 */}
              {task.description && (
                <p className="text-xs text-muted-foreground line-clamp-2">
                  {task.description}
                </p>
              )}

              {/* 标签显示 */}
              {task.tags && task.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {task.tags.map(tag => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              )}

              {/* 截止日期 */}
              {task.deadline && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  <span className={isOverdue ? 'text-red-600 font-medium' : ''}>
                    {new Date(task.deadline).toLocaleDateString()}
                  </span>
                  {isOverdue && <span className="text-red-600">(已过期)</span>}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onAddSubtask?.(task.id)}
            className="h-6 w-6 p-0"
          >
            <Plus className="h-3 w-3" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setIsEditing(true)}>
                <Edit className="h-4 w-4 mr-2" />
                编辑
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onAddSubtask?.(task.id)}>
                <Plus className="h-4 w-4 mr-2" />
                添加子任务
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => onDelete?.(task.id)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
}
```

**增强任务项组件 (EnhancedTaskItem.tsx)**:
```typescript
interface EnhancedTaskItemProps {
  task: ExtendedTask
  level?: number
  onToggleComplete?: (task: ExtendedTask) => void
  onUpdateStatus?: (taskId: string, status: string) => void
  onOpenAttributes?: (task: ExtendedTask) => void
  onDelete?: (task: ExtendedTask) => void
  showHierarchy?: boolean
}

export function EnhancedTaskItem({
  task,
  level = 0,
  onToggleComplete,
  onUpdateStatus,
  onOpenAttributes,
  onDelete,
  showHierarchy = true
}: EnhancedTaskItemProps) {
  const [isExpanded, setIsExpanded] = useState(false)

  // 检查是否过期
  const isOverdue = task.deadline && new Date(task.deadline) < new Date() && !task.completed

  // 计算预计时间显示
  const formatEstimatedTime = (minutes?: number) => {
    if (!minutes) return null
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h${mins > 0 ? ` ${mins}m` : ''}`
    }
    return `${mins}m`
  }

  return (
    <div
      className={cn(
        'p-4 border rounded-lg hover:bg-accent/50 transition-colors relative',
        task.completed && 'opacity-60',
        isOverdue && 'border-red-200 bg-red-50',
        showHierarchy && level > 0 && 'ml-6' // 基础缩进
      )}
    >
      {/* 层级指示器 */}
      {showHierarchy && level > 0 && (
        <div className="absolute -left-6 top-0 bottom-0 w-6 flex items-center justify-center">
          <div className="w-px h-full bg-gray-200" />
          <div className="absolute top-6 left-1/2 w-3 h-px bg-gray-200 -translate-x-1/2" />
        </div>
      )}

      <div className="flex items-start gap-3 w-full">
        {/* 完成状态复选框 */}
        <input
          type="checkbox"
          checked={task.completed}
          onChange={() => onToggleComplete?.(task)}
          className="mt-1 h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
        />

        {/* 任务内容区域 */}
        <div className="flex-1 min-w-0">
          {/* 主要信息行 */}
          <div className="flex items-center gap-2 mb-2">
            <h4
              className={cn(
                'font-medium text-sm',
                task.completed && 'line-through text-muted-foreground'
              )}
            >
              {task.content}
            </h4>

            {/* 状态标签 */}
            {task.status && task.status !== 'todo' && (
              <StatusBadge
                status={task.status}
                onStatusChange={(newStatus) => onUpdateStatus?.(task.id, newStatus)}
              />
            )}

            {/* 优先级标签 */}
            {task.priority && task.priority !== 'medium' && (
              <Badge variant="outline" className={cn("text-xs", getPriorityColor(task.priority))}>
                {task.priority}
              </Badge>
            )}
          </div>

          {/* 描述 */}
          {task.description && (
            <p className="text-sm text-muted-foreground mb-2 line-clamp-2">
              {task.description}
            </p>
          )}

          {/* 标签 */}
          {task.tags && task.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mb-2">
              {task.tags.map(tag => (
                <TaskTagBadge key={tag} tag={tag} />
              ))}
            </div>
          )}

          {/* 元数据行 */}
          <div className="flex items-center gap-4 text-xs text-muted-foreground">
            {/* 截止日期 */}
            {task.deadline && (
              <div className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                <span className={isOverdue ? 'text-red-600 font-medium' : ''}>
                  {new Date(task.deadline).toLocaleDateString()}
                </span>
                {isOverdue && <span className="text-red-600">(过期)</span>}
              </div>
            )}

            {/* 预计时间 */}
            {task.estimatedTime && (
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{formatEstimatedTime(task.estimatedTime)}</span>
              </div>
            )}

            {/* 创建时间 */}
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>{new Date(task.createdAt).toLocaleDateString()}</span>
            </div>
          </div>

          {/* 展开的详细信息 */}
          {isExpanded && (
            <div className="mt-3 pt-3 border-t space-y-2">
              {task.actualTime && (
                <div className="text-xs text-muted-foreground">
                  实际用时: {formatEstimatedTime(task.actualTime)}
                </div>
              )}
              {task.sourceType && (
                <div className="text-xs text-muted-foreground">
                  来源: {task.sourceType}
                </div>
              )}
            </div>
          )}
        </div>

        {/* 操作按钮区域 */}
        <div className="flex items-center gap-1">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="h-6 w-6 p-0"
          >
            {isExpanded ? <ChevronUp className="h-3 w-3" /> : <ChevronDown className="h-3 w-3" />}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => onOpenAttributes?.(task)}
            className="h-6 w-6 p-0"
          >
            <Settings className="h-3 w-3" />
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <MoreHorizontal className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onOpenAttributes?.(task)}>
                <Edit className="h-4 w-4 mr-2" />
                编辑属性
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onDelete?.(task)} className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                删除
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </div>
  )
}
```

#### 6. 任务标签系统

**标签数据结构**:
```typescript
interface TaskTag {
  id: string
  name: string
  color?: string
  icon?: string
}

interface TaskTagRelation {
  taskId: string
  tagId: string
}
```

**标签组件**:
```typescript
const TaskTagBadge = ({ tag, onRemove, onClick }) => (
  <Badge
    variant="secondary"
    className="text-xs cursor-pointer hover:bg-secondary/80 transition-colors"
    onClick={() => onClick?.(tag)}
  >
    {tag}
    {onRemove && (
      <button
        onClick={(e) => {
          e.stopPropagation()
          onRemove(tag)
        }}
        className="ml-1 hover:text-destructive"
      >
        <X className="h-2 w-2" />
      </button>
    )}
  </Badge>
)

const StatusBadge = ({ status, onStatusChange }) => {
  const [isOpen, setIsOpen] = useState(false)

  const statusOptions = [
    { value: 'todo', label: '待办', color: 'bg-gray-100 text-gray-800' },
    { value: 'in_progress', label: '进行中', color: 'bg-blue-100 text-blue-800' },
    { value: 'blocked', label: '阻塞', color: 'bg-red-100 text-red-800' },
    { value: 'done', label: '完成', color: 'bg-green-100 text-green-800' }
  ]

  const currentStatus = statusOptions.find(s => s.value === status)

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Badge
          variant="outline"
          className={cn("text-xs cursor-pointer", currentStatus?.color)}
        >
          {currentStatus?.label || status}
        </Badge>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start">
        {statusOptions.map(option => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => {
              onStatusChange(option.value)
              setIsOpen(false)
            }}
            className={option.value === status ? 'bg-accent' : ''}
          >
            <div className={cn("w-2 h-2 rounded-full mr-2", option.color)} />
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
```

### 总结

任务管理模块作为PaoLife应用的核心功能，具有以下特点：

1. **无限层级任务系统**: 支持复杂的任务层级结构和子任务管理
2. **智能拖拽排序**: 支持跨层级移动和同层级重排序
3. **丰富的任务属性**: 优先级、状态、标签、截止日期、预计时间
4. **双视图模式**: 基础视图和增强视图满足不同使用场景
5. **性能优化**: 虚拟化渲染、层级控制、缓存机制
6. **用户体验**: 直观的层级指示器、实时反馈、便捷操作

这个设计为用户提供了专业级的任务管理能力，支持复杂项目的任务分解和执行跟踪。
