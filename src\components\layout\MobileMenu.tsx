// 移动端菜单组件
// 移动设备上的导航菜单

import { Component, Show } from 'solid-js';
import { A, useLocation } from '@solidjs/router';
import { useAuth } from '../../contexts/AuthContext';

interface MobileMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

export const MobileMenu: Component<MobileMenuProps> = (props) => {
  const location = useLocation();
  const { user, logout } = useAuth();

  const menuItems = [
    {
      label: '仪表板',
      href: '/dashboard',
      icon: () => (
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z" />
        </svg>
      )
    },
    {
      label: '项目',
      href: '/projects',
      icon: () => (
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      )
    },
    {
      label: '任务',
      href: '/tasks',
      icon: () => (
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      )
    },
    {
      label: '领域',
      href: '/areas',
      icon: () => (
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      )
    },
    {
      label: '日历',
      href: '/calendar',
      icon: () => (
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      label: '统计分析',
      href: '/analytics',
      icon: () => (
        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    }
  ];

  const isActive = (href: string) => {
    const currentPath = location.pathname;
    if (href === '/dashboard') {
      return currentPath === '/' || currentPath === '/dashboard';
    }
    return currentPath.startsWith(href);
  };

  const handleLogout = async () => {
    try {
      await logout();
      props.onClose();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  return (
    <Show when={props.isOpen}>
      <div class="fixed inset-0 z-50 md:hidden">
        {/* 背景遮罩 */}
        <div 
          class="fixed inset-0 bg-gray-600 bg-opacity-75"
          onClick={props.onClose}
        />
        
        {/* 菜单面板 */}
        <div class="fixed inset-y-0 left-0 flex flex-col w-full max-w-xs bg-white shadow-xl">
          {/* 头部 */}
          <div class="flex items-center justify-between h-16 px-4 bg-gray-900">
            <div class="flex items-center">
              <div class="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg mr-3">
                <span class="text-white font-bold text-lg">P</span>
              </div>
              <span class="text-white text-xl font-semibold">PaoLife</span>
            </div>
            
            <button
              type="button"
              class="text-gray-300 hover:text-white"
              onClick={props.onClose}
            >
              <span class="sr-only">关闭菜单</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>

          {/* 用户信息 */}
          <div class="px-4 py-4 border-b border-gray-200">
            <div class="flex items-center">
              <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-blue-500 flex items-center justify-center">
                  <span class="text-sm font-medium text-white">
                    {user()?.username?.charAt(0).toUpperCase() || 'U'}
                  </span>
                </div>
              </div>
              <div class="ml-3">
                <p class="text-base font-medium text-gray-900">
                  {user()?.full_name || user()?.username}
                </p>
                <p class="text-sm text-gray-500">
                  {user()?.email}
                </p>
              </div>
            </div>
          </div>

          {/* 导航菜单 */}
          <nav class="flex-1 px-4 py-4 space-y-1 overflow-y-auto">
            {menuItems.map((item) => (
              <A
                href={item.href}
                class={`group flex items-center px-2 py-3 text-base font-medium rounded-md transition-colors ${
                  isActive(item.href)
                    ? 'bg-blue-100 text-blue-900'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
                onClick={props.onClose}
              >
                <div class={`mr-4 flex-shrink-0 h-6 w-6 ${
                  isActive(item.href) ? 'text-blue-500' : 'text-gray-400 group-hover:text-gray-500'
                }`}>
                  {item.icon()}
                </div>
                {item.label}
              </A>
            ))}
          </nav>

          {/* 底部操作 */}
          <div class="flex-shrink-0 p-4 border-t border-gray-200 space-y-1">
            <A
              href="/profile"
              class="group flex items-center px-2 py-3 text-base font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
              onClick={props.onClose}
            >
              <svg class="mr-4 h-6 w-6 text-gray-400 group-hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
              个人资料
            </A>
            
            <A
              href="/settings"
              class="group flex items-center px-2 py-3 text-base font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
              onClick={props.onClose}
            >
              <svg class="mr-4 h-6 w-6 text-gray-400 group-hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              设置
            </A>
            
            <button
              type="button"
              class="w-full group flex items-center px-2 py-3 text-base font-medium text-gray-600 rounded-md hover:bg-gray-50 hover:text-gray-900"
              onClick={handleLogout}
            >
              <svg class="mr-4 h-6 w-6 text-gray-400 group-hover:text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
              </svg>
              退出登录
            </button>
          </div>
        </div>
      </div>
    </Show>
  );
};

export default MobileMenu;
