// 事件系统
// 实现领域事件的发布和订阅机制，支持事件驱动架构

use crate::shared::types::EntityId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::broadcast;

/// 领域事件trait
pub trait DomainEvent: Send + Sync + Clone + std::fmt::Debug {
    fn event_type(&self) -> &'static str;
    fn aggregate_id(&self) -> &str;
    fn occurred_at(&self) -> DateTime<Utc>;
}

/// 具体的领域事件枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum AppEvent {
    // 项目相关事件
    ProjectCreated(ProjectCreatedEvent),
    ProjectUpdated(ProjectUpdatedEvent),
    ProjectDeleted(ProjectDeletedEvent),
    ProjectStatusChanged(ProjectStatusChangedEvent),

    // 任务相关事件
    TaskCreated(TaskCreatedEvent),
    TaskUpdated(TaskUpdatedEvent),
    TaskCompleted(TaskCompletedEvent),
    TaskDeleted(TaskDeletedEvent),

    // 领域相关事件
    AreaCreated(AreaCreatedEvent),
    AreaUpdated(AreaUpdatedEvent),

    // 习惯相关事件
    HabitRecorded(HabitRecordedEvent),
    HabitStreakUpdated(HabitStreakUpdatedEvent),

    // 指标相关事件
    MetricUpdated(MetricUpdatedEvent),
    MetricTargetReached(MetricTargetReachedEvent),

    // 文件系统事件
    FileChanged(FileChangedEvent),
    DocumentLinksUpdated(DocumentLinksUpdatedEvent),

    // 系统事件
    UserLoggedIn(UserLoggedInEvent),
    UserLoggedOut(UserLoggedOutEvent),
    DataBackupCompleted(DataBackupCompletedEvent),
}

impl DomainEvent for AppEvent {
    fn event_type(&self) -> &'static str {
        match self {
            AppEvent::ProjectCreated(_) => "project_created",
            AppEvent::ProjectUpdated(_) => "project_updated",
            AppEvent::ProjectDeleted(_) => "project_deleted",
            AppEvent::ProjectStatusChanged(_) => "project_status_changed",
            AppEvent::TaskCreated(_) => "task_created",
            AppEvent::TaskUpdated(_) => "task_updated",
            AppEvent::TaskCompleted(_) => "task_completed",
            AppEvent::TaskDeleted(_) => "task_deleted",
            AppEvent::AreaCreated(_) => "area_created",
            AppEvent::AreaUpdated(_) => "area_updated",
            AppEvent::HabitRecorded(_) => "habit_recorded",
            AppEvent::HabitStreakUpdated(_) => "habit_streak_updated",
            AppEvent::MetricUpdated(_) => "metric_updated",
            AppEvent::MetricTargetReached(_) => "metric_target_reached",
            AppEvent::FileChanged(_) => "file_changed",
            AppEvent::DocumentLinksUpdated(_) => "document_links_updated",
            AppEvent::UserLoggedIn(_) => "user_logged_in",
            AppEvent::UserLoggedOut(_) => "user_logged_out",
            AppEvent::DataBackupCompleted(_) => "data_backup_completed",
        }
    }

    fn aggregate_id(&self) -> &str {
        match self {
            AppEvent::ProjectCreated(e) => &e.project_id,
            AppEvent::ProjectUpdated(e) => &e.project_id,
            AppEvent::ProjectDeleted(e) => &e.project_id,
            AppEvent::ProjectStatusChanged(e) => &e.project_id,
            AppEvent::TaskCreated(e) => &e.task_id,
            AppEvent::TaskUpdated(e) => &e.task_id,
            AppEvent::TaskCompleted(e) => &e.task_id,
            AppEvent::TaskDeleted(e) => &e.task_id,
            AppEvent::AreaCreated(e) => &e.area_id,
            AppEvent::AreaUpdated(e) => &e.area_id,
            AppEvent::HabitRecorded(e) => &e.habit_id,
            AppEvent::HabitStreakUpdated(e) => &e.habit_id,
            AppEvent::MetricUpdated(e) => &e.metric_id,
            AppEvent::MetricTargetReached(e) => &e.metric_id,
            AppEvent::FileChanged(e) => &e.file_path,
            AppEvent::DocumentLinksUpdated(e) => &e.document_path,
            AppEvent::UserLoggedIn(e) => &e.user_id,
            AppEvent::UserLoggedOut(e) => &e.user_id,
            AppEvent::DataBackupCompleted(e) => &e.backup_id,
        }
    }

    fn occurred_at(&self) -> DateTime<Utc> {
        match self {
            AppEvent::ProjectCreated(e) => e.occurred_at,
            AppEvent::ProjectUpdated(e) => e.occurred_at,
            AppEvent::ProjectDeleted(e) => e.occurred_at,
            AppEvent::ProjectStatusChanged(e) => e.occurred_at,
            AppEvent::TaskCreated(e) => e.occurred_at,
            AppEvent::TaskUpdated(e) => e.occurred_at,
            AppEvent::TaskCompleted(e) => e.occurred_at,
            AppEvent::TaskDeleted(e) => e.occurred_at,
            AppEvent::AreaCreated(e) => e.occurred_at,
            AppEvent::AreaUpdated(e) => e.occurred_at,
            AppEvent::HabitRecorded(e) => e.occurred_at,
            AppEvent::HabitStreakUpdated(e) => e.occurred_at,
            AppEvent::MetricUpdated(e) => e.occurred_at,
            AppEvent::MetricTargetReached(e) => e.occurred_at,
            AppEvent::FileChanged(e) => e.occurred_at,
            AppEvent::DocumentLinksUpdated(e) => e.occurred_at,
            AppEvent::UserLoggedIn(e) => e.occurred_at,
            AppEvent::UserLoggedOut(e) => e.occurred_at,
            AppEvent::DataBackupCompleted(e) => e.occurred_at,
        }
    }
}

// 具体事件结构定义
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectCreatedEvent {
    pub project_id: EntityId,
    pub name: String,
    pub area_id: Option<EntityId>,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectUpdatedEvent {
    pub project_id: EntityId,
    pub changes: Vec<String>,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectDeletedEvent {
    pub project_id: EntityId,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectStatusChangedEvent {
    pub project_id: EntityId,
    pub old_status: String,
    pub new_status: String,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskCreatedEvent {
    pub task_id: EntityId,
    pub title: String,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskUpdatedEvent {
    pub task_id: EntityId,
    pub changes: Vec<String>,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskCompletedEvent {
    pub task_id: EntityId,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskDeletedEvent {
    pub task_id: EntityId,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaCreatedEvent {
    pub area_id: EntityId,
    pub name: String,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaUpdatedEvent {
    pub area_id: EntityId,
    pub changes: Vec<String>,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HabitRecordedEvent {
    pub habit_id: EntityId,
    pub date: String,
    pub value: i32,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HabitStreakUpdatedEvent {
    pub habit_id: EntityId,
    pub new_streak: i32,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricUpdatedEvent {
    pub metric_id: EntityId,
    pub new_value: f64,
    pub target_value: Option<f64>,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MetricTargetReachedEvent {
    pub metric_id: EntityId,
    pub target_value: f64,
    pub actual_value: f64,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FileChangedEvent {
    pub file_path: String,
    pub change_type: String, // created, modified, deleted
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DocumentLinksUpdatedEvent {
    pub document_path: String,
    pub links_added: Vec<String>,
    pub links_removed: Vec<String>,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserLoggedInEvent {
    pub user_id: EntityId,
    pub session_id: String,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserLoggedOutEvent {
    pub user_id: EntityId,
    pub session_id: String,
    pub occurred_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataBackupCompletedEvent {
    pub backup_id: String,
    pub file_path: String,
    pub size_bytes: u64,
    pub occurred_at: DateTime<Utc>,
}

/// 事件总线
pub struct EventBus {
    sender: broadcast::Sender<AppEvent>,
}

impl EventBus {
    pub fn new() -> Self {
        let (sender, _) = broadcast::channel(1000);
        Self { sender }
    }

    /// 发布事件
    pub fn publish(&self, event: AppEvent) -> Result<(), crate::shared::errors::AppError> {
        self.sender.send(event).map_err(|_| {
            crate::shared::errors::AppError::Internal {
                message: "Failed to publish event".to_string(),
            }
        })?;
        Ok(())
    }

    /// 订阅事件
    pub fn subscribe(&self) -> broadcast::Receiver<AppEvent> {
        self.sender.subscribe()
    }

    /// 获取订阅者数量
    pub fn subscriber_count(&self) -> usize {
        self.sender.receiver_count()
    }
}

impl Default for EventBus {
    fn default() -> Self {
        Self::new()
    }
}

/// 事件处理器trait
#[async_trait::async_trait]
pub trait EventHandler: Send + Sync {
    async fn handle(&self, event: &AppEvent) -> Result<(), crate::shared::errors::AppError>;
    fn interested_events(&self) -> Vec<&'static str>;
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_event_creation() {
        let event = AppEvent::ProjectCreated(ProjectCreatedEvent {
            project_id: "test-project".to_string(),
            name: "Test Project".to_string(),
            area_id: None,
            occurred_at: Utc::now(),
        });

        assert_eq!(event.event_type(), "project_created");
        assert_eq!(event.aggregate_id(), "test-project");
    }

    #[tokio::test]
    async fn test_event_bus() {
        let event_bus = EventBus::new();
        let mut receiver = event_bus.subscribe();

        let event = AppEvent::ProjectCreated(ProjectCreatedEvent {
            project_id: "test-project".to_string(),
            name: "Test Project".to_string(),
            area_id: None,
            occurred_at: Utc::now(),
        });

        event_bus.publish(event.clone()).unwrap();

        let received_event = receiver.recv().await.unwrap();
        assert_eq!(received_event.event_type(), event.event_type());
    }
}
