// 数据库迁移管理器
// 负责执行数据库迁移和版本管理

use sqlx::{SqlitePool, Row};
use crate::shared::errors::{AppError, AppResult};
use std::collections::HashMap;

/// 迁移信息
#[derive(Debug, Clone)]
pub struct Migration {
    pub version: String,
    pub name: String,
    pub sql: String,
}

/// 迁移管理器
pub struct MigrationManager {
    pool: SqlitePool,
    migrations: Vec<Migration>,
}

impl MigrationManager {
    /// 创建新的迁移管理器
    pub fn new(pool: SqlitePool) -> Self {
        let migrations = vec![
            Migration {
                version: "001".to_string(),
                name: "initial_schema".to_string(),
                sql: include_str!("migrations/001_initial_schema.sql").to_string(),
            },
        ];

        Self { pool, migrations }
    }

    /// 初始化迁移表
    pub async fn init_migration_table(&self) -> AppResult<()> {
        let sql = r#"
            CREATE TABLE IF NOT EXISTS schema_migrations (
                version TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            );
        "#;

        sqlx::query(sql)
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to create migration table: {}", e)))?;

        tracing::info!("Migration table initialized");
        Ok(())
    }

    /// 获取已应用的迁移版本
    pub async fn get_applied_migrations(&self) -> AppResult<Vec<String>> {
        let rows = sqlx::query("SELECT version FROM schema_migrations ORDER BY version")
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get applied migrations: {}", e)))?;

        let versions = rows
            .iter()
            .map(|row| row.get::<String, _>("version"))
            .collect();

        Ok(versions)
    }

    /// 获取待应用的迁移
    pub async fn get_pending_migrations(&self) -> AppResult<Vec<&Migration>> {
        let applied = self.get_applied_migrations().await?;
        let applied_set: std::collections::HashSet<String> = applied.into_iter().collect();

        let pending: Vec<&Migration> = self
            .migrations
            .iter()
            .filter(|migration| !applied_set.contains(&migration.version))
            .collect();

        Ok(pending)
    }

    /// 应用单个迁移
    pub async fn apply_migration(&self, migration: &Migration) -> AppResult<()> {
        tracing::info!(
            version = %migration.version,
            name = %migration.name,
            "Applying migration"
        );

        // 开始事务
        let mut tx = self.pool.begin().await
            .map_err(|e| AppError::database(format!("Failed to begin transaction: {}", e)))?;

        // 执行迁移SQL
        sqlx::query(&migration.sql)
            .execute(&mut *tx)
            .await
            .map_err(|e| AppError::database(format!(
                "Failed to execute migration {}: {}", 
                migration.version, 
                e
            )))?;

        // 记录迁移
        sqlx::query(
            "INSERT INTO schema_migrations (version, name) VALUES (?, ?)"
        )
        .bind(&migration.version)
        .bind(&migration.name)
        .execute(&mut *tx)
        .await
        .map_err(|e| AppError::database(format!(
            "Failed to record migration {}: {}", 
            migration.version, 
            e
        )))?;

        // 提交事务
        tx.commit().await
            .map_err(|e| AppError::database(format!("Failed to commit migration: {}", e)))?;

        tracing::info!(
            version = %migration.version,
            name = %migration.name,
            "Migration applied successfully"
        );

        Ok(())
    }

    /// 应用所有待应用的迁移
    pub async fn migrate(&self) -> AppResult<()> {
        // 初始化迁移表
        self.init_migration_table().await?;

        // 获取待应用的迁移
        let pending = self.get_pending_migrations().await?;

        if pending.is_empty() {
            tracing::info!("No pending migrations");
            return Ok(());
        }

        tracing::info!(count = pending.len(), "Found pending migrations");

        // 应用每个迁移
        for migration in pending {
            self.apply_migration(migration).await?;
        }

        tracing::info!("All migrations applied successfully");
        Ok(())
    }

    /// 检查数据库状态
    pub async fn check_database_status(&self) -> AppResult<DatabaseStatus> {
        let applied = self.get_applied_migrations().await?;
        let pending = self.get_pending_migrations().await?;

        let status = DatabaseStatus {
            total_migrations: self.migrations.len(),
            applied_migrations: applied.len(),
            pending_migrations: pending.len(),
            latest_applied: applied.last().cloned(),
            is_up_to_date: pending.is_empty(),
        };

        Ok(status)
    }

    /// 验证数据库完整性
    pub async fn validate_database(&self) -> AppResult<ValidationResult> {
        let mut errors = Vec::new();
        let mut warnings = Vec::new();

        // 检查必要的表是否存在
        let required_tables = vec![
            "users", "areas", "projects", "tasks", "resources",
            "habits", "habit_records", "tags", "project_tags",
            "task_tags", "time_entries", "notifications",
            "user_settings", "audit_logs", "schema_migrations"
        ];

        for table in required_tables {
            let exists = self.check_table_exists(table).await?;
            if !exists {
                errors.push(format!("Required table '{}' does not exist", table));
            }
        }

        // 检查外键约束
        let foreign_key_check = sqlx::query("PRAGMA foreign_key_check")
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to check foreign keys: {}", e)))?;

        if !foreign_key_check.is_empty() {
            errors.push("Foreign key constraint violations found".to_string());
        }

        // 检查索引
        let index_check = self.check_indexes().await?;
        if !index_check {
            warnings.push("Some indexes may be missing".to_string());
        }

        let result = ValidationResult {
            is_valid: errors.is_empty(),
            errors,
            warnings,
        };

        Ok(result)
    }

    /// 检查表是否存在
    async fn check_table_exists(&self, table_name: &str) -> AppResult<bool> {
        let row = sqlx::query(
            "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name=?"
        )
        .bind(table_name)
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to check table existence: {}", e)))?;

        let count: i64 = row.get("count");
        Ok(count > 0)
    }

    /// 检查索引
    async fn check_indexes(&self) -> AppResult<bool> {
        let rows = sqlx::query("SELECT name FROM sqlite_master WHERE type='index'")
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to check indexes: {}", e)))?;

        // 简单检查：确保有一定数量的索引
        Ok(rows.len() > 10)
    }

    /// 获取数据库统计信息
    pub async fn get_database_stats(&self) -> AppResult<DatabaseStats> {
        let mut stats = HashMap::new();

        let tables = vec![
            "users", "areas", "projects", "tasks", "resources",
            "habits", "habit_records", "tags", "time_entries",
            "notifications", "user_settings", "audit_logs"
        ];

        for table in tables {
            let count = self.get_table_count(table).await?;
            stats.insert(table.to_string(), count);
        }

        let db_stats = DatabaseStats {
            table_counts: stats,
            database_size: self.get_database_size().await?,
        };

        Ok(db_stats)
    }

    /// 获取表记录数
    async fn get_table_count(&self, table_name: &str) -> AppResult<i64> {
        let sql = format!("SELECT COUNT(*) as count FROM {}", table_name);
        let row = sqlx::query(&sql)
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to count table {}: {}", table_name, e)))?;

        Ok(row.get("count"))
    }

    /// 获取数据库大小
    async fn get_database_size(&self) -> AppResult<i64> {
        let row = sqlx::query("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to get database size: {}", e)))?;

        Ok(row.get("size"))
    }
}

/// 数据库状态
#[derive(Debug, Clone)]
pub struct DatabaseStatus {
    pub total_migrations: usize,
    pub applied_migrations: usize,
    pub pending_migrations: usize,
    pub latest_applied: Option<String>,
    pub is_up_to_date: bool,
}

/// 验证结果
#[derive(Debug, Clone)]
pub struct ValidationResult {
    pub is_valid: bool,
    pub errors: Vec<String>,
    pub warnings: Vec<String>,
}

/// 数据库统计信息
#[derive(Debug, Clone)]
pub struct DatabaseStats {
    pub table_counts: HashMap<String, i64>,
    pub database_size: i64,
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::infrastructure::database::connection::DatabaseConnection;

    #[tokio::test]
    async fn test_migration_manager() {
        let db = DatabaseConnection::new_in_memory().await.unwrap();
        let pool = db.get_pool();
        let manager = MigrationManager::new(pool.clone());

        // 测试初始化迁移表
        manager.init_migration_table().await.unwrap();

        // 测试获取待应用的迁移
        let pending = manager.get_pending_migrations().await.unwrap();
        assert!(!pending.is_empty());

        // 测试应用迁移
        manager.migrate().await.unwrap();

        // 验证迁移已应用
        let applied = manager.get_applied_migrations().await.unwrap();
        assert!(!applied.is_empty());

        // 测试数据库状态
        let status = manager.check_database_status().await.unwrap();
        assert!(status.is_up_to_date);

        // 测试数据库验证
        let validation = manager.validate_database().await.unwrap();
        assert!(validation.is_valid);
    }
}
