import { Component, createSignal, onMount, For, Show } from 'solid-js';
import { A } from '@solidjs/router';
import { invoke } from '@tauri-apps/api/core';
import { useAuth } from '../contexts/AuthContext';
import toast from 'solid-toast';

// 类型定义
interface ProjectStats {
  total_projects: number;
  active_projects: number;
  completed_projects: number;
  overdue_projects: number;
  completion_rate: number;
  average_progress: number;
}

interface TaskStats {
  total_assigned_tasks: number;
  active_assigned_tasks: number;
  completed_assigned_tasks: number;
  overdue_assigned_tasks: number;
  completion_rate: number;
  average_completion_time_days?: number;
}

interface RecentProject {
  id: string;
  name: string;
  status: string;
  progress: number;
  deadline?: string;
}

interface RecentTask {
  id: string;
  title: string;
  status: string;
  completion_percentage: number;
  due_date?: string;
}

// 统计卡片组件
const StatCard: Component<{
  title: string;
  value: string | number;
  subtitle?: string;
  color: 'blue' | 'green' | 'yellow' | 'red';
  icon: Component;
}> = (props) => {
  const colorClasses = {
    blue: 'bg-blue-500',
    green: 'bg-green-500',
    yellow: 'bg-yellow-500',
    red: 'bg-red-500',
  };

  return (
    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class={`w-8 h-8 ${colorClasses[props.color]} rounded-md flex items-center justify-center text-white`}>
              <props.icon />
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                {props.title}
              </dt>
              <dd class="text-lg font-medium text-gray-900 dark:text-white">
                {props.value}
              </dd>
              <Show when={props.subtitle}>
                <dd class="text-sm text-gray-500 dark:text-gray-400">
                  {props.subtitle}
                </dd>
              </Show>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );
};

// 图标组件
const ProjectIcon: Component = () => (
  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
  </svg>
);

const TaskIcon: Component = () => (
  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
  </svg>
);

const CompletionIcon: Component = () => (
  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
  </svg>
);

const OverdueIcon: Component = () => (
  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
  </svg>
);

export const DashboardPage: Component = () => {
  const [projectStats, setProjectStats] = createSignal<ProjectStats | null>(null);
  const [taskStats, setTaskStats] = createSignal<TaskStats | null>(null);
  const [recentProjects, setRecentProjects] = createSignal<RecentProject[]>([]);
  const [recentTasks, setRecentTasks] = createSignal<RecentTask[]>([]);
  const [isLoading, setIsLoading] = createSignal(true);

  const { user } = useAuth();

  onMount(async () => {
    await loadDashboardData();
  });

  const loadDashboardData = async () => {
    try {
      setIsLoading(true);
      const currentUserId = user()?.id;

      if (!currentUserId) {
        toast.error('用户未登录');
        return;
      }

      // 并行加载数据
      const [projectStatsRes, taskStatsRes, projectsRes, tasksRes] = await Promise.allSettled([
        invoke('get_project_stats', { currentUserId }),
        invoke('get_task_stats', { currentUserId }),
        invoke('list_projects', { 
          pagination: { page: 1, page_size: 5 },
          statusFilter: null,
          currentUserId 
        }),
        invoke('list_tasks', { 
          pagination: { page: 1, page_size: 5 },
          statusFilter: null,
          currentUserId 
        }),
      ]);

      // 处理项目统计
      if (projectStatsRes.status === 'fulfilled' && projectStatsRes.value.success) {
        setProjectStats(projectStatsRes.value.data);
      }

      // 处理任务统计
      if (taskStatsRes.status === 'fulfilled' && taskStatsRes.value.success) {
        setTaskStats(taskStatsRes.value.data);
      }

      // 处理最近项目
      if (projectsRes.status === 'fulfilled' && projectsRes.value.success) {
        setRecentProjects(projectsRes.value.data.items || []);
      }

      // 处理最近任务
      if (tasksRes.status === 'fulfilled' && tasksRes.value.success) {
        setRecentTasks(tasksRes.value.data.items || []);
      }

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
      toast.error('加载仪表板数据失败');
    } finally {
      setIsLoading(false);
    }
  };

  const formatPercentage = (value: number) => `${Math.round(value * 100)}%`;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 dark:text-green-400';
      case 'in_progress': return 'text-blue-600 dark:text-blue-400';
      case 'at_risk': return 'text-yellow-600 dark:text-yellow-400';
      case 'paused': return 'text-gray-600 dark:text-gray-400';
      case 'todo': return 'text-gray-600 dark:text-gray-400';
      case 'waiting': return 'text-yellow-600 dark:text-yellow-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'not_started': '未开始',
      'in_progress': '进行中',
      'at_risk': '有风险',
      'paused': '已暂停',
      'completed': '已完成',
      'archived': '已归档',
      'todo': '待办',
      'waiting': '等待中',
      'cancelled': '已取消',
    };
    return statusMap[status] || status;
  };

  if (isLoading()) {
    return (
      <div class="flex items-center justify-center h-64">
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div class="space-y-6">
      {/* 页面标题 */}
      <div>
        <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
          欢迎回来，{user()?.full_name || user()?.username}！
        </h1>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
          这是你的项目和任务概览
        </p>
      </div>

      {/* 统计卡片 */}
      <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <Show when={projectStats()}>
          <StatCard
            title="活跃项目"
            value={projectStats()!.active_projects}
            subtitle={`总共 ${projectStats()!.total_projects} 个项目`}
            color="blue"
            icon={ProjectIcon}
          />
          <StatCard
            title="已完成项目"
            value={projectStats()!.completed_projects}
            subtitle={`完成率 ${formatPercentage(projectStats()!.completion_rate)}`}
            color="green"
            icon={CompletionIcon}
          />
        </Show>
        
        <Show when={taskStats()}>
          <StatCard
            title="活跃任务"
            value={taskStats()!.active_assigned_tasks}
            subtitle={`总共 ${taskStats()!.total_assigned_tasks} 个任务`}
            color="blue"
            icon={TaskIcon}
          />
          <StatCard
            title="逾期任务"
            value={taskStats()!.overdue_assigned_tasks}
            color="red"
            icon={OverdueIcon}
          />
        </Show>
      </div>

      {/* 内容区域 */}
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* 最近项目 */}
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                最近项目
              </h3>
              <A
                href="/projects"
                class="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
              >
                查看全部
              </A>
            </div>
            <div class="space-y-3">
              <Show
                when={recentProjects().length > 0}
                fallback={
                  <p class="text-gray-500 dark:text-gray-400 text-center py-4">
                    暂无项目
                  </p>
                }
              >
                <For each={recentProjects()}>
                  {(project) => (
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                          {project.name}
                        </h4>
                        <p class={`text-xs ${getStatusColor(project.status)}`}>
                          {getStatusText(project.status)}
                        </p>
                      </div>
                      <div class="text-right">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          {project.progress}%
                        </div>
                        <Show when={project.deadline}>
                          <div class="text-xs text-gray-500 dark:text-gray-400">
                            {new Date(project.deadline!).toLocaleDateString()}
                          </div>
                        </Show>
                      </div>
                    </div>
                  )}
                </For>
              </Show>
            </div>
          </div>
        </div>

        {/* 最近任务 */}
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div class="px-4 py-5 sm:p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white">
                最近任务
              </h3>
              <A
                href="/tasks"
                class="text-sm font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400"
              >
                查看全部
              </A>
            </div>
            <div class="space-y-3">
              <Show
                when={recentTasks().length > 0}
                fallback={
                  <p class="text-gray-500 dark:text-gray-400 text-center py-4">
                    暂无任务
                  </p>
                }
              >
                <For each={recentTasks()}>
                  {(task) => (
                    <div class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-md">
                      <div class="flex-1">
                        <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                          {task.title}
                        </h4>
                        <p class={`text-xs ${getStatusColor(task.status)}`}>
                          {getStatusText(task.status)}
                        </p>
                      </div>
                      <div class="text-right">
                        <div class="text-sm font-medium text-gray-900 dark:text-white">
                          {task.completion_percentage}%
                        </div>
                        <Show when={task.due_date}>
                          <div class="text-xs text-gray-500 dark:text-gray-400">
                            {new Date(task.due_date!).toLocaleDateString()}
                          </div>
                        </Show>
                      </div>
                    </div>
                  )}
                </For>
              </Show>
            </div>
          </div>
        </div>
      </div>

      {/* 快速操作 */}
      <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
        <div class="px-4 py-5 sm:p-6">
          <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-white mb-4">
            快速操作
          </h3>
          <div class="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
            <A
              href="/projects/new"
              class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              创建项目
            </A>
            <A
              href="/tasks/new"
              class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              创建任务
            </A>
            <A
              href="/areas/new"
              class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              创建领域
            </A>
            <A
              href="/settings"
              class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              设置
            </A>
          </div>
        </div>
      </div>
    </div>
  );
};
