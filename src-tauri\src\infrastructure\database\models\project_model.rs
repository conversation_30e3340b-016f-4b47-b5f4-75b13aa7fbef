// 项目数据模型
// 对应数据库中的项目表结构

use sqlx::FromRow;
use serde::{Deserialize, Serialize};
use chrono::{DateTime, Utc, NaiveDate};

/// 项目数据模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ProjectModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: String,
    pub progress: i32,
    pub priority: i32,
    pub start_date: Option<NaiveDate>,
    pub deadline: Option<NaiveDate>,
    pub estimated_hours: Option<i32>,
    pub actual_hours: i32,
    pub area_id: Option<String>,
    pub created_by: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub archived_at: Option<DateTime<Utc>>,
}

/// 创建项目数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateProjectModel {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: String,
    pub progress: i32,
    pub priority: i32,
    pub start_date: Option<NaiveDate>,
    pub deadline: Option<NaiveDate>,
    pub estimated_hours: Option<i32>,
    pub actual_hours: i32,
    pub area_id: Option<String>,
    pub created_by: String,
}

/// 更新项目数据模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateProjectModel {
    pub name: Option<String>,
    pub description: Option<String>,
    pub status: Option<String>,
    pub progress: Option<i32>,
    pub priority: Option<i32>,
    pub start_date: Option<NaiveDate>,
    pub deadline: Option<NaiveDate>,
    pub estimated_hours: Option<i32>,
    pub actual_hours: Option<i32>,
    pub area_id: Option<String>,
    pub archived_at: Option<DateTime<Utc>>,
}

/// 项目查询模型
#[derive(Debug, Clone, Default, Serialize, Deserialize)]
pub struct ProjectQueryModel {
    pub name: Option<String>,
    pub status: Option<String>,
    pub priority: Option<i32>,
    pub area_id: Option<String>,
    pub created_by: Option<String>,
    pub has_deadline: Option<bool>,
    pub is_overdue: Option<bool>,
    pub created_after: Option<DateTime<Utc>>,
    pub created_before: Option<DateTime<Utc>>,
}

/// 项目统计模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct ProjectStatsModel {
    pub total_projects: i64,
    pub active_projects: i64,
    pub completed_projects: i64,
    pub overdue_projects: i64,
    pub projects_by_priority_low: i64,
    pub projects_by_priority_medium: i64,
    pub projects_by_priority_high: i64,
    pub projects_by_priority_urgent: i64,
    pub average_progress: f64,
    pub completion_rate: f64,
}

/// 用户项目统计模型
#[derive(Debug, Clone, FromRow, Serialize, Deserialize)]
pub struct UserProjectStatsModel {
    pub user_id: String,
    pub total_projects: i64,
    pub active_projects: i64,
    pub completed_projects: i64,
    pub overdue_projects: i64,
    pub completion_rate: f64,
    pub average_progress: f64,
    pub total_estimated_hours: i64,
    pub total_actual_hours: i64,
}

impl ProjectModel {
    /// 创建新的项目模型
    pub fn new(
        id: String,
        name: String,
        description: Option<String>,
        priority: i32,
        area_id: Option<String>,
        created_by: String,
    ) -> Self {
        let now = Utc::now();
        Self {
            id,
            name,
            description,
            status: "not_started".to_string(),
            progress: 0,
            priority,
            start_date: None,
            deadline: None,
            estimated_hours: None,
            actual_hours: 0,
            area_id,
            created_by,
            created_at: now,
            updated_at: now,
            archived_at: None,
        }
    }

    /// 检查项目是否已完成
    pub fn is_completed(&self) -> bool {
        self.status == "completed"
    }

    /// 检查项目是否已归档
    pub fn is_archived(&self) -> bool {
        self.archived_at.is_some()
    }

    /// 检查项目是否逾期
    pub fn is_overdue(&self) -> bool {
        if let Some(deadline) = self.deadline {
            let today = chrono::Utc::now().date_naive();
            deadline < today && !self.is_completed()
        } else {
            false
        }
    }

    /// 获取项目状态显示文本
    pub fn status_display(&self) -> &str {
        match self.status.as_str() {
            "not_started" => "未开始",
            "in_progress" => "进行中",
            "at_risk" => "有风险",
            "paused" => "已暂停",
            "completed" => "已完成",
            "archived" => "已归档",
            _ => "未知状态",
        }
    }

    /// 获取优先级显示文本
    pub fn priority_display(&self) -> &str {
        match self.priority {
            1 => "低",
            2 => "中",
            3 => "高",
            4 => "紧急",
            _ => "未知",
        }
    }

    /// 计算剩余天数
    pub fn days_remaining(&self) -> Option<i64> {
        if let Some(deadline) = self.deadline {
            let today = chrono::Utc::now().date_naive();
            Some((deadline - today).num_days())
        } else {
            None
        }
    }

    /// 计算项目持续时间（天数）
    pub fn duration_days(&self) -> i64 {
        (Utc::now() - self.created_at).num_days()
    }

    /// 检查是否有预估时间
    pub fn has_estimated_hours(&self) -> bool {
        self.estimated_hours.is_some() && self.estimated_hours.unwrap() > 0
    }

    /// 计算时间使用率
    pub fn time_utilization_rate(&self) -> Option<f64> {
        if let Some(estimated) = self.estimated_hours {
            if estimated > 0 {
                Some(self.actual_hours as f64 / estimated as f64)
            } else {
                None
            }
        } else {
            None
        }
    }
}

impl CreateProjectModel {
    /// 创建新的创建项目模型
    pub fn new(
        id: String,
        name: String,
        description: Option<String>,
        priority: i32,
        area_id: Option<String>,
        created_by: String,
    ) -> Self {
        Self {
            id,
            name,
            description,
            status: "not_started".to_string(),
            progress: 0,
            priority,
            start_date: None,
            deadline: None,
            estimated_hours: None,
            actual_hours: 0,
            area_id,
            created_by,
        }
    }

    /// 设置开始日期
    pub fn with_start_date(mut self, start_date: NaiveDate) -> Self {
        self.start_date = Some(start_date);
        self
    }

    /// 设置截止日期
    pub fn with_deadline(mut self, deadline: NaiveDate) -> Self {
        self.deadline = Some(deadline);
        self
    }

    /// 设置预估时间
    pub fn with_estimated_hours(mut self, hours: i32) -> Self {
        self.estimated_hours = Some(hours);
        self
    }
}

impl UpdateProjectModel {
    /// 创建空的更新模型
    pub fn new() -> Self {
        Self {
            name: None,
            description: None,
            status: None,
            progress: None,
            priority: None,
            start_date: None,
            deadline: None,
            estimated_hours: None,
            actual_hours: None,
            area_id: None,
            archived_at: None,
        }
    }

    /// 设置名称
    pub fn with_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// 设置状态
    pub fn with_status(mut self, status: String) -> Self {
        self.status = Some(status);
        self
    }

    /// 设置进度
    pub fn with_progress(mut self, progress: i32) -> Self {
        self.progress = Some(progress);
        self
    }

    /// 设置优先级
    pub fn with_priority(mut self, priority: i32) -> Self {
        self.priority = Some(priority);
        self
    }

    /// 归档项目
    pub fn archive(mut self) -> Self {
        self.archived_at = Some(Utc::now());
        self
    }

    /// 检查是否有更新
    pub fn has_updates(&self) -> bool {
        self.name.is_some()
            || self.description.is_some()
            || self.status.is_some()
            || self.progress.is_some()
            || self.priority.is_some()
            || self.start_date.is_some()
            || self.deadline.is_some()
            || self.estimated_hours.is_some()
            || self.actual_hours.is_some()
            || self.area_id.is_some()
            || self.archived_at.is_some()
    }
}

impl ProjectQueryModel {
    /// 创建新的查询模型
    pub fn new() -> Self {
        Self::default()
    }

    /// 按名称查询
    pub fn by_name(mut self, name: String) -> Self {
        self.name = Some(name);
        self
    }

    /// 按状态查询
    pub fn by_status(mut self, status: String) -> Self {
        self.status = Some(status);
        self
    }

    /// 按优先级查询
    pub fn by_priority(mut self, priority: i32) -> Self {
        self.priority = Some(priority);
        self
    }

    /// 按领域查询
    pub fn by_area(mut self, area_id: String) -> Self {
        self.area_id = Some(area_id);
        self
    }

    /// 按创建者查询
    pub fn by_creator(mut self, created_by: String) -> Self {
        self.created_by = Some(created_by);
        self
    }

    /// 查询有截止日期的项目
    pub fn with_deadline(mut self) -> Self {
        self.has_deadline = Some(true);
        self
    }

    /// 查询逾期项目
    pub fn overdue_only(mut self) -> Self {
        self.is_overdue = Some(true);
        self
    }

    /// 检查是否有查询条件
    pub fn has_conditions(&self) -> bool {
        self.name.is_some()
            || self.status.is_some()
            || self.priority.is_some()
            || self.area_id.is_some()
            || self.created_by.is_some()
            || self.has_deadline.is_some()
            || self.is_overdue.is_some()
            || self.created_after.is_some()
            || self.created_before.is_some()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_project_model_creation() {
        let project = ProjectModel::new(
            "proj123".to_string(),
            "Test Project".to_string(),
            Some("A test project".to_string()),
            2,
            Some("area123".to_string()),
            "user123".to_string(),
        );

        assert_eq!(project.id, "proj123");
        assert_eq!(project.name, "Test Project");
        assert_eq!(project.status, "not_started");
        assert_eq!(project.progress, 0);
        assert!(!project.is_completed());
        assert!(!project.is_archived());
    }

    #[test]
    fn test_project_overdue_check() {
        let mut project = ProjectModel::new(
            "proj123".to_string(),
            "Test Project".to_string(),
            None,
            2,
            None,
            "user123".to_string(),
        );

        // 设置过去的截止日期
        project.deadline = Some(chrono::Utc::now().date_naive() - chrono::Duration::days(1));
        assert!(project.is_overdue());

        // 设置未来的截止日期
        project.deadline = Some(chrono::Utc::now().date_naive() + chrono::Duration::days(1));
        assert!(!project.is_overdue());

        // 完成的项目不算逾期
        project.status = "completed".to_string();
        project.deadline = Some(chrono::Utc::now().date_naive() - chrono::Duration::days(1));
        assert!(!project.is_overdue());
    }

    #[test]
    fn test_update_project_model() {
        let update_project = UpdateProjectModel::new()
            .with_name("Updated Project".to_string())
            .with_status("in_progress".to_string())
            .with_progress(50);

        assert!(update_project.has_updates());
        assert_eq!(update_project.name, Some("Updated Project".to_string()));
        assert_eq!(update_project.status, Some("in_progress".to_string()));
        assert_eq!(update_project.progress, Some(50));
    }
}
