import { Component, createSignal, Show } from 'solid-js';
import { A, useNavigate } from '@solidjs/router';
import { useAuth } from '../../contexts/AuthContext';

export const LoginPage: Component = () => {
  const [username, setUsername] = createSignal('');
  const [password, setPassword] = createSignal('');
  const [isSubmitting, setIsSubmitting] = createSignal(false);
  const [errors, setErrors] = createSignal<Record<string, string>>({});

  const { login, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // 如果已经登录，重定向到仪表板
  if (isAuthenticated()) {
    navigate('/dashboard');
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!username().trim()) {
      newErrors.username = '请输入用户名';
    }

    if (!password().trim()) {
      newErrors.password = '请输入密码';
    } else if (password().length < 6) {
      newErrors.password = '密码至少需要6个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const success = await login({
        username: username(),
        password: password(),
      });

      if (success) {
        navigate('/dashboard');
      }
    } catch (error) {
      console.error('Login error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        {/* 头部 */}
        <div class="text-center">
          <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            登录到 PaoLife
          </h2>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            管理你的项目、任务和生活领域
          </p>
        </div>

        {/* 登录表单 */}
        <form class="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div class="space-y-4">
            {/* 用户名输入 */}
            <div>
              <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                用户名
              </label>
              <input
                id="username"
                name="username"
                type="text"
                autocomplete="username"
                required
                value={username()}
                onInput={(e) => setUsername(e.currentTarget.value)}
                class={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors().username 
                    ? 'border-red-300 dark:border-red-600' 
                    : 'border-gray-300 dark:border-gray-600'
                } placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                placeholder="请输入用户名"
              />
              <Show when={errors().username}>
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors().username}</p>
              </Show>
            </div>

            {/* 密码输入 */}
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                密码
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autocomplete="current-password"
                required
                value={password()}
                onInput={(e) => setPassword(e.currentTarget.value)}
                class={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors().password 
                    ? 'border-red-300 dark:border-red-600' 
                    : 'border-gray-300 dark:border-gray-600'
                } placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                placeholder="请输入密码"
              />
              <Show when={errors().password}>
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors().password}</p>
              </Show>
            </div>
          </div>

          {/* 登录按钮 */}
          <div>
            <button
              type="submit"
              disabled={isSubmitting()}
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <Show
                when={!isSubmitting()}
                fallback={
                  <div class="flex items-center">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    登录中...
                  </div>
                }
              >
                登录
              </Show>
            </button>
          </div>

          {/* 注册链接 */}
          <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              还没有账户？{' '}
              <A
                href="/register"
                class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
              >
                立即注册
              </A>
            </p>
          </div>
        </form>

        {/* 演示账户提示 */}
        <div class="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-md">
          <div class="text-sm text-blue-700 dark:text-blue-300">
            <p class="font-medium mb-2">演示账户：</p>
            <p>用户名: demo</p>
            <p>密码: demo123</p>
          </div>
        </div>
      </div>
    </div>
  );
};
