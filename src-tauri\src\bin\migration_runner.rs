// 独立的数据库迁移运行器二进制文件
// 可以独立运行来管理数据库迁移

use paolife::infrastructure::database::migration_runner;

#[tokio::main]
async fn main() {
    // 初始化日志
    tracing_subscriber::fmt()
        .with_env_filter(tracing_subscriber::EnvFilter::from_default_env())
        .init();

    // 运行CLI
    if let Err(e) = migration_runner::run_cli().await {
        eprintln!("❌ Error: {}", e);
        std::process::exit(1);
    }
}
