# PaoLife项目详细技术文档

## 第六部分：资源管理页面详细分析

### 页面概览

资源管理模块是PaoLife应用的知识管理核心，包含资源页面 (`ResourcesPage.tsx`)。实现了完整的文档管理系统，包括文件树导航、Markdown编辑器、双向链接系统、引用分析和知识图谱等功能。

### 核心架构设计

#### 1. 资源页面布局 (ResourcesPage.tsx)

**三栏布局结构**:
```typescript
// 资源页面状态管理
const [selectedFile, setSelectedFile] = useState<FileTreeItem | null>(null)
const [showBacklinks, setShowBacklinks] = useState(true)  // 默认显示引用面板
const [showAnalytics, setShowAnalytics] = useState(false)
const [refreshTrigger, setRefreshTrigger] = useState(0)
const [currentFileContent, setCurrentFileContent] = useState('')
const [currentEditorContent, setCurrentEditorContent] = useState('')
const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

// 专注模式和布局控制
const isFocusMode = settings.focusMode
const showRightPanel = showBacklinks || showAnalytics
```

**布局响应式设计**:
```typescript
// 三栏布局配置
const LayoutConfig = {
  // 正常模式：文件树(1) + 编辑器(2) + 引用面板(1)
  normal: 'grid-cols-1 lg:grid-cols-4',
  fileTree: 'lg:col-span-1',
  editor: 'lg:col-span-2', 
  referencePanel: 'lg:col-span-1',
  
  // 专注模式：仅编辑器(4)
  focus: 'lg:col-span-4'
}

// 动态布局计算
const getEditorColSpan = () => {
  if (isFocusMode) return 'lg:col-span-4'
  if (!showRightPanel) return 'lg:col-span-3'
  return 'lg:col-span-2'
}
```

**文件操作处理**:
```typescript
// 文件选择和打开
const handleFileSelect = async (file: FileTreeItem) => {
  setSelectedFile(file)
  
  // 检查未保存更改
  if (hasUnsavedChanges) {
    const shouldSave = await confirm({
      title: '保存更改',
      description: '当前文件有未保存的更改，是否保存？',
      confirmText: '保存',
      cancelText: '不保存'
    })
    
    if (shouldSave) {
      await handleSaveFile()
    }
  }

  // 如果是Markdown文件，在编辑器中打开
  if (file.type === 'file' && file.name.endsWith('.md')) {
    try {
      const realPath = await convertToRealPath(file.path, settings)
      const result = await fileSystemApi.readFile({ path: realPath })
      
      if (result.success && result.data) {
        const content = result.data.content || ''
        setCurrentFileContent(content)
        setCurrentEditorContent(content)
        setHasUnsavedChanges(false)
      }
    } catch (error) {
      addNotification({
        type: 'error',
        title: '文件打开失败',
        message: error.message
      })
    }
  }
}

// 文件创建处理
const handleFileCreate = async (parentPath: string, name: string, type: 'file' | 'folder') => {
  try {
    const realParentPath = await convertToRealPath(parentPath, settings)
    const fullPath = `${realParentPath}/${name}`
    
    if (type === 'file') {
      // 创建Markdown文件
      const defaultContent = `# ${name.replace('.md', '')}\n\n创建时间: ${new Date().toLocaleString()}\n`
      await fileSystemApi.writeFile({
        path: fullPath,
        content: defaultContent
      })
    } else {
      // 创建文件夹
      await fileSystemApi.createDirectory(fullPath)
    }
    
    // 刷新文件树
    setRefreshTrigger(prev => prev + 1)
    
    addNotification({
      type: 'success',
      title: `${type === 'file' ? '文件' : '文件夹'}创建成功`,
      message: `${name} 已创建`
    })
  } catch (error) {
    addNotification({
      type: 'error',
      title: '创建失败',
      message: error.message
    })
  }
}
```

#### 2. 文件树组件 (FileTree.tsx)

**文件系统集成**:
```typescript
// 文件树数据结构
interface FileTreeItem {
  id: string
  name: string
  path: string  // 虚拟路径
  type: 'file' | 'folder'
  size?: number
  modifiedAt?: string
  children?: FileTreeItem[]
  isExpanded?: boolean
}

// 递归加载目录结构
const loadDirectoryRecursive = async (dirPath: string): Promise<FileTreeItem[]> => {
  try {
    const result = await fileSystemApi.listDirectory(dirPath)
    if (!result.success || !result.data) return []

    const items: FileTreeItem[] = []
    
    for (const item of result.data) {
      const itemPath = `${dirPath}/${item.name}`
      const fileTreeItem: FileTreeItem = {
        id: itemPath,
        name: item.name,
        path: itemPath,
        type: item.type,
        size: item.size,
        modifiedAt: item.modifiedAt
      }

      // 如果是文件夹，递归加载子项
      if (item.type === 'folder') {
        fileTreeItem.children = await loadDirectoryRecursive(itemPath)
      }

      items.push(fileTreeItem)
    }

    // 排序：文件夹优先，然后按名称排序
    return items.sort((a, b) => {
      if (a.type !== b.type) {
        return a.type === 'folder' ? -1 : 1
      }
      return a.name.localeCompare(b.name)
    })
  } catch (error) {
    console.error('加载目录失败:', error)
    return []
  }
}
```

**文件搜索功能**:
```typescript
// 文件搜索过滤
const filterFiles = (files: FileTreeItem[], query: string): FileTreeItem[] => {
  if (!query.trim()) return files

  const searchTerm = query.toLowerCase()
  
  const filterRecursive = (items: FileTreeItem[]): FileTreeItem[] => {
    const filtered: FileTreeItem[] = []
    
    for (const item of items) {
      const nameMatch = item.name.toLowerCase().includes(searchTerm)
      
      if (item.type === 'folder') {
        const filteredChildren = filterRecursive(item.children || [])
        
        // 如果文件夹名匹配或有匹配的子项，则包含此文件夹
        if (nameMatch || filteredChildren.length > 0) {
          filtered.push({
            ...item,
            children: filteredChildren,
            isExpanded: filteredChildren.length > 0 // 自动展开有匹配项的文件夹
          })
        }
      } else if (nameMatch) {
        filtered.push(item)
      }
    }
    
    return filtered
  }
  
  return filterRecursive(files)
}

// 搜索组件
const FileSearchInput = ({ searchQuery, onSearchChange }) => (
  <div className="p-3 border-b">
    <div className="relative">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
      <Input
        type="text"
        placeholder="搜索文件..."
        value={searchQuery}
        onChange={(e) => onSearchChange(e.target.value)}
        className="pl-10 pr-4 py-2 text-sm"
      />
      {searchQuery && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
          onClick={() => onSearchChange('')}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  </div>
)
```

**文件树节点组件**:
```typescript
const FileTreeNode = ({ 
  item, 
  level, 
  isSelected, 
  isExpanded, 
  onSelect, 
  onToggleExpand,
  onRename,
  onDelete,
  onCreateFile,
  onCreateFolder 
}) => {
  const [isRenaming, setIsRenaming] = useState(false)
  const [newName, setNewName] = useState(item.name)

  return (
    <div className="file-tree-node">
      <div
        className={cn(
          'flex items-center gap-2 px-2 py-1 text-sm cursor-pointer hover:bg-gray-100 rounded',
          isSelected && 'bg-blue-100 text-blue-800',
          'transition-colors duration-150'
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => onSelect(item)}
      >
        {/* 展开/折叠图标 */}
        {item.type === 'folder' && (
          <Button
            variant="ghost"
            size="sm"
            className="h-4 w-4 p-0"
            onClick={(e) => {
              e.stopPropagation()
              onToggleExpand(item)
            }}
          >
            {isExpanded ? (
              <ChevronDown className="h-3 w-3" />
            ) : (
              <ChevronRight className="h-3 w-3" />
            )}
          </Button>
        )}

        {/* 文件/文件夹图标 */}
        <div className="flex-shrink-0">
          {item.type === 'folder' ? (
            <Folder className="h-4 w-4 text-blue-500" />
          ) : (
            <FileText className="h-4 w-4 text-gray-500" />
          )}
        </div>

        {/* 文件名 */}
        {isRenaming ? (
          <Input
            value={newName}
            onChange={(e) => setNewName(e.target.value)}
            onBlur={() => handleRenameSubmit()}
            onKeyDown={(e) => {
              if (e.key === 'Enter') handleRenameSubmit()
              if (e.key === 'Escape') setIsRenaming(false)
            }}
            className="h-6 text-xs"
            autoFocus
          />
        ) : (
          <span className="flex-1 truncate">{item.name}</span>
        )}

        {/* 操作菜单 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100"
            >
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={() => setIsRenaming(true)}>
              <Edit className="h-4 w-4 mr-2" />
              重命名
            </DropdownMenuItem>
            {item.type === 'folder' && (
              <>
                <DropdownMenuItem onClick={() => onCreateFile(item)}>
                  <FileText className="h-4 w-4 mr-2" />
                  新建文件
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => onCreateFolder(item)}>
                  <Folder className="h-4 w-4 mr-2" />
                  新建文件夹
                </DropdownMenuItem>
              </>
            )}
            <DropdownMenuItem 
              onClick={() => onDelete(item)}
              className="text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              删除
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* 子项渲染 */}
      {item.type === 'folder' && isExpanded && item.children && (
        <div className="children">
          {item.children.map(child => (
            <FileTreeNode
              key={child.id}
              item={child}
              level={level + 1}
              isSelected={selectedFile?.id === child.id}
              isExpanded={expandedFolders.has(child.id)}
              onSelect={onSelect}
              onToggleExpand={onToggleExpand}
              onRename={onRename}
              onDelete={onDelete}
              onCreateFile={onCreateFile}
              onCreateFolder={onCreateFolder}
            />
          ))}
        </div>
      )}
    </div>
  )
}
```

#### 3. Markdown编辑器系统 (MarkdownEditor.tsx)

**Milkdown Crepe集成**:
```typescript
// 编辑器配置
const CrepeEditorCore = ({
  initialValue,
  onChange,
  readonly,
  placeholder,
  wikiLinkConfig,
  onPageClick,
  onLinkCreate,
  currentPageName,
  theme
}) => {
  const lastContentRef = useRef<string>('')
  const isUpdatingProgrammaticallyRef = useRef<boolean>(false)

  // 使用官方的useEditor钩子
  const { get } = useEditor((root) => {
    // 根据用户设置配置编辑器功能
    const features: Record<string, any> = {
      [Crepe.Feature.Toolbar]: !settings.focusMode, // 专注模式下隐藏工具栏
      [Crepe.Feature.BlockEdit]: false, // 禁用可能影响高度的块编辑功能
      [Crepe.Feature.Cursor]: true,
      [Crepe.Feature.ListItem]: true,   // 恢复列表功能
      [Crepe.Feature.LinkTooltip]: false,  // 禁用内置链接功能，避免与 WikiLink 冲突
      [Crepe.Feature.ImageBlock]: false, // 禁用可能影响高度的图片块功能
      [Crepe.Feature.Placeholder]: true,
      [Crepe.Feature.Table]: true,      // 恢复表格功能
      [Crepe.Feature.Latex]: false,
      [Crepe.Feature.CodeMirror]: false // 禁用CodeMirror，可能影响滚动
    }

    const crepe = new Crepe({
      root,
      defaultValue: '', // 始终使用空字符串，避免分屏问题
      features
    })

    // 配置WikiLink插件
    if (wikiLinkConfig) {
      crepe.use(wikiLinkPlugin(wikiLinkConfig))
    }

    return crepe
  })

  // 内容变化处理
  const handleContentChange = useCallback((markdown: string) => {
    if (isUpdatingProgrammaticallyRef.current) {
      return // 跳过程序化更新
    }

    if (markdown !== lastContentRef.current) {
      lastContentRef.current = markdown
      onChange?.(markdown)

      // 触发双向链接更新
      if (currentPageName && onLinkCreate) {
        updateBidirectionalLinks(currentPageName, markdown)
      }
    }
  }, [onChange, currentPageName, onLinkCreate])

  return (
    <div className={`milkdown-editor h-full flex flex-col overflow-hidden editor-fixed-height ${editorThemeClass} ${focusModeClass}`}>
      <Milkdown />
    </div>
  )
}
```

**WikiLink配置系统**:
```typescript
// WikiLink配置
const wikiLinkConfig = useMemo(() => {
  return {
    enableAutoComplete: false,  // 自动补全功能已移除
    enablePreview: true,  // 启用预览功能
    enableBidirectionalLinks: true,
    previewMaxLines: 5,
    autoCreatePages: false,

    // 读取页面内容用于预览
    readPageContent: async (pageName: string) => {
      try {
        const filePath = await resolvePagePath(pageName)
        const result = await fileSystemApi.readFile({ path: filePath })

        if (result.success && result.data) {
          return result.data.content || ''
        }
        return null
      } catch (error) {
        console.error('读取页面内容失败:', error)
        return null
      }
    },

    // 保存页面内容
    savePageContent: async (pageName: string, content: string) => {
      try {
        const filePath = await resolvePagePath(pageName)
        const result = await fileSystemApi.writeFile({
          path: filePath,
          content: content
        })

        if (result.success) {
          // 如果保存的是当前打开的文件，更新编辑器内容
          if (selectedFile && selectedFile.path === filePath) {
            setCurrentEditorContent(content)
          }

          addNotification({
            type: 'success',
            title: '保存成功',
            message: `${pageName} 已保存`
          })
        }
      } catch (error) {
        addNotification({
          type: 'error',
          title: '保存失败',
          message: error.message
        })
      }
    },

    // 检查页面是否存在
    pageExists: async (pageName: string) => {
      try {
        const filePath = await resolvePagePath(pageName)
        const result = await fileSystemApi.fileExists(filePath)
        return result.success && result.data
      } catch (error) {
        return false
      }
    },

    // 获取所有页面列表（用于自动补全）
    getAllPages: async () => {
      try {
        const resourcesPath = await getResourcesPath(settings)
        const allFiles = await getAllMarkdownFiles(resourcesPath)
        return allFiles.map(file => ({
          name: file.name.replace('.md', ''),
          path: file.path
        }))
      } catch (error) {
        return []
      }
    }
  }
}, [settings, selectedFile, addNotification])
```

**WikiLink智能跳转**:
```typescript
// WikiLink点击处理
const handleWikiLinkClick = useCallback(async (pageName: string) => {
  try {
    const filePath = await resolvePagePath(pageName)
    const existsResult = await fileSystemApi.fileExists(filePath)

    if (existsResult.success && existsResult.data) {
      // 文件存在，直接打开
      await openExistingFile(filePath)
    } else {
      // 文件不存在，显示创建确认对话框
      const ok = await confirm({
        title: '创建新文件',
        description: `文件 "${pageName}" 不存在，是否创建？`,
        variant: 'default',
        confirmText: '创建',
        cancelText: '取消'
      })

      if (ok) {
        await createAndOpenNewFile(filePath, pageName)
      }
    }
  } catch (error) {
    addNotification({
      type: 'error',
      title: '跳转失败',
      message: error.message
    })
  }
}, [settings, addNotification, openExistingFile])

// 创建并打开新文件
const createAndOpenNewFile = useCallback(async (filePath: string, pageName: string) => {
  try {
    const resourcesPath = await getResourcesPath(settings)
    const normalizedName = normalizePageName(pageName)
    const defaultContent = `# ${pageName}\n\n创建时间: ${new Date().toLocaleString()}\n`

    const success = await createPage(normalizedName, resourcesPath, defaultContent)
    if (success) {
      await openExistingFile(filePath)
      addNotification({
        type: 'success',
        title: '文件已创建',
        message: `${pageName}.md 已创建并打开`
      })
    } else {
      throw new Error('创建文件失败')
    }
  } catch (error) {
    addNotification({
      type: 'error',
      title: '创建失败',
      message: error.message
    })
  }
}, [settings, addNotification, openExistingFile])
```

#### 4. 双向链接系统

**双向链接服务架构**:
```typescript
// 双向链接数据接口
interface BidirectionalLinkData {
  id: string
  sourceDocPath: string
  sourceDocTitle?: string
  targetDocPath: string
  targetDocTitle?: string
  linkText: string
  displayText?: string
  linkType: string
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
  contextBefore?: string
  contextAfter?: string
  isValid: boolean
  linkStrength: number
  createdAt: string
  updatedAt: string
  lastValidated: string
}

// 双向链接服务类
export class BidirectionalLinkService {
  /**
   * 获取文档的双向链接数据
   */
  async getDocumentLinks(docPath: string): Promise<{
    backlinks: BidirectionalLinkData[]
    outlinks: BidirectionalLinkData[]
    statistics: LinkStatistics
  }> {
    try {
      const result = await databaseApi.getDocumentLinks(docPath)

      if (!result.success || !result.data) {
        return {
          backlinks: [],
          outlinks: [],
          statistics: this.createEmptyStatistics()
        }
      }

      const { backlinks, outlinks } = result.data

      // 转换数据格式
      const backlinkData = backlinks.map(this.convertToLinkData)
      const outlinkData = outlinks.map(this.convertToLinkData)

      // 计算统计信息
      const statistics = this.calculateStatistics(backlinkData, outlinkData)

      return {
        backlinks: backlinkData,
        outlinks: outlinkData,
        statistics
      }
    } catch (error) {
      console.error('获取文档链接失败:', error)
      return {
        backlinks: [],
        outlinks: [],
        statistics: this.createEmptyStatistics()
      }
    }
  }

  /**
   * 批量替换文档的链接
   */
  async replaceDocumentLinks(
    sourceDocPath: string,
    links: CreateLinkRequest[]
  ): Promise<BidirectionalLinkData[]> {
    try {
      const result = await databaseApi.replaceDocumentLinks({ sourceDocPath, links })

      if (result.success && result.data) {
        return result.data.map(this.convertToLinkData)
      }

      return []
    } catch (error) {
      console.error('替换文档链接失败:', error)
      return []
    }
  }

  /**
   * 计算链接统计信息
   */
  private calculateStatistics(
    backlinks: BidirectionalLinkData[],
    outlinks: BidirectionalLinkData[]
  ): LinkStatistics {
    const totalLinks = backlinks.length + outlinks.length
    const validLinks = [...backlinks, ...outlinks].filter(link => link.isValid).length
    const invalidLinks = totalLinks - validLinks

    const allLinks = [...backlinks, ...outlinks]
    const linkStrengthAvg = allLinks.length > 0
      ? allLinks.reduce((sum, link) => sum + link.linkStrength, 0) / allLinks.length
      : 0

    return {
      totalLinks,
      validLinks,
      invalidLinks,
      backlinkCount: backlinks.length,
      outlinkCount: outlinks.length,
      linkStrengthAvg
    }
  }
}
```

#### 5. 统一引用服务 (UnifiedReferenceService)

**统一引用数据结构**:
```typescript
// 统一引用接口
export interface UnifiedReference {
  id: string
  sourceType: 'document' | 'project' | 'area'
  sourceId: string
  sourcePath?: string
  sourceTitle: string
  targetType: 'document'
  targetPath: string
  targetTitle: string
  referenceType: 'wikilink' | 'description' | 'task' | 'note'
  context: string
  strength: number // 引用强度 0-1
  createdAt: string
  updatedAt: string
}

// WikiLink引用扩展
export interface WikiLinkReference extends UnifiedReference {
  sourceType: 'document'
  referenceType: 'wikilink'
  linkText: string
  displayText?: string
  startPosition: number
  endPosition: number
  lineNumber: number
  columnNumber: number
}

// 项目引用扩展
export interface ProjectReference extends UnifiedReference {
  sourceType: 'project'
  referenceType: 'description' | 'task'
  projectId: string
  taskId?: string
}

// 领域引用扩展
export interface AreaReference extends UnifiedReference {
  sourceType: 'area'
  referenceType: 'note'
  areaId: string
}
```

**统一引用服务类**:
```typescript
export class UnifiedReferenceService {
  private cache = new Map<string, { references: UnifiedReference[], timestamp: number }>()
  private pendingQueries = new Map<string, Promise<UnifiedReference[]>>()
  private readonly CACHE_TTL = 5 * 60 * 1000 // 5分钟缓存

  /**
   * 获取文档的所有引用
   */
  async getAllReferences(documentPath: string): Promise<UnifiedReference[]> {
    console.log('🔍 获取文档的所有引用:', documentPath)

    // 检查缓存
    const cached = this.getCachedReferences(documentPath)
    if (cached) {
      console.log('📋 使用缓存的引用数据')
      return cached.references
    }

    // 检查是否有正在进行的查询
    const pendingQuery = this.pendingQueries.get(documentPath)
    if (pendingQuery) {
      console.log('⏳ 等待正在进行的查询完成')
      return await pendingQuery
    }

    console.log('🔄 缓存未命中，开始新查询')

    // 创建查询Promise并存储
    const queryPromise = this.performQuery(documentPath)
    this.pendingQueries.set(documentPath, queryPromise)

    try {
      const result = await queryPromise
      return result
    } finally {
      // 查询完成后清理
      this.pendingQueries.delete(documentPath)
    }
  }

  /**
   * 执行实际的查询操作
   */
  private async performQuery(documentPath: string): Promise<UnifiedReference[]> {
    try {
      // 并行获取所有类型的引用
      const [wikiLinkRefs, projectRefs, areaRefs, resourceLinkRefs] = await Promise.all([
        this.getWikiLinkReferences(documentPath),
        this.getProjectReferences(documentPath),
        this.getAreaReferences(documentPath),
        this.getResourceLinkReferences(documentPath)
      ])

      // 合并所有引用
      const allReferences: UnifiedReference[] = [
        ...wikiLinkRefs,
        ...projectRefs,
        ...areaRefs,
        ...resourceLinkRefs
      ]

      // 缓存结果
      this.setCachedReferences(documentPath, allReferences)

      console.log('✅ 统一引用查询完成:', {
        文档路径: documentPath,
        WikiLink引用: wikiLinkRefs.length,
        项目引用: projectRefs.length,
        领域引用: areaRefs.length,
        资源链接引用: resourceLinkRefs.length,
        总引用数: allReferences.length
      })

      return allReferences
    } catch (error) {
      console.error('❌ 统一引用查询失败:', error)
      return []
    }
  }

  /**
   * 获取 WikiLink 引用
   */
  private async getWikiLinkReferences(documentPath: string): Promise<WikiLinkReference[]> {
    try {
      const { bidirectionalLinkService } = await import('./bidirectionalLinkService')
      const linkData = await bidirectionalLinkService.getDocumentLinks(documentPath)

      // 合并反向链接和出链
      const allLinks = [...linkData.backlinks, ...linkData.outlinks]

      return allLinks.map(link => ({
        id: `wikilink-${link.id}`,
        sourceType: 'document' as const,
        sourceId: link.sourceDocPath,
        sourcePath: link.sourceDocPath,
        sourceTitle: link.sourceDocTitle || link.sourceDocPath.split('/').pop()?.replace('.md', '') || link.sourceDocPath,
        targetType: 'document' as const,
        targetPath: link.targetDocPath,
        targetTitle: link.targetDocTitle || link.targetDocPath.split('/').pop()?.replace('.md', '') || link.targetDocPath,
        referenceType: 'wikilink' as const,
        context: `${link.contextBefore || ''}[[${link.linkText}]]${link.contextAfter || ''}`,
        strength: link.linkStrength,
        createdAt: link.createdAt,
        updatedAt: link.updatedAt,
        linkText: link.linkText,
        displayText: link.displayText,
        startPosition: link.startPosition,
        endPosition: link.endPosition,
        lineNumber: link.lineNumber,
        columnNumber: link.columnNumber
      }))
    } catch (error) {
      console.error('❌ 获取WikiLink引用失败:', error)
      return []
    }
  }

  /**
   * 计算引用统计信息
   */
  calculateStatistics(references: UnifiedReference[]): ReferenceStatistics {
    const wikiLinks = references.filter(ref => ref.referenceType === 'wikilink').length
    const projectReferences = references.filter(ref =>
      ref.referenceType === 'task' || ref.referenceType === 'description'
    ).length
    const areaReferences = references.filter(ref => ref.referenceType === 'note').length

    const totalReferences = references.length
    const averageStrength = totalReferences > 0
      ? references.reduce((sum, ref) => sum + ref.strength, 0) / totalReferences
      : 0

    // 计算引用类型分布
    const typeDistribution = {
      wikilink: wikiLinks,
      project: projectReferences,
      area: areaReferences
    }

    return {
      totalReferences,
      wikiLinks,
      projectReferences,
      areaReferences,
      averageStrength,
      typeDistribution,
      strongReferences: references.filter(ref => ref.strength > 0.7).length,
      weakReferences: references.filter(ref => ref.strength < 0.3).length
    }
  }
}
```

#### 6. 统一引用面板 (UnifiedReferencePanel)

**引用面板组件架构**:
```typescript
const UnifiedReferencePanel = ({ documentPath, onReferenceClick, className }) => {
  const [references, setReferences] = useState<UnifiedReference[]>([])
  const [statistics, setStatistics] = useState<ReferenceStatistics | null>(null)
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState<'all' | 'wikilinks' | 'projects' | 'areas'>('all')
  const [sortBy, setSortBy] = useState<'strength' | 'created' | 'updated' | 'title'>('strength')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')

  // 加载引用数据
  useEffect(() => {
    if (!documentPath) return

    const loadReferences = async () => {
      setLoading(true)
      try {
        console.log('🔄 加载统一引用数据:', documentPath)
        const allReferences = await unifiedReferenceService.getAllReferences(documentPath)
        const stats = unifiedReferenceService.calculateStatistics(allReferences)

        setReferences(allReferences)
        setStatistics(stats)

        console.log('✅ 统一引用数据加载完成:', {
          总引用数: allReferences.length,
          统计: stats
        })
      } catch (err) {
        console.error('❌ 加载统一引用数据失败:', err)
      } finally {
        setLoading(false)
      }
    }

    loadReferences()
  }, [documentPath])

  // 过滤和排序引用
  const filteredAndSortedReferences = useMemo(() => {
    let filtered = references

    // 按标签页过滤
    switch (activeTab) {
      case 'wikilinks':
        filtered = references.filter(ref => ref.referenceType === 'wikilink')
        break
      case 'projects':
        filtered = references.filter(ref =>
          ref.referenceType === 'task' || ref.referenceType === 'description'
        )
        break
      case 'areas':
        filtered = references.filter(ref => ref.referenceType === 'note')
        break
      default:
        filtered = references
    }

    // 排序
    return filtered.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'strength':
          comparison = a.strength - b.strength
          break
        case 'created':
          comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
          break
        case 'updated':
          comparison = new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime()
          break
        case 'title':
          comparison = a.sourceTitle.localeCompare(b.sourceTitle)
          break
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })
  }, [references, activeTab, sortBy, sortOrder])

  return (
    <div className={`unified-reference-panel h-full flex flex-col ${className}`}>
      {/* 头部统计 */}
      <div className="header-section p-4 border-b bg-gray-50">
        <h3 className="text-lg font-semibold mb-2">引用关系</h3>
        {statistics && (
          <div className="grid grid-cols-2 gap-2 text-sm">
            <div className="stat-item">
              <span className="text-gray-600">总引用:</span>
              <span className="font-medium ml-1">{statistics.totalReferences}</span>
            </div>
            <div className="stat-item">
              <span className="text-gray-600">平均强度:</span>
              <span className="font-medium ml-1">{statistics.averageStrength.toFixed(2)}</span>
            </div>
            <div className="stat-item">
              <span className="text-gray-600">强引用:</span>
              <span className="font-medium ml-1 text-green-600">{statistics.strongReferences}</span>
            </div>
            <div className="stat-item">
              <span className="text-gray-600">弱引用:</span>
              <span className="font-medium ml-1 text-orange-600">{statistics.weakReferences}</span>
            </div>
          </div>
        )}
      </div>

      {/* 标签页 */}
      <div className="tabs-section border-b">
        <div className="flex">
          {[
            { key: 'all', label: '全部', count: references.length },
            { key: 'wikilinks', label: 'WikiLink', count: statistics?.wikiLinks || 0 },
            { key: 'projects', label: '项目', count: statistics?.projectReferences || 0 },
            { key: 'areas', label: '领域', count: statistics?.areaReferences || 0 }
          ].map(tab => (
            <button
              key={tab.key}
              type="button"
              className={`tab-button px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
                activeTab === tab.key
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
              onClick={() => setActiveTab(tab.key as any)}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      </div>

      {/* 排序控制 */}
      <div className="sort-section p-2 border-b bg-gray-50">
        <div className="flex items-center gap-2 text-sm">
          <span className="text-gray-600">排序:</span>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-24 h-7">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="strength">强度</SelectItem>
              <SelectItem value="created">创建</SelectItem>
              <SelectItem value="updated">更新</SelectItem>
              <SelectItem value="title">标题</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc')}
            className="h-7 w-7 p-0"
          >
            {sortOrder === 'desc' ? <ArrowDown className="h-3 w-3" /> : <ArrowUp className="h-3 w-3" />}
          </Button>
        </div>
      </div>

      {/* 引用列表 */}
      <div className="references-list flex-1 overflow-y-auto">
        {filteredAndSortedReferences.length === 0 ? (
          <div className="p-4 text-center text-gray-500">
            <div className="mb-2">📭</div>
            <div>暂无{activeTab === 'all' ? '' : activeTab === 'wikilinks' ? 'WikiLink' : activeTab === 'projects' ? '项目' : '领域'}引用</div>
          </div>
        ) : (
          <div className="space-y-2 p-2">
            {filteredAndSortedReferences.map(reference => (
              <ReferenceItem
                key={reference.id}
                reference={reference}
                onClick={() => onReferenceClick?.(reference)}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
```

**引用项组件**:
```typescript
const ReferenceItem = ({ reference, onClick }) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'wikilink': return <Link className="h-4 w-4 text-blue-500" />
      case 'task': return <CheckSquare className="h-4 w-4 text-green-500" />
      case 'description': return <FileText className="h-4 w-4 text-purple-500" />
      case 'note': return <StickyNote className="h-4 w-4 text-orange-500" />
      default: return <Circle className="h-4 w-4 text-gray-500" />
    }
  }

  const getStrengthColor = (strength: number) => {
    if (strength > 0.7) return 'text-green-600 bg-green-100'
    if (strength > 0.4) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  return (
    <div
      className="reference-item p-3 border rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
      onClick={onClick}
    >
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0 mt-0.5">
          {getTypeIcon(reference.referenceType)}
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-1">
            <h4 className="font-medium text-sm truncate">
              {reference.sourceTitle}
            </h4>
            <Badge
              variant="secondary"
              className={`text-xs ${getStrengthColor(reference.strength)}`}
            >
              {(reference.strength * 100).toFixed(0)}%
            </Badge>
          </div>

          <p className="text-xs text-gray-600 line-clamp-2 mb-1">
            {reference.context}
          </p>

          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span>{reference.referenceType}</span>
            <span>•</span>
            <span>{new Date(reference.updatedAt).toLocaleDateString()}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
```

### 总结

资源管理模块作为PaoLife应用的知识管理核心，具有以下特点：

1. **完整的文档管理**: 文件树导航、Markdown编辑、文件操作
2. **智能双向链接**: WikiLink语法、自动链接发现、引用网络
3. **统一引用系统**: 整合文档、项目、领域的引用关系
4. **可视化分析**: 引用强度、网络图谱、统计分析
5. **专注模式支持**: 可切换的界面布局、专注写作体验
6. **实时同步**: 文件监控、缓存机制、增量更新

这个设计为用户提供了专业级的知识管理能力，支持复杂的文档关联和知识网络构建。
