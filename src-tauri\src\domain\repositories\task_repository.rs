// 任务仓储接口
// 定义任务数据访问的抽象接口

use crate::domain::entities::task::{Task, CreateTaskData, UpdateTaskData, TaskQuery, TaskStats};
use crate::shared::types::{EntityId, QueryParams, TaskStatus, Priority};
use crate::shared::errors::AppResult;
use async_trait::async_trait;
use chrono::NaiveDate;

/// 任务仓储接口
#[async_trait]
pub trait TaskRepository: Send + Sync {
    /// 创建任务
    async fn create(&self, data: CreateTaskData) -> AppResult<Task>;

    /// 根据ID查找任务
    async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<Task>>;

    /// 更新任务
    async fn update(&self, id: &EntityId, data: UpdateTaskData) -> AppResult<Task>;

    /// 删除任务
    async fn delete(&self, id: &EntityId) -> AppResult<()>;

    /// 查询任务列表
    async fn find_all(&self, query: TaskQuery, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 统计任务数量
    async fn count(&self, query: TaskQuery) -> AppResult<u64>;

    /// 根据项目ID查找任务
    async fn find_by_project_id(&self, project_id: &EntityId, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 根据领域ID查找任务
    async fn find_by_area_id(&self, area_id: &EntityId, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 根据父任务ID查找子任务
    async fn find_by_parent_id(&self, parent_id: &EntityId, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 根据分配者查找任务
    async fn find_by_assignee(&self, assignee_id: &EntityId, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 根据状态查找任务
    async fn find_by_status(&self, status: TaskStatus, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 根据优先级查找任务
    async fn find_by_priority(&self, priority: Priority, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 查找根任务（没有父任务的任务）
    async fn find_root_tasks(&self, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 查找逾期任务
    async fn find_overdue(&self, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 查找即将到期的任务
    async fn find_due_soon(&self, days: i32, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 查找活跃任务
    async fn find_active(&self, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 查找已完成任务
    async fn find_completed(&self, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 获取任务层级结构
    async fn get_task_hierarchy(&self, root_task_id: Option<&EntityId>) -> AppResult<Vec<TaskHierarchy>>;

    /// 更新任务状态
    async fn update_status(&self, id: &EntityId, status: TaskStatus) -> AppResult<()>;

    /// 更新任务完成百分比
    async fn update_completion_percentage(&self, id: &EntityId, percentage: u8) -> AppResult<()>;

    /// 更新任务排序
    async fn update_sort_order(&self, id: &EntityId, sort_order: i32) -> AppResult<()>;

    /// 批量更新任务排序
    async fn batch_update_sort_order(&self, updates: Vec<(EntityId, i32)>) -> AppResult<()>;

    /// 添加工作时间
    async fn add_work_time(&self, id: &EntityId, minutes: u32) -> AppResult<()>;

    /// 移动任务到不同的父任务下
    async fn move_task(&self, id: &EntityId, new_parent_id: Option<&EntityId>) -> AppResult<()>;

    /// 复制任务
    async fn copy_task(&self, id: &EntityId, new_parent_id: Option<&EntityId>) -> AppResult<Task>;

    /// 获取任务统计信息
    async fn get_stats(&self, id: &EntityId) -> AppResult<TaskStats>;

    /// 获取项目的任务统计
    async fn get_project_task_stats(&self, project_id: &EntityId) -> AppResult<ProjectTaskStats>;

    /// 获取领域的任务统计
    async fn get_area_task_stats(&self, area_id: &EntityId) -> AppResult<AreaTaskStats>;

    /// 获取用户的任务统计
    async fn get_user_task_stats(&self, user_id: &EntityId) -> AppResult<UserTaskStats>;

    /// 批量更新任务状态
    async fn batch_update_status(&self, ids: Vec<EntityId>, status: TaskStatus) -> AppResult<u64>;

    /// 搜索任务
    async fn search(&self, keyword: &str, params: QueryParams) -> AppResult<Vec<Task>>;

    /// 获取任务依赖关系
    async fn get_task_dependencies(&self, id: &EntityId) -> AppResult<Vec<TaskDependency>>;

    /// 添加任务依赖
    async fn add_dependency(&self, task_id: &EntityId, depends_on_id: &EntityId) -> AppResult<()>;

    /// 移除任务依赖
    async fn remove_dependency(&self, task_id: &EntityId, depends_on_id: &EntityId) -> AppResult<()>;
}

/// 任务层级结构
#[derive(Debug, Clone)]
pub struct TaskHierarchy {
    pub task: Task,
    pub level: u32,
    pub path: String,
    pub children: Vec<TaskHierarchy>,
}

/// 项目任务统计
#[derive(Debug, Clone)]
pub struct ProjectTaskStats {
    pub total_tasks: u64,
    pub active_tasks: u64,
    pub completed_tasks: u64,
    pub overdue_tasks: u64,
    pub completion_rate: f64,
    pub average_completion_percentage: f64,
}

/// 领域任务统计
#[derive(Debug, Clone)]
pub struct AreaTaskStats {
    pub total_tasks: u64,
    pub active_tasks: u64,
    pub completed_tasks: u64,
    pub overdue_tasks: u64,
    pub completion_rate: f64,
    pub average_completion_percentage: f64,
}

/// 用户任务统计
#[derive(Debug, Clone)]
pub struct UserTaskStats {
    pub total_assigned_tasks: u64,
    pub active_assigned_tasks: u64,
    pub completed_assigned_tasks: u64,
    pub overdue_assigned_tasks: u64,
    pub completion_rate: f64,
    pub average_completion_time_days: Option<f64>,
}

/// 任务依赖关系
#[derive(Debug, Clone)]
pub struct TaskDependency {
    pub task_id: EntityId,
    pub depends_on_id: EntityId,
    pub dependency_type: DependencyType,
    pub created_at: chrono::DateTime<chrono::Utc>,
}

/// 依赖类型
#[derive(Debug, Clone)]
pub enum DependencyType {
    FinishToStart,  // 前置任务完成后才能开始
    StartToStart,   // 前置任务开始后才能开始
    FinishToFinish, // 前置任务完成后才能完成
    StartToFinish,  // 前置任务开始后才能完成
}
