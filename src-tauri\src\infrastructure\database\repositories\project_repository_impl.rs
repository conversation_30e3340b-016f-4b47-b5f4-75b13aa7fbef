// 项目仓储实现
// 实现项目数据访问的具体逻辑

use crate::domain::entities::project::{Project, CreateProjectData, UpdateProjectData, ProjectQuery, ProjectStats};
use crate::domain::repositories::project_repository::{ProjectRepository, UserProjectStats, AreaProjectStats};
use crate::infrastructure::database::models::ProjectModel;
use crate::infrastructure::database::mappers::{ProjectMapper, Mapper};
use crate::infrastructure::database::DatabaseUtils;
use crate::shared::types::{EntityId, QueryParams, ProjectStatus, Priority};
use crate::shared::errors::{AppError, AppResult};
use async_trait::async_trait;
use sqlx::{SqlitePool, Row};
use chrono::{Utc, NaiveDate};

/// 项目仓储实现
pub struct ProjectRepositoryImpl {
    pool: SqlitePool,
}

impl ProjectRepositoryImpl {
    /// 创建新的项目仓储实现
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }
}

#[async_trait]
impl ProjectRepository for ProjectRepositoryImpl {
    async fn create(&self, data: CreateProjectData) -> AppResult<Project> {
        // 创建项目实体
        let project = Project::new(data)?;
        let model = ProjectMapper::to_model(&project);

        // 插入数据库
        sqlx::query!(
            r#"
            INSERT INTO projects (
                id, name, description, status, progress, priority,
                start_date, deadline, estimated_hours, actual_hours,
                area_id, created_by, created_at, updated_at, archived_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#,
            model.id,
            model.name,
            model.description,
            model.status,
            model.progress,
            model.priority,
            model.start_date,
            model.deadline,
            model.estimated_hours,
            model.actual_hours,
            model.area_id,
            model.created_by,
            model.created_at,
            model.updated_at,
            model.archived_at
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to create project: {}", e)))?;

        Ok(project)
    }

    async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<Project>> {
        let model = sqlx::query_as!(
            ProjectModel,
            "SELECT * FROM projects WHERE id = ?",
            id
        )
        .fetch_optional(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find project by id: {}", e)))?;

        match model {
            Some(model) => Ok(Some(ProjectMapper::to_domain(&model)?)),
            None => Ok(None),
        }
    }

    async fn update(&self, id: &EntityId, data: UpdateProjectData) -> AppResult<Project> {
        // 先获取现有项目
        let mut project = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("项目"))?;

        // 更新项目实体
        project.update(data)?;
        let model = ProjectMapper::to_model(&project);

        // 更新数据库
        sqlx::query!(
            r#"
            UPDATE projects SET
                name = ?, description = ?, status = ?, progress = ?, priority = ?,
                start_date = ?, deadline = ?, estimated_hours = ?, area_id = ?, updated_at = ?
            WHERE id = ?
            "#,
            model.name,
            model.description,
            model.status,
            model.progress,
            model.priority,
            model.start_date,
            model.deadline,
            model.estimated_hours,
            model.area_id,
            model.updated_at,
            model.id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update project: {}", e)))?;

        Ok(project)
    }

    async fn delete(&self, id: &EntityId) -> AppResult<()> {
        let result = sqlx::query!(
            "DELETE FROM projects WHERE id = ?",
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to delete project: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("项目"));
        }

        Ok(())
    }

    async fn find_all(&self, query: ProjectQuery, params: QueryParams) -> AppResult<Vec<Project>> {
        let mut conditions = Vec::new();
        let mut query_params: Vec<String> = Vec::new();

        // 构建查询条件
        if let Some(name) = &query.name {
            conditions.push("name LIKE ?".to_string());
            query_params.push(format!("%{}%", name));
        }

        if let Some(status) = &query.status {
            conditions.push("status = ?".to_string());
            query_params.push(status.to_string());
        }

        if let Some(priority) = &query.priority {
            conditions.push("priority = ?".to_string());
            query_params.push(priority.to_i32().to_string());
        }

        if let Some(area_id) = &query.area_id {
            conditions.push("area_id = ?".to_string());
            query_params.push(area_id.clone());
        }

        if let Some(created_by) = &query.created_by {
            conditions.push("created_by = ?".to_string());
            query_params.push(created_by.clone());
        }

        if let Some(deadline_before) = query.deadline_before {
            conditions.push("deadline <= ?".to_string());
            query_params.push(deadline_before.to_string());
        }

        if let Some(deadline_after) = query.deadline_after {
            conditions.push("deadline >= ?".to_string());
            query_params.push(deadline_after.to_string());
        }

        if let Some(created_after) = query.created_after {
            conditions.push("created_at >= ?".to_string());
            query_params.push(created_after.to_rfc3339());
        }

        if let Some(created_before) = query.created_before {
            conditions.push("created_at <= ?".to_string());
            query_params.push(created_before.to_rfc3339());
        }

        // 添加搜索条件
        if let Some(search) = &params.search {
            let search_condition = DatabaseUtils::build_search_condition(search, &["name", "description"]);
            if !search_condition.is_empty() {
                conditions.push(search_condition);
            }
        }

        // 构建完整查询
        let base_query = "SELECT * FROM projects";
        let sorts = params.sort.as_deref().unwrap_or(&[]);
        let sql = DatabaseUtils::build_query(base_query, conditions, sorts, &params.pagination);

        // 执行查询
        let mut query_builder = sqlx::query_as::<_, ProjectModel>(&sql);
        for param in query_params {
            query_builder = query_builder.bind(param);
        }

        let models = query_builder
            .fetch_all(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to find projects: {}", e)))?;

        ProjectMapper::to_domain_list(models)
    }

    async fn count(&self, query: ProjectQuery) -> AppResult<u64> {
        let mut conditions = Vec::new();
        let mut query_params: Vec<String> = Vec::new();

        // 构建查询条件（与find_all相同的逻辑）
        if let Some(name) = &query.name {
            conditions.push("name LIKE ?".to_string());
            query_params.push(format!("%{}%", name));
        }

        if let Some(status) = &query.status {
            conditions.push("status = ?".to_string());
            query_params.push(status.to_string());
        }

        if let Some(priority) = &query.priority {
            conditions.push("priority = ?".to_string());
            query_params.push(priority.to_i32().to_string());
        }

        if let Some(area_id) = &query.area_id {
            conditions.push("area_id = ?".to_string());
            query_params.push(area_id.clone());
        }

        if let Some(created_by) = &query.created_by {
            conditions.push("created_by = ?".to_string());
            query_params.push(created_by.clone());
        }

        let base_query = "SELECT COUNT(*) FROM projects";
        let sql = format!("{}{}", base_query, DatabaseUtils::build_where_clause(conditions));

        let mut query_builder = sqlx::query(&sql);
        for param in query_params {
            query_builder = query_builder.bind(param);
        }

        let row = query_builder
            .fetch_one(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to count projects: {}", e)))?;

        let count: i64 = row.get(0);
        Ok(count as u64)
    }

    async fn find_by_area_id(&self, area_id: &EntityId, params: QueryParams) -> AppResult<Vec<Project>> {
        let query = ProjectQuery {
            name: None,
            status: None,
            priority: None,
            area_id: Some(area_id.clone()),
            created_by: None,
            deadline_before: None,
            deadline_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_by_creator(&self, creator_id: &EntityId, params: QueryParams) -> AppResult<Vec<Project>> {
        let query = ProjectQuery {
            name: None,
            status: None,
            priority: None,
            area_id: None,
            created_by: Some(creator_id.clone()),
            deadline_before: None,
            deadline_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_by_status(&self, status: ProjectStatus, params: QueryParams) -> AppResult<Vec<Project>> {
        let query = ProjectQuery {
            name: None,
            status: Some(status),
            priority: None,
            area_id: None,
            created_by: None,
            deadline_before: None,
            deadline_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_by_priority(&self, priority: Priority, params: QueryParams) -> AppResult<Vec<Project>> {
        let query = ProjectQuery {
            name: None,
            status: None,
            priority: Some(priority),
            area_id: None,
            created_by: None,
            deadline_before: None,
            deadline_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, params).await
    }

    async fn find_overdue(&self, params: QueryParams) -> AppResult<Vec<Project>> {
        let today = Utc::now().date_naive();
        let query = ProjectQuery {
            name: None,
            status: None,
            priority: None,
            area_id: None,
            created_by: None,
            deadline_before: Some(today),
            deadline_after: None,
            created_after: None,
            created_before: None,
        };

        // 只查找未完成的项目
        let mut projects = self.find_all(query, params).await?;
        projects.retain(|p| !p.status.is_completed());
        Ok(projects)
    }

    async fn find_due_soon(&self, days: i32, params: QueryParams) -> AppResult<Vec<Project>> {
        let today = Utc::now().date_naive();
        let due_date = today + chrono::Duration::days(days as i64);
        
        let query = ProjectQuery {
            name: None,
            status: None,
            priority: None,
            area_id: None,
            created_by: None,
            deadline_before: Some(due_date),
            deadline_after: Some(today),
            created_after: None,
            created_before: None,
        };

        // 只查找活跃的项目
        let mut projects = self.find_all(query, params).await?;
        projects.retain(|p| p.status.is_active());
        Ok(projects)
    }

    async fn find_active(&self, params: QueryParams) -> AppResult<Vec<Project>> {
        let models = sqlx::query_as!(
            ProjectModel,
            r#"
            SELECT * FROM projects 
            WHERE status IN ('in_progress', 'at_risk', 'paused')
            ORDER BY priority DESC, deadline ASC
            LIMIT ? OFFSET ?
            "#,
            params.pagination.as_ref().map(|p| p.limit()).unwrap_or(20),
            params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find active projects: {}", e)))?;

        ProjectMapper::to_domain_list(models)
    }

    async fn find_completed(&self, params: QueryParams) -> AppResult<Vec<Project>> {
        let models = sqlx::query_as!(
            ProjectModel,
            r#"
            SELECT * FROM projects 
            WHERE status IN ('completed', 'archived')
            ORDER BY updated_at DESC
            LIMIT ? OFFSET ?
            "#,
            params.pagination.as_ref().map(|p| p.limit()).unwrap_or(20),
            params.pagination.as_ref().map(|p| p.offset()).unwrap_or(0)
        )
        .fetch_all(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to find completed projects: {}", e)))?;

        ProjectMapper::to_domain_list(models)
    }

    async fn update_status(&self, id: &EntityId, status: ProjectStatus) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE projects SET status = ?, updated_at = ? WHERE id = ?",
            status.to_string(),
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update project status: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("项目"));
        }

        Ok(())
    }

    async fn update_progress(&self, id: &EntityId, progress: u8) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE projects SET progress = ?, updated_at = ? WHERE id = ?",
            progress as i32,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to update project progress: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("项目"));
        }

        Ok(())
    }

    async fn add_work_hours(&self, id: &EntityId, hours: u32) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE projects SET actual_hours = actual_hours + ?, updated_at = ? WHERE id = ?",
            hours as i32,
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to add work hours: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("项目"));
        }

        Ok(())
    }

    async fn archive(&self, id: &EntityId) -> AppResult<()> {
        let now = Utc::now();
        let result = sqlx::query!(
            "UPDATE projects SET status = 'archived', archived_at = ?, updated_at = ? WHERE id = ?",
            now,
            now,
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to archive project: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("项目"));
        }

        Ok(())
    }

    async fn unarchive(&self, id: &EntityId) -> AppResult<()> {
        let result = sqlx::query!(
            "UPDATE projects SET status = 'in_progress', archived_at = NULL, updated_at = ? WHERE id = ?",
            Utc::now(),
            id
        )
        .execute(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to unarchive project: {}", e)))?;

        if result.rows_affected() == 0 {
            return Err(AppError::not_found("项目"));
        }

        Ok(())
    }

    async fn get_stats(&self, id: &EntityId) -> AppResult<ProjectStats> {
        let project = self.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("项目"))?;

        Ok(project.get_stats())
    }

    async fn get_user_project_stats(&self, user_id: &EntityId) -> AppResult<UserProjectStats> {
        let row = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN status IN ('in_progress', 'at_risk', 'paused') THEN 1 END) as active_projects,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
                COUNT(CASE WHEN deadline < date('now') AND status NOT IN ('completed', 'archived') THEN 1 END) as overdue_projects,
                AVG(progress) as average_progress
            FROM projects 
            WHERE created_by = ?
            "#,
            user_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get user project stats: {}", e)))?;

        let total = row.total_projects as u64;
        let completed = row.completed_projects as u64;
        let completion_rate = if total > 0 { completed as f64 / total as f64 } else { 0.0 };

        Ok(UserProjectStats {
            total_projects: total,
            active_projects: row.active_projects as u64,
            completed_projects: completed,
            overdue_projects: row.overdue_projects as u64,
            completion_rate,
            average_progress: row.average_progress.unwrap_or(0.0),
        })
    }

    async fn get_area_project_stats(&self, area_id: &EntityId) -> AppResult<AreaProjectStats> {
        let row = sqlx::query!(
            r#"
            SELECT 
                COUNT(*) as total_projects,
                COUNT(CASE WHEN status IN ('in_progress', 'at_risk', 'paused') THEN 1 END) as active_projects,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_projects,
                COUNT(CASE WHEN deadline < date('now') AND status NOT IN ('completed', 'archived') THEN 1 END) as overdue_projects,
                AVG(progress) as average_progress
            FROM projects 
            WHERE area_id = ?
            "#,
            area_id
        )
        .fetch_one(&self.pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get area project stats: {}", e)))?;

        let total = row.total_projects as u64;
        let completed = row.completed_projects as u64;
        let completion_rate = if total > 0 { completed as f64 / total as f64 } else { 0.0 };

        Ok(AreaProjectStats {
            total_projects: total,
            active_projects: row.active_projects as u64,
            completed_projects: completed,
            overdue_projects: row.overdue_projects as u64,
            completion_rate,
            average_progress: row.average_progress.unwrap_or(0.0),
        })
    }

    async fn batch_update_status(&self, ids: Vec<EntityId>, status: ProjectStatus) -> AppResult<u64> {
        if ids.is_empty() {
            return Ok(0);
        }

        let placeholders = ids.iter().map(|_| "?").collect::<Vec<_>>().join(",");
        let sql = format!(
            "UPDATE projects SET status = ?, updated_at = ? WHERE id IN ({})",
            placeholders
        );

        let mut query = sqlx::query(&sql)
            .bind(status.to_string())
            .bind(Utc::now());

        for id in ids {
            query = query.bind(id);
        }

        let result = query
            .execute(&self.pool)
            .await
            .map_err(|e| AppError::database(format!("Failed to batch update project status: {}", e)))?;

        Ok(result.rows_affected())
    }

    async fn search(&self, keyword: &str, params: QueryParams) -> AppResult<Vec<Project>> {
        let search_params = QueryParams {
            search: Some(keyword.to_string()),
            ..params
        };

        let query = ProjectQuery {
            name: None,
            status: None,
            priority: None,
            area_id: None,
            created_by: None,
            deadline_before: None,
            deadline_after: None,
            created_after: None,
            created_before: None,
        };

        self.find_all(query, search_params).await
    }
}
