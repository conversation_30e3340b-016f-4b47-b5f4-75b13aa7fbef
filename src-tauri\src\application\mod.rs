// 应用层 - 协调领域对象完成业务用例
// 这一层定义应用服务，编排领域服务和仓储来完成具体的业务场景

pub mod use_cases;
pub mod dto;
pub mod services;
pub mod commands;
pub mod queries;

// 重新导出应用层组件
pub use use_cases::*;
pub use dto::*;
pub use services::*;
pub use commands::*;
pub use queries::*;

/// 应用服务工厂
pub struct ApplicationServiceFactory {
    user_service: services::UserService,
    project_service: services::ProjectService,
    task_service: services::TaskService,
    area_service: services::AreaService,
}

impl ApplicationServiceFactory {
    /// 创建新的应用服务工厂
    pub fn new(
        user_service: services::UserService,
        project_service: services::ProjectService,
        task_service: services::TaskService,
        area_service: services::AreaService,
    ) -> Self {
        Self {
            user_service,
            project_service,
            task_service,
            area_service,
        }
    }

    /// 获取用户服务
    pub fn user_service(&self) -> &services::UserService {
        &self.user_service
    }

    /// 获取项目服务
    pub fn project_service(&self) -> &services::ProjectService {
        &self.project_service
    }

    /// 获取任务服务
    pub fn task_service(&self) -> &services::TaskService {
        &self.task_service
    }

    /// 获取领域服务
    pub fn area_service(&self) -> &services::AreaService {
        &self.area_service
    }
}
