// 用户应用服务
// 实现用户相关的业务用例

use crate::application::services::{Service, ServiceContext, ServiceResult, PagedResult, ServiceUtils};
use crate::domain::entities::user::{User, CreateUserData, UpdateUserData, UserQuery};
use crate::domain::repositories::user_repository::UserRepository;
use crate::shared::types::{EntityId, QueryParams, Pagination};
use crate::shared::errors::{AppError, AppResult};
use std::sync::Arc;

/// 用户应用服务
pub struct UserService {
    user_repository: Arc<dyn UserRepository>,
}

impl UserService {
    /// 创建新的用户服务
    pub fn new(user_repository: Arc<dyn UserRepository>) -> Self {
        Self { user_repository }
    }

    /// 创建用户
    pub async fn create_user(
        &self,
        context: &ServiceContext,
        data: CreateUserData,
    ) -> AppResult<ServiceResult<User>> {
        tracing::info!(
            request_id = %context.request_id,
            username = %data.username,
            "Creating new user"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.create(data).await
        }).await;

        match result {
            Ok(user) => {
                ServiceUtils::log_audit_event(
                    context,
                    "create_user",
                    "user",
                    &user.id,
                    Some(&format!("Created user: {}", user.username)),
                );

                Ok(ServiceResult::new(user)
                    .with_execution_time(execution_time)
                    .with_affected_entity(user.id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to create user"
                );
                Err(e)
            }
        }
    }

    /// 根据ID获取用户
    pub async fn get_user_by_id(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<Option<User>>> {
        tracing::debug!(
            request_id = %context.request_id,
            user_id = %id,
            "Getting user by ID"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.find_by_id(id).await
        }).await;

        match result {
            Ok(user) => {
                if user.is_some() {
                    ServiceUtils::log_audit_event(
                        context,
                        "get_user",
                        "user",
                        id,
                        Some("User retrieved successfully"),
                    );
                }

                Ok(ServiceResult::new(user).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %id,
                    "Failed to get user by ID"
                );
                Err(e)
            }
        }
    }

    /// 根据用户名获取用户
    pub async fn get_user_by_username(
        &self,
        context: &ServiceContext,
        username: &str,
    ) -> AppResult<ServiceResult<Option<User>>> {
        tracing::debug!(
            request_id = %context.request_id,
            username = %username,
            "Getting user by username"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.find_by_username(username).await
        }).await;

        match result {
            Ok(user) => {
                if user.is_some() {
                    ServiceUtils::log_audit_event(
                        context,
                        "get_user_by_username",
                        "user",
                        username,
                        Some("User retrieved successfully"),
                    );
                }

                Ok(ServiceResult::new(user).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    username = %username,
                    "Failed to get user by username"
                );
                Err(e)
            }
        }
    }

    /// 更新用户
    pub async fn update_user(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        data: UpdateUserData,
    ) -> AppResult<ServiceResult<User>> {
        // 检查权限：只能更新自己的信息
        ServiceUtils::check_permission(context, id)?;

        tracing::info!(
            request_id = %context.request_id,
            user_id = %id,
            "Updating user"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.update(id, data).await
        }).await;

        match result {
            Ok(user) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_user",
                    "user",
                    &user.id,
                    Some(&format!("Updated user: {}", user.username)),
                );

                Ok(ServiceResult::new(user)
                    .with_execution_time(execution_time)
                    .with_affected_entity(user.id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %id,
                    "Failed to update user"
                );
                Err(e)
            }
        }
    }

    /// 删除用户
    pub async fn delete_user(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限：只能删除自己的账户
        ServiceUtils::check_permission(context, id)?;

        tracing::warn!(
            request_id = %context.request_id,
            user_id = %id,
            "Deleting user"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.delete(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "delete_user",
                    "user",
                    id,
                    Some("User deleted successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %id,
                    "Failed to delete user"
                );
                Err(e)
            }
        }
    }

    /// 查询用户列表
    pub async fn list_users(
        &self,
        context: &ServiceContext,
        query: UserQuery,
        page: u32,
        page_size: u32,
    ) -> AppResult<ServiceResult<PagedResult<User>>> {
        tracing::debug!(
            request_id = %context.request_id,
            page = %page,
            page_size = %page_size,
            "Listing users"
        );

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: None,
            sort: None,
            filters: None,
        };

        let (users_result, execution_time) = ServiceUtils::measure_time(async {
            let users = self.user_repository.find_all(query.clone(), params).await?;
            let total_count = self.user_repository.count(query).await?;
            Ok::<(Vec<User>, u64), AppError>((users, total_count))
        }).await;

        match users_result {
            Ok((users, total_count)) => {
                let paged_result = PagedResult::new(users, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "list_users",
                    "user",
                    "multiple",
                    Some(&format!("Retrieved {} users", paged_result.len())),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to list users"
                );
                Err(e)
            }
        }
    }

    /// 激活用户
    pub async fn activate_user(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限：只能激活自己的账户
        ServiceUtils::check_permission(context, id)?;

        tracing::info!(
            request_id = %context.request_id,
            user_id = %id,
            "Activating user"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.activate(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "activate_user",
                    "user",
                    id,
                    Some("User activated successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %id,
                    "Failed to activate user"
                );
                Err(e)
            }
        }
    }

    /// 停用用户
    pub async fn deactivate_user(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限：只能停用自己的账户
        ServiceUtils::check_permission(context, id)?;

        tracing::info!(
            request_id = %context.request_id,
            user_id = %id,
            "Deactivating user"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.deactivate(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "deactivate_user",
                    "user",
                    id,
                    Some("User deactivated successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %id,
                    "Failed to deactivate user"
                );
                Err(e)
            }
        }
    }

    /// 更新密码
    pub async fn update_password(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        new_password: &str,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限：只能更新自己的密码
        ServiceUtils::check_permission(context, id)?;

        tracing::info!(
            request_id = %context.request_id,
            user_id = %id,
            "Updating user password"
        );

        // 验证密码强度
        let strength = crate::shared::utils::validate_password_strength(new_password);
        if matches!(strength.level, crate::shared::utils::PasswordStrengthLevel::Weak) {
            return Err(AppError::validation(format!(
                "密码强度不足: {}",
                strength.feedback.join(", ")
            )));
        }

        // 生成密码哈希
        let password_hash = self.hash_password(new_password)?;

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.update_password(id, &password_hash).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_password",
                    "user",
                    id,
                    Some("Password updated successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %id,
                    "Failed to update password"
                );
                Err(e)
            }
        }
    }

    /// 记录用户登录
    pub async fn record_login(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        tracing::info!(
            request_id = %context.request_id,
            user_id = %id,
            "Recording user login"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.user_repository.update_last_login(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "record_login",
                    "user",
                    id,
                    Some("Login recorded successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %id,
                    "Failed to record login"
                );
                Err(e)
            }
        }
    }

    /// 验证用户密码
    pub async fn verify_password(
        &self,
        context: &ServiceContext,
        username: &str,
        password: &str,
    ) -> AppResult<ServiceResult<Option<User>>> {
        tracing::debug!(
            request_id = %context.request_id,
            username = %username,
            "Verifying user password"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            if let Some(user) = self.user_repository.find_by_username(username).await? {
                if user.verify_password(password)? {
                    Ok(Some(user))
                } else {
                    Ok(None)
                }
            } else {
                Ok(None)
            }
        }).await;

        match result {
            Ok(user_opt) => {
                if user_opt.is_some() {
                    ServiceUtils::log_audit_event(
                        context,
                        "verify_password",
                        "user",
                        username,
                        Some("Password verification successful"),
                    );
                } else {
                    ServiceUtils::log_audit_event(
                        context,
                        "verify_password_failed",
                        "user",
                        username,
                        Some("Password verification failed"),
                    );
                }

                Ok(ServiceResult::new(user_opt).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    username = %username,
                    "Failed to verify password"
                );
                Err(e)
            }
        }
    }

    /// 生成密码哈希
    fn hash_password(&self, password: &str) -> AppResult<String> {
        // 这里应该使用实际的密码哈希库，如bcrypt
        // 暂时使用简单的哈希
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(password.as_bytes());
        hasher.update(b"salt"); // 实际应用中应该使用随机盐
        Ok(format!("{:x}", hasher.finalize()))
    }
}

impl Service for UserService {
    fn name(&self) -> &'static str {
        "UserService"
    }

    async fn health_check(&self) -> AppResult<()> {
        // 执行简单的查询来检查仓储是否正常工作
        let query = UserQuery {
            username: None,
            email: None,
            is_active: None,
            created_after: None,
            created_before: None,
        };
        
        let params = QueryParams {
            pagination: Some(Pagination::new(1, 1)),
            search: None,
            sort: None,
            filters: None,
        };

        self.user_repository.find_all(query, params).await?;
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::domain::repositories::user_repository::UserRepository;
    use async_trait::async_trait;
    use std::collections::HashMap;
    use std::sync::Mutex;

    // 模拟用户仓储用于测试
    struct MockUserRepository {
        users: Mutex<HashMap<String, User>>,
    }

    impl MockUserRepository {
        fn new() -> Self {
            Self {
                users: Mutex::new(HashMap::new()),
            }
        }
    }

    #[async_trait]
    impl UserRepository for MockUserRepository {
        async fn create(&self, data: CreateUserData) -> AppResult<User> {
            let user = User::new(data)?;
            self.users.lock().unwrap().insert(user.id.clone(), user.clone());
            Ok(user)
        }

        async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<User>> {
            Ok(self.users.lock().unwrap().get(id).cloned())
        }

        async fn find_by_username(&self, username: &str) -> AppResult<Option<User>> {
            Ok(self.users.lock().unwrap()
                .values()
                .find(|u| u.username == username)
                .cloned())
        }

        // 其他方法的简单实现...
        async fn find_by_email(&self, _email: &str) -> AppResult<Option<User>> { Ok(None) }
        async fn update(&self, _id: &EntityId, _data: UpdateUserData) -> AppResult<User> { 
            Err(AppError::not_implemented("Not implemented for test"))
        }
        async fn delete(&self, _id: &EntityId) -> AppResult<()> { Ok(()) }
        async fn find_all(&self, _query: UserQuery, _params: QueryParams) -> AppResult<Vec<User>> { Ok(Vec::new()) }
        async fn count(&self, _query: UserQuery) -> AppResult<u64> { Ok(0) }
        async fn username_exists(&self, _username: &str) -> AppResult<bool> { Ok(false) }
        async fn email_exists(&self, _email: &str) -> AppResult<bool> { Ok(false) }
        async fn activate(&self, _id: &EntityId) -> AppResult<()> { Ok(()) }
        async fn deactivate(&self, _id: &EntityId) -> AppResult<()> { Ok(()) }
        async fn update_last_login(&self, _id: &EntityId) -> AppResult<()> { Ok(()) }
        async fn update_password(&self, _id: &EntityId, _password_hash: &str) -> AppResult<()> { Ok(()) }
        async fn find_active_users(&self, _params: QueryParams) -> AppResult<Vec<User>> { Ok(Vec::new()) }
        async fn find_recent_users(&self, _days: i32, _params: QueryParams) -> AppResult<Vec<User>> { Ok(Vec::new()) }
    }

    #[tokio::test]
    async fn test_create_user() {
        let repository = Arc::new(MockUserRepository::new());
        let service = UserService::new(repository);
        let context = ServiceContext::new(None);

        let data = CreateUserData {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "SecurePass123!".to_string(),
            full_name: Some("Test User".to_string()),
            timezone: None,
            language: None,
        };

        let result = service.create_user(&context, data).await;
        assert!(result.is_ok());

        let service_result = result.unwrap();
        assert_eq!(service_result.data.username, "testuser");
        assert!(service_result.metadata.execution_time_ms > 0);
    }

    #[tokio::test]
    async fn test_service_health_check() {
        let repository = Arc::new(MockUserRepository::new());
        let service = UserService::new(repository);

        let result = service.health_check().await;
        assert!(result.is_ok());
    }
}
