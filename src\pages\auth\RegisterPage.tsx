import { Component, createSignal, Show } from 'solid-js';
import { A, useNavigate } from '@solidjs/router';
import { useAuth } from '../../contexts/AuthContext';

export const RegisterPage: Component = () => {
  const [formData, setFormData] = createSignal({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    fullName: '',
  });
  const [isSubmitting, setIsSubmitting] = createSignal(false);
  const [errors, setErrors] = createSignal<Record<string, string>>({});

  const { register, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // 如果已经登录，重定向到仪表板
  if (isAuthenticated()) {
    navigate('/dashboard');
  }

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    const data = formData();

    if (!data.username.trim()) {
      newErrors.username = '请输入用户名';
    } else if (data.username.length < 3) {
      newErrors.username = '用户名至少需要3个字符';
    }

    if (!data.email.trim()) {
      newErrors.email = '请输入邮箱地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    if (!data.password.trim()) {
      newErrors.password = '请输入密码';
    } else if (data.password.length < 6) {
      newErrors.password = '密码至少需要6个字符';
    }

    if (!data.confirmPassword.trim()) {
      newErrors.confirmPassword = '请确认密码';
    } else if (data.password !== data.confirmPassword) {
      newErrors.confirmPassword = '两次输入的密码不一致';
    }

    if (!data.fullName.trim()) {
      newErrors.fullName = '请输入姓名';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const data = formData();
      const success = await register({
        username: data.username,
        email: data.email,
        password: data.password,
        full_name: data.fullName,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        language: 'zh',
      });

      if (success) {
        navigate('/login');
      }
    } catch (error) {
      console.error('Registration error:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        {/* 头部 */}
        <div class="text-center">
          <h2 class="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
            注册 PaoLife 账户
          </h2>
          <p class="mt-2 text-sm text-gray-600 dark:text-gray-400">
            开始管理你的项目、任务和生活领域
          </p>
        </div>

        {/* 注册表单 */}
        <form class="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div class="space-y-4">
            {/* 用户名输入 */}
            <div>
              <label for="username" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                用户名 *
              </label>
              <input
                id="username"
                name="username"
                type="text"
                autocomplete="username"
                required
                value={formData().username}
                onInput={(e) => updateFormData('username', e.currentTarget.value)}
                class={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors().username 
                    ? 'border-red-300 dark:border-red-600' 
                    : 'border-gray-300 dark:border-gray-600'
                } placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                placeholder="请输入用户名"
              />
              <Show when={errors().username}>
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors().username}</p>
              </Show>
            </div>

            {/* 邮箱输入 */}
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                邮箱地址 *
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autocomplete="email"
                required
                value={formData().email}
                onInput={(e) => updateFormData('email', e.currentTarget.value)}
                class={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors().email 
                    ? 'border-red-300 dark:border-red-600' 
                    : 'border-gray-300 dark:border-gray-600'
                } placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                placeholder="请输入邮箱地址"
              />
              <Show when={errors().email}>
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors().email}</p>
              </Show>
            </div>

            {/* 姓名输入 */}
            <div>
              <label for="fullName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                姓名 *
              </label>
              <input
                id="fullName"
                name="fullName"
                type="text"
                autocomplete="name"
                required
                value={formData().fullName}
                onInput={(e) => updateFormData('fullName', e.currentTarget.value)}
                class={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors().fullName 
                    ? 'border-red-300 dark:border-red-600' 
                    : 'border-gray-300 dark:border-gray-600'
                } placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                placeholder="请输入姓名"
              />
              <Show when={errors().fullName}>
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors().fullName}</p>
              </Show>
            </div>

            {/* 密码输入 */}
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                密码 *
              </label>
              <input
                id="password"
                name="password"
                type="password"
                autocomplete="new-password"
                required
                value={formData().password}
                onInput={(e) => updateFormData('password', e.currentTarget.value)}
                class={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors().password 
                    ? 'border-red-300 dark:border-red-600' 
                    : 'border-gray-300 dark:border-gray-600'
                } placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                placeholder="请输入密码"
              />
              <Show when={errors().password}>
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors().password}</p>
              </Show>
            </div>

            {/* 确认密码输入 */}
            <div>
              <label for="confirmPassword" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                确认密码 *
              </label>
              <input
                id="confirmPassword"
                name="confirmPassword"
                type="password"
                autocomplete="new-password"
                required
                value={formData().confirmPassword}
                onInput={(e) => updateFormData('confirmPassword', e.currentTarget.value)}
                class={`mt-1 appearance-none relative block w-full px-3 py-2 border ${
                  errors().confirmPassword 
                    ? 'border-red-300 dark:border-red-600' 
                    : 'border-gray-300 dark:border-gray-600'
                } placeholder-gray-500 dark:placeholder-gray-400 text-gray-900 dark:text-white bg-white dark:bg-gray-800 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm`}
                placeholder="请再次输入密码"
              />
              <Show when={errors().confirmPassword}>
                <p class="mt-1 text-sm text-red-600 dark:text-red-400">{errors().confirmPassword}</p>
              </Show>
            </div>
          </div>

          {/* 注册按钮 */}
          <div>
            <button
              type="submit"
              disabled={isSubmitting()}
              class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              <Show
                when={!isSubmitting()}
                fallback={
                  <div class="flex items-center">
                    <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    注册中...
                  </div>
                }
              >
                注册账户
              </Show>
            </button>
          </div>

          {/* 登录链接 */}
          <div class="text-center">
            <p class="text-sm text-gray-600 dark:text-gray-400">
              已有账户？{' '}
              <A
                href="/login"
                class="font-medium text-blue-600 hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300"
              >
                立即登录
              </A>
            </p>
          </div>
        </form>
      </div>
    </div>
  );
};
