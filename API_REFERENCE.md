# PaoLife API 参考文档

## 📋 API 概览

PaoLife 使用 Tauri 的 IPC (Inter-Process Communication) 机制实现前后端通信。所有 API 都是异步的，返回统一的响应格式。

## 🔧 通用响应格式

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp: string;
  request_id?: string;
}
```

## 🔐 认证 API

### 用户登录
```typescript
// 命令: login
interface LoginRequest {
  username: string;
  password: string;
}

interface LoginResponse {
  user: User;
  token: string;
  expires_at: string;
}

// 使用示例
const response = await invoke<ApiResponse<LoginResponse>>('login', {
  request: {
    username: 'demo',
    password: 'demo123'
  }
});
```

### 用户注册
```typescript
// 命令: register
interface RegisterRequest {
  username: string;
  email?: string;
  password: string;
  full_name?: string;
  timezone?: string;
  language?: string;
}

// 使用示例
const response = await invoke<ApiResponse<User>>('register', {
  request: {
    username: 'newuser',
    email: '<EMAIL>',
    password: 'securepass123',
    full_name: 'New User'
  }
});
```

### 用户登出
```typescript
// 命令: logout
const response = await invoke<ApiResponse<void>>('logout', {
  currentUserId: 'user-id'
});
```

### 获取当前用户
```typescript
// 命令: get_current_user
const response = await invoke<ApiResponse<User | null>>('get_current_user', {
  currentUserId: 'user-id'
});
```

## 👤 用户管理 API

### 创建用户
```typescript
// 命令: create_user
interface CreateUserRequest {
  username: string;
  email?: string;
  password: string;
  full_name?: string;
  timezone?: string;
  language?: string;
}

const response = await invoke<ApiResponse<User>>('create_user', {
  request: createUserData
});
```

### 获取用户信息
```typescript
// 命令: get_user
const response = await invoke<ApiResponse<User | null>>('get_user', {
  userId: 'user-id',
  currentUserId: 'current-user-id'
});
```

### 更新用户信息
```typescript
// 命令: update_user
interface UpdateUserRequest {
  email?: string;
  full_name?: string;
  timezone?: string;
  language?: string;
}

const response = await invoke<ApiResponse<User>>('update_user', {
  userId: 'user-id',
  request: updateData,
  currentUserId: 'current-user-id'
});
```

### 更新密码
```typescript
// 命令: update_password
const response = await invoke<ApiResponse<void>>('update_password', {
  userId: 'user-id',
  newPassword: 'new-password',
  currentUserId: 'current-user-id'
});
```

## 📁 项目管理 API

### 创建项目
```typescript
// 命令: create_project
interface CreateProjectRequest {
  name: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  start_date?: string; // YYYY-MM-DD
  deadline?: string;   // YYYY-MM-DD
  estimated_hours?: number;
  area_id?: string;
}

const response = await invoke<ApiResponse<Project>>('create_project', {
  request: projectData,
  currentUserId: 'user-id'
});
```

### 获取项目
```typescript
// 命令: get_project
const response = await invoke<ApiResponse<Project | null>>('get_project', {
  projectId: 'project-id',
  currentUserId: 'user-id'
});
```

### 更新项目
```typescript
// 命令: update_project
interface UpdateProjectRequest {
  name?: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  start_date?: string;
  deadline?: string;
  estimated_hours?: number;
  area_id?: string;
}

const response = await invoke<ApiResponse<Project>>('update_project', {
  projectId: 'project-id',
  request: updateData,
  currentUserId: 'user-id'
});
```

### 列出项目
```typescript
// 命令: list_projects
interface PaginationRequest {
  page?: number;
  page_size?: number;
}

const response = await invoke<ApiResponse<PagedResult<Project>>>('list_projects', {
  pagination: { page: 1, page_size: 20 },
  statusFilter: 'in_progress', // 可选
  currentUserId: 'user-id'
});
```

### 更新项目状态
```typescript
// 命令: update_project_status
const response = await invoke<ApiResponse<void>>('update_project_status', {
  projectId: 'project-id',
  status: 'completed', // not_started | in_progress | at_risk | paused | completed | archived
  currentUserId: 'user-id'
});
```

### 获取项目统计
```typescript
// 命令: get_project_stats
interface ProjectStats {
  total_projects: number;
  active_projects: number;
  completed_projects: number;
  overdue_projects: number;
  completion_rate: number;
  average_progress: number;
}

const response = await invoke<ApiResponse<ProjectStats>>('get_project_stats', {
  currentUserId: 'user-id'
});
```

## ✅ 任务管理 API

### 创建任务
```typescript
// 命令: create_task
interface CreateTaskRequest {
  title: string;
  description?: string;
  priority?: 'low' | 'medium' | 'high' | 'urgent';
  parent_task_id?: string;
  project_id?: string;
  area_id?: string;
  assigned_to?: string;
  due_date?: string; // YYYY-MM-DD
  estimated_minutes?: number;
}

const response = await invoke<ApiResponse<Task>>('create_task', {
  request: taskData,
  currentUserId: 'user-id'
});
```

### 获取任务
```typescript
// 命令: get_task
const response = await invoke<ApiResponse<Task | null>>('get_task', {
  taskId: 'task-id',
  currentUserId: 'user-id'
});
```

### 更新任务状态
```typescript
// 命令: update_task_status
const response = await invoke<ApiResponse<void>>('update_task_status', {
  taskId: 'task-id',
  status: 'completed', // todo | in_progress | waiting | completed | cancelled
  currentUserId: 'user-id'
});
```

### 更新任务完成度
```typescript
// 命令: update_task_completion
const response = await invoke<ApiResponse<void>>('update_task_completion', {
  taskId: 'task-id',
  percentage: 75, // 0-100
  currentUserId: 'user-id'
});
```

### 获取任务层级结构
```typescript
// 命令: get_task_hierarchy
interface TaskHierarchy {
  task: Task;
  children: TaskHierarchy[];
  depth: number;
}

const response = await invoke<ApiResponse<TaskHierarchy[]>>('get_task_hierarchy', {
  rootTaskId: 'root-task-id', // 可选
  currentUserId: 'user-id'
});
```

## 🎯 领域管理 API

### 创建领域
```typescript
// 命令: create_area
interface CreateAreaRequest {
  name: string;
  description?: string;
  standard?: string;
  icon_name?: string;
  color_hex?: string;
  sort_order?: number;
}

const response = await invoke<ApiResponse<Area>>('create_area', {
  request: areaData,
  currentUserId: 'user-id'
});
```

### 获取领域摘要
```typescript
// 命令: get_area_summary
interface AreaSummary {
  area: Area;
  total_projects: number;
  completed_projects: number;
  active_projects: number;
  total_tasks: number;
  completed_tasks: number;
  active_tasks: number;
  total_habits: number;
  active_habits: number;
}

const response = await invoke<ApiResponse<AreaSummary>>('get_area_summary', {
  areaId: 'area-id',
  currentUserId: 'user-id'
});
```

## 🔍 搜索 API

### 搜索项目
```typescript
// 命令: search_projects
const response = await invoke<ApiResponse<PagedResult<Project>>>('search_projects', {
  keyword: '搜索关键词',
  pagination: { page: 1, page_size: 20 },
  currentUserId: 'user-id'
});
```

### 搜索任务
```typescript
// 命令: search_tasks
const response = await invoke<ApiResponse<PagedResult<Task>>>('search_tasks', {
  keyword: '搜索关键词',
  pagination: { page: 1, page_size: 20 },
  currentUserId: 'user-id'
});
```

## 📊 数据类型定义

### 用户类型
```typescript
interface User {
  id: string;
  username: string;
  email?: string;
  full_name?: string;
  timezone: string;
  language: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_login_at?: string;
}
```

### 项目类型
```typescript
interface Project {
  id: string;
  name: string;
  description?: string;
  status: 'not_started' | 'in_progress' | 'at_risk' | 'paused' | 'completed' | 'archived';
  progress: number; // 0-100
  priority: 'low' | 'medium' | 'high' | 'urgent';
  start_date?: string;
  deadline?: string;
  estimated_hours?: number;
  actual_hours: number;
  area_id?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}
```

### 任务类型
```typescript
interface Task {
  id: string;
  title: string;
  description?: string;
  status: 'todo' | 'in_progress' | 'waiting' | 'completed' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  parent_task_id?: string;
  project_id?: string;
  area_id?: string;
  assigned_to?: string;
  due_date?: string;
  estimated_minutes?: number;
  actual_minutes: number;
  completion_percentage: number; // 0-100
  sort_order: number;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}
```

### 领域类型
```typescript
interface Area {
  id: string;
  name: string;
  description?: string;
  standard?: string;
  icon_name?: string;
  color_hex?: string;
  sort_order: number;
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
  archived_at?: string;
}
```

### 分页结果类型
```typescript
interface PagedResult<T> {
  items: T[];
  total_count: number;
  page: number;
  page_size: number;
  total_pages: number;
  has_next: boolean;
  has_previous: boolean;
}
```

## ⚠️ 错误处理

### 错误类型
- `validation`: 数据验证错误
- `not_found`: 资源未找到
- `unauthorized`: 未认证
- `forbidden`: 权限不足
- `database`: 数据库错误
- `business_logic`: 业务逻辑错误
- `internal`: 内部错误

### 错误响应示例
```typescript
{
  success: false,
  error: "验证错误: 用户名不能为空",
  timestamp: "2024-12-29T10:30:00Z",
  request_id: "req_123456"
}
```

## 🔧 使用建议

1. **错误处理**: 始终检查 `response.success` 字段
2. **认证状态**: 大部分API需要 `currentUserId` 参数
3. **分页查询**: 使用合理的页面大小避免性能问题
4. **数据验证**: 前端也应进行基本的数据验证
5. **异步处理**: 所有API调用都是异步的，使用 `await` 或 `.then()`

---

这个API文档涵盖了 PaoLife 的所有核心功能接口，为前端开发和第三方集成提供了完整的参考。
