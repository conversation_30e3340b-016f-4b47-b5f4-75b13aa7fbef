# PaoLife 开发环境搭建指南

## 系统要求

### 操作系统支持
- **Windows**: Windows 10 1903+ (推荐 Windows 11)
- **macOS**: macOS 10.15+ (推荐 macOS 12+)
- **Linux**: Ubuntu 18.04+, Debian 10+, Fedora 32+

### 硬件要求
- **CPU**: 4核心以上 (推荐8核心)
- **内存**: 8GB以上 (推荐16GB)
- **存储**: 20GB可用空间 (SSD推荐)
- **网络**: 稳定的互联网连接

## 第一部分：基础环境安装

### 1. Rust 环境安装

#### Windows 安装
```powershell
# 下载并安装 Rustup
# 访问 https://rustup.rs/ 下载 rustup-init.exe
# 或使用 winget 安装
winget install Rustlang.Rustup

# 验证安装
rustc --version
cargo --version
```

#### macOS 安装
```bash
# 使用 Homebrew 安装
brew install rustup-init
rustup-init

# 或使用官方脚本
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 重新加载环境
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

#### Linux 安装
```bash
# 使用官方脚本安装
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 重新加载环境
source ~/.cargo/env

# 验证安装
rustc --version
cargo --version
```

#### Rust 工具链配置
```bash
# 安装稳定版工具链 (项目要求 1.81+)
rustup install stable
rustup default stable

# 安装必要组件
rustup component add clippy rustfmt

# 安装开发工具
cargo install cargo-nextest      # 更快的测试执行
cargo install cargo-watch        # 文件监控和自动重新编译
cargo install cargo-audit        # 安全审计
cargo install cargo-outdated     # 依赖更新检查
cargo install tauri-cli          # Tauri CLI工具

# 验证工具安装
cargo nextest --version
cargo clippy --version
cargo fmt --version
cargo tauri --version
```

### 2. Node.js 环境安装

#### 使用 Node Version Manager (推荐)

**Windows (使用 nvm-windows)**
```powershell
# 下载并安装 nvm-windows
# 访问 https://github.com/coreybutler/nvm-windows/releases
# 下载 nvm-setup.zip 并安装

# 安装 Node.js 22 LTS
nvm install 22
nvm use 22

# 验证安装
node --version
npm --version
```

**macOS/Linux (使用 nvm)**
```bash
# 安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash

# 重新加载终端或执行
source ~/.bashrc

# 安装 Node.js 22 LTS
nvm install 22
nvm use 22
nvm alias default 22

# 验证安装
node --version
npm --version
```

#### 配置包管理器
```bash
# 启用 Corepack (Node.js 16.9+)
corepack enable

# 安装 pnpm (项目推荐)
corepack prepare pnpm@latest --activate

# 验证 pnpm 安装
pnpm --version

# 配置 pnpm 镜像 (可选，提升下载速度)
pnpm config set registry https://registry.npmmirror.com/
```

### 3. 系统依赖安装

#### Windows 依赖
```powershell
# 安装 Visual Studio Build Tools
# 访问 https://visualstudio.microsoft.com/visual-cpp-build-tools/
# 下载并安装，确保包含 C++ 构建工具

# 使用 winget 安装其他工具
winget install Microsoft.VisualStudioCode
winget install Git.Git
winget install Microsoft.PowerShell
```

#### macOS 依赖
```bash
# 安装 Xcode Command Line Tools
xcode-select --install

# 使用 Homebrew 安装开发工具
brew install git
brew install --cask visual-studio-code
```

#### Linux 依赖 (Ubuntu/Debian)
```bash
# 更新包管理器
sudo apt update

# 安装构建依赖
sudo apt install -y \
    build-essential \
    curl \
    wget \
    file \
    libssl-dev \
    libgtk-3-dev \
    libayatana-appindicator3-dev \
    librsvg2-dev \
    libwebkit2gtk-4.0-dev

# 安装开发工具
sudo apt install -y git code
```

## 第二部分：项目环境配置

### 1. 克隆项目
```bash
# 克隆项目仓库
git clone https://github.com/your-org/PaoLife.git
cd PaoLife

# 检查分支
git branch -a
git checkout develop  # 或主开发分支
```

### 2. Rust 项目配置

#### 安装 Rust 依赖
```bash
# 进入 Rust 项目目录
cd src-tauri

# 安装依赖
cargo fetch

# 检查依赖
cargo tree

# 运行基础检查
cargo check
cargo clippy
cargo fmt --check
```

#### 配置开发环境
```bash
# 创建 .cargo/config.toml (如果不存在)
mkdir -p .cargo
cat > .cargo/config.toml << 'EOF'
[build]
target-dir = "target"

[env]
RUST_LOG = { value = "debug", relative = true }
DATABASE_URL = { value = "sqlite:dev.db", relative = true }

[alias]
dev = "run --bin paolife-dev"
test-all = "nextest run --workspace"
lint = "clippy --all-targets --all-features -- -D warnings"
fmt-check = "fmt --all -- --check"

[profile.dev]
opt-level = 0
debug = true
split-debuginfo = "unpacked"
incremental = true
codegen-units = 256

[profile.release]
opt-level = 3
lto = true
codegen-units = 1
panic = "abort"
strip = true
EOF
```

### 3. 前端项目配置

#### 安装前端依赖
```bash
# 返回项目根目录
cd ..

# 安装依赖
pnpm install

# 验证安装
pnpm list
```

#### 配置开发工具
```bash
# 创建 .vscode/settings.json (VS Code 配置)
mkdir -p .vscode
cat > .vscode/settings.json << 'EOF'
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll": true,
    "source.organizeImports": true
  },
  "files.associations": {
    "*.css": "tailwindcss"
  },
  "tailwindCSS.experimental.classRegex": [
    ["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"],
    ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]
  ],
  "rust-analyzer.cargo.features": "all",
  "rust-analyzer.checkOnSave.command": "clippy"
}
EOF

# 创建 .vscode/extensions.json (推荐扩展)
cat > .vscode/extensions.json << 'EOF'
{
  "recommendations": [
    "rust-lang.rust-analyzer",
    "tauri-apps.tauri-vscode",
    "bradlc.vscode-tailwindcss",
    "biomejs.biome",
    "ms-vscode.vscode-typescript-next"
  ]
}
EOF
```

### 4. 数据库初始化

#### 创建开发数据库
```bash
# 进入 Rust 项目目录
cd src-tauri

# 运行数据库迁移 (如果有迁移脚本)
cargo run --bin migrate

# 或手动创建数据库文件
touch dev.db

# 验证数据库连接
cargo run --bin db-check
```

## 第三部分：开发工具配置

### 1. VS Code 配置

#### 安装推荐扩展
```bash
# 使用命令行安装扩展
code --install-extension rust-lang.rust-analyzer
code --install-extension tauri-apps.tauri-vscode
code --install-extension bradlc.vscode-tailwindcss
code --install-extension biomejs.biome
code --install-extension ms-vscode.vscode-typescript-next
```

#### 配置调试环境
```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "type": "lldb",
      "request": "launch",
      "name": "Tauri Development Debug",
      "cargo": {
        "args": [
          "build",
          "--manifest-path=./src-tauri/Cargo.toml",
          "--no-default-features"
        ],
        "filter": {
          "name": "paolife",
          "kind": "bin"
        }
      },
      "args": [],
      "cwd": "${workspaceFolder}"
    }
  ]
}
```

### 2. Git 配置

#### 配置 Git Hooks
```bash
# 创建 pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/sh
# 运行代码格式检查
echo "Running Rust format check..."
cd src-tauri && cargo fmt --check
if [ $? -ne 0 ]; then
    echo "Rust code formatting issues found. Please run 'cargo fmt'"
    exit 1
fi

echo "Running frontend format check..."
cd .. && pnpm lint
if [ $? -ne 0 ]; then
    echo "Frontend code formatting issues found. Please run 'pnpm lint:fix'"
    exit 1
fi

echo "All checks passed!"
EOF

# 使 hook 可执行
chmod +x .git/hooks/pre-commit
```

### 3. 环境变量配置

#### 创建环境配置文件
```bash
# 创建 .env.development
cat > .env.development << 'EOF'
# 开发环境配置
RUST_LOG=debug
DATABASE_URL=sqlite:dev.db
TAURI_DEV_HOST=localhost
TAURI_DEV_PORT=1420

# 前端配置
VITE_API_BASE_URL=http://localhost:1420
VITE_APP_NAME=PaoLife
VITE_APP_VERSION=0.1.0
EOF

# 创建 .env.example (模板文件)
cp .env.development .env.example
```

## 第四部分：验证安装

### 1. 运行开发服务器
```bash
# 启动开发服务器
pnpm tauri dev

# 或分别启动
# 终端1: 启动前端开发服务器
pnpm dev

# 终端2: 启动 Tauri 开发模式
cd src-tauri && cargo tauri dev
```

### 2. 运行测试
```bash
# 运行 Rust 测试
cd src-tauri && cargo nextest run

# 运行前端测试
cd .. && pnpm test

# 运行所有测试
pnpm test:all
```

### 3. 构建检查
```bash
# 构建 Rust 项目
cd src-tauri && cargo build

# 构建前端项目
cd .. && pnpm build

# 构建完整应用
pnpm tauri build
```

## 故障排除

### 常见问题

#### Rust 编译错误
```bash
# 清理构建缓存
cargo clean

# 更新依赖
cargo update

# 检查工具链版本
rustup show
```

#### 前端依赖问题
```bash
# 清理 node_modules
rm -rf node_modules pnpm-lock.yaml

# 重新安装
pnpm install
```

#### Tauri 构建问题
```bash
# 检查系统依赖
cargo tauri info

# 更新 Tauri CLI
cargo install tauri-cli --force
```

### 获取帮助
- **官方文档**: https://tauri.app/
- **Rust 文档**: https://doc.rust-lang.org/
- **SolidJS 文档**: https://www.solidjs.com/
- **项目 Issues**: GitHub Issues 页面

## 下一步

环境搭建完成后，建议阅读：
1. **开发规范文档** - 了解代码规范和工作流程
2. **API接口规范** - 了解前后端接口设计
3. **UI设计规范** - 了解前端组件和设计系统

开始愉快的开发吧！🚀
