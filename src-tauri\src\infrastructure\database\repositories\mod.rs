// 数据库仓储实现
// 实现具体的数据库操作逻辑

use sqlx::SqlitePool;

pub mod user_repository_impl;
pub mod project_repository_impl;
pub mod task_repository_impl;
pub mod area_repository_impl;

// 重新导出
pub use user_repository_impl::*;
pub use project_repository_impl::*;
pub use task_repository_impl::*;
pub use area_repository_impl::*;

/// 仓储工厂
pub struct RepositoryFactory {
    pool: SqlitePool,
}

impl RepositoryFactory {
    /// 创建新的仓储工厂
    pub fn new(pool: SqlitePool) -> Self {
        Self { pool }
    }

    /// 创建用户仓储
    pub fn user_repository(&self) -> UserRepositoryImpl {
        UserRepositoryImpl::new(self.pool.clone())
    }

    /// 创建项目仓储
    pub fn project_repository(&self) -> ProjectRepositoryImpl {
        ProjectRepositoryImpl::new(self.pool.clone())
    }

    /// 创建任务仓储
    pub fn task_repository(&self) -> TaskRepositoryImpl {
        TaskRepositoryImpl::new(self.pool.clone())
    }

    /// 创建领域仓储
    pub fn area_repository(&self) -> AreaRepositoryImpl {
        AreaRepositoryImpl::new(self.pool.clone())
    }
}
