// 数据库迁移运行器
// 提供命令行工具和程序化接口来管理数据库迁移

use crate::infrastructure::database::{DatabaseConnection, MigrationManager};
use crate::shared::errors::{AppError, AppResult};
use std::env;
use std::path::PathBuf;

/// 迁移运行器
pub struct MigrationRunner {
    database_url: String,
}

impl MigrationRunner {
    /// 创建新的迁移运行器
    pub fn new() -> AppResult<Self> {
        let database_url = Self::get_database_url()?;
        Ok(Self { database_url })
    }

    /// 从环境变量或默认路径获取数据库URL
    fn get_database_url() -> AppResult<String> {
        // 首先尝试从环境变量获取
        if let Ok(url) = env::var("DATABASE_URL") {
            return Ok(url);
        }

        // 获取应用数据目录
        let app_data_dir = Self::get_app_data_dir()?;
        let db_path = app_data_dir.join("paolife.db");
        
        Ok(format!("sqlite://{}", db_path.to_string_lossy()))
    }

    /// 获取应用数据目录
    fn get_app_data_dir() -> AppResult<PathBuf> {
        let app_data_dir = if cfg!(target_os = "windows") {
            // Windows: %APPDATA%/PaoLife
            dirs::data_dir()
                .ok_or_else(|| AppError::internal("Failed to get Windows AppData directory"))?
                .join("PaoLife")
        } else if cfg!(target_os = "macos") {
            // macOS: ~/Library/Application Support/PaoLife
            dirs::data_dir()
                .ok_or_else(|| AppError::internal("Failed to get macOS Application Support directory"))?
                .join("PaoLife")
        } else {
            // Linux: ~/.local/share/PaoLife
            dirs::data_dir()
                .ok_or_else(|| AppError::internal("Failed to get Linux data directory"))?
                .join("PaoLife")
        };

        // 确保目录存在
        std::fs::create_dir_all(&app_data_dir)
            .map_err(|e| AppError::internal(format!("Failed to create app data directory: {}", e)))?;

        Ok(app_data_dir)
    }

    /// 运行所有待应用的迁移
    pub async fn migrate(&self) -> AppResult<()> {
        println!("🚀 Starting database migration...");
        println!("📍 Database URL: {}", self.database_url);

        let db = DatabaseConnection::new(&self.database_url).await?;
        let migration_manager = MigrationManager::new(db.get_pool());

        // 检查当前状态
        let status = migration_manager.check_database_status().await?;
        println!("📊 Migration Status:");
        println!("   Total migrations: {}", status.total_migrations);
        println!("   Applied migrations: {}", status.applied_migrations);
        println!("   Pending migrations: {}", status.pending_migrations);
        
        if let Some(latest) = &status.latest_applied {
            println!("   Latest applied: {}", latest);
        }

        if status.is_up_to_date {
            println!("✅ Database is already up to date!");
            return Ok(());
        }

        // 应用迁移
        println!("🔄 Applying {} pending migrations...", status.pending_migrations);
        migration_manager.migrate().await?;

        // 验证结果
        let final_status = migration_manager.check_database_status().await?;
        if final_status.is_up_to_date {
            println!("✅ All migrations applied successfully!");
            println!("📈 Database schema is now at version: {}", 
                final_status.latest_applied.unwrap_or_else(|| "unknown".to_string()));
        } else {
            return Err(AppError::internal("Migration completed but database is not up to date"));
        }

        Ok(())
    }

    /// 检查数据库状态
    pub async fn status(&self) -> AppResult<()> {
        println!("🔍 Checking database status...");
        println!("📍 Database URL: {}", self.database_url);

        let db = DatabaseConnection::new(&self.database_url).await?;
        let migration_manager = MigrationManager::new(db.get_pool());

        // 获取迁移状态
        let status = migration_manager.check_database_status().await?;
        
        println!("\n📊 Migration Status:");
        println!("   Total migrations: {}", status.total_migrations);
        println!("   Applied migrations: {}", status.applied_migrations);
        println!("   Pending migrations: {}", status.pending_migrations);
        
        if let Some(latest) = &status.latest_applied {
            println!("   Latest applied: {}", latest);
        }

        if status.is_up_to_date {
            println!("   Status: ✅ Up to date");
        } else {
            println!("   Status: ⚠️  Pending migrations");
        }

        // 获取数据库统计
        let stats = migration_manager.get_database_stats().await?;
        println!("\n📈 Database Statistics:");
        println!("   Database size: {} bytes", stats.database_size);
        println!("   Table counts:");
        
        for (table, count) in &stats.table_counts {
            println!("     {}: {} records", table, count);
        }

        // 验证数据库
        let validation = migration_manager.validate_database().await?;
        println!("\n🔍 Database Validation:");
        
        if validation.is_valid {
            println!("   Status: ✅ Valid");
        } else {
            println!("   Status: ❌ Invalid");
            for error in &validation.errors {
                println!("   Error: {}", error);
            }
        }

        if !validation.warnings.is_empty() {
            println!("   Warnings:");
            for warning in &validation.warnings {
                println!("     ⚠️  {}", warning);
            }
        }

        Ok(())
    }

    /// 重置数据库 (删除所有数据)
    pub async fn reset(&self) -> AppResult<()> {
        println!("⚠️  WARNING: This will delete all data in the database!");
        println!("📍 Database URL: {}", self.database_url);
        
        // 在实际应用中，这里应该有确认提示
        println!("🗑️  Resetting database...");

        let db = DatabaseConnection::new(&self.database_url).await?;
        let pool = db.get_pool();

        // 获取所有表名
        let tables: Vec<String> = sqlx::query_scalar(
            "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'"
        )
        .fetch_all(&pool)
        .await
        .map_err(|e| AppError::database(format!("Failed to get table names: {}", e)))?;

        // 删除所有表
        for table in tables {
            let sql = format!("DROP TABLE IF EXISTS {}", table);
            sqlx::query(&sql)
                .execute(&pool)
                .await
                .map_err(|e| AppError::database(format!("Failed to drop table {}: {}", table, e)))?;
            println!("   Dropped table: {}", table);
        }

        println!("✅ Database reset completed!");
        println!("💡 Run 'migrate' to recreate the schema");

        Ok(())
    }

    /// 创建数据库备份
    pub async fn backup(&self, backup_path: Option<String>) -> AppResult<()> {
        let backup_path = backup_path.unwrap_or_else(|| {
            let timestamp = chrono::Utc::now().format("%Y%m%d_%H%M%S");
            format!("paolife_backup_{}.db", timestamp)
        });

        println!("💾 Creating database backup...");
        println!("📍 Source: {}", self.database_url);
        println!("📍 Backup: {}", backup_path);

        let db = DatabaseConnection::new(&self.database_url).await?;
        db.backup(&backup_path).await?;

        println!("✅ Backup created successfully!");
        Ok(())
    }

    /// 优化数据库
    pub async fn optimize(&self) -> AppResult<()> {
        println!("⚡ Optimizing database...");
        println!("📍 Database URL: {}", self.database_url);

        let db = DatabaseConnection::new(&self.database_url).await?;
        
        println!("🔄 Running ANALYZE...");
        println!("🔄 Rebuilding indexes...");
        println!("🔄 Vacuuming database...");
        
        db.optimize().await?;

        println!("✅ Database optimization completed!");
        Ok(())
    }

    /// 健康检查
    pub async fn health(&self) -> AppResult<()> {
        println!("🏥 Performing database health check...");
        println!("📍 Database URL: {}", self.database_url);

        let db = DatabaseConnection::new(&self.database_url).await?;
        let health = db.health_check().await?;

        println!("\n🏥 Health Check Results:");
        
        if health.is_healthy {
            println!("   Status: ✅ Healthy");
        } else {
            println!("   Status: ❌ Unhealthy");
            if let Some(error) = &health.error {
                println!("   Error: {}", error);
            }
        }

        println!("   Response time: {} ms", health.response_time_ms);
        println!("   Pool size: {} (used: {}, idle: {})", 
            health.pool_size.size, 
            health.pool_size.used, 
            health.pool_size.idle
        );

        if health.migration_status.is_up_to_date {
            println!("   Migrations: ✅ Up to date");
        } else {
            println!("   Migrations: ⚠️  {} pending", health.migration_status.pending_migrations);
        }

        println!("   Database size: {} bytes", health.database_stats.database_size);

        Ok(())
    }
}

impl Default for MigrationRunner {
    fn default() -> Self {
        Self::new().expect("Failed to create migration runner")
    }
}

/// 命令行接口
pub async fn run_cli() -> AppResult<()> {
    let args: Vec<String> = env::args().collect();
    
    if args.len() < 2 {
        print_help();
        return Ok(());
    }

    let runner = MigrationRunner::new()?;
    
    match args[1].as_str() {
        "migrate" => runner.migrate().await?,
        "status" => runner.status().await?,
        "reset" => runner.reset().await?,
        "backup" => {
            let backup_path = args.get(2).cloned();
            runner.backup(backup_path).await?
        },
        "optimize" => runner.optimize().await?,
        "health" => runner.health().await?,
        "help" | "--help" | "-h" => print_help(),
        _ => {
            println!("❌ Unknown command: {}", args[1]);
            print_help();
        }
    }

    Ok(())
}

/// 打印帮助信息
fn print_help() {
    println!("PaoLife Database Migration Tool");
    println!();
    println!("USAGE:");
    println!("    migration_runner <COMMAND>");
    println!();
    println!("COMMANDS:");
    println!("    migrate     Apply all pending migrations");
    println!("    status      Show current migration status");
    println!("    reset       Reset database (delete all data)");
    println!("    backup      Create database backup");
    println!("    optimize    Optimize database performance");
    println!("    health      Perform health check");
    println!("    help        Show this help message");
    println!();
    println!("ENVIRONMENT VARIABLES:");
    println!("    DATABASE_URL    Database connection URL (optional)");
    println!();
    println!("EXAMPLES:");
    println!("    migration_runner migrate");
    println!("    migration_runner status");
    println!("    migration_runner backup my_backup.db");
    println!("    DATABASE_URL=sqlite:///tmp/test.db migration_runner migrate");
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_migration_runner() {
        // 设置测试环境变量
        env::set_var("DATABASE_URL", "sqlite::memory:");
        
        let runner = MigrationRunner::new().unwrap();
        
        // 测试迁移
        runner.migrate().await.unwrap();
        
        // 测试状态检查
        runner.status().await.unwrap();
        
        // 测试健康检查
        runner.health().await.unwrap();
    }
}
