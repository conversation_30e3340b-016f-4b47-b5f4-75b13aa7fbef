// 任务实体
// 任务管理的核心业务逻辑，支持层级结构和状态管理

use crate::shared::types::{EntityId, Timestamp, TaskStatus, Priority};
use crate::shared::errors::{AppError, AppResult};
use chrono::{DateTime, Utc, NaiveDate};
use serde::{Deserialize, Serialize};

/// 任务实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Task {
    pub id: EntityId,
    pub title: String,
    pub description: Option<String>,
    pub status: TaskStatus,
    pub priority: Priority,
    pub parent_task_id: Option<EntityId>,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub assigned_to: Option<EntityId>,
    pub due_date: Option<NaiveDate>,
    pub estimated_minutes: Option<u32>,
    pub actual_minutes: u32,
    pub completion_percentage: u8, // 0-100
    pub sort_order: i32,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub completed_at: Option<Timestamp>,
}

/// 任务创建数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTaskData {
    pub title: String,
    pub description: Option<String>,
    pub priority: Option<Priority>,
    pub parent_task_id: Option<EntityId>,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub assigned_to: Option<EntityId>,
    pub due_date: Option<NaiveDate>,
    pub estimated_minutes: Option<u32>,
}

/// 任务更新数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateTaskData {
    pub title: Option<String>,
    pub description: Option<String>,
    pub status: Option<TaskStatus>,
    pub priority: Option<Priority>,
    pub parent_task_id: Option<EntityId>,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub assigned_to: Option<EntityId>,
    pub due_date: Option<NaiveDate>,
    pub estimated_minutes: Option<u32>,
    pub completion_percentage: Option<u8>,
    pub sort_order: Option<i32>,
}

/// 任务查询条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskQuery {
    pub title: Option<String>,
    pub status: Option<TaskStatus>,
    pub priority: Option<Priority>,
    pub parent_task_id: Option<EntityId>,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub assigned_to: Option<EntityId>,
    pub due_before: Option<NaiveDate>,
    pub due_after: Option<NaiveDate>,
    pub created_after: Option<Timestamp>,
    pub created_before: Option<Timestamp>,
}

impl Task {
    /// 创建新任务
    pub fn new(data: CreateTaskData) -> AppResult<Self> {
        // 验证任务标题
        Self::validate_title(&data.title)?;
        
        // 验证任务必须关联到项目或领域
        if data.project_id.is_none() && data.area_id.is_none() {
            return Err(AppError::validation("任务必须关联到项目或领域"));
        }

        let now = Utc::now();
        
        Ok(Self {
            id: crate::shared::utils::generate_id(),
            title: data.title,
            description: data.description,
            status: TaskStatus::Todo,
            priority: data.priority.unwrap_or(Priority::Medium),
            parent_task_id: data.parent_task_id,
            project_id: data.project_id,
            area_id: data.area_id,
            assigned_to: data.assigned_to,
            due_date: data.due_date,
            estimated_minutes: data.estimated_minutes,
            actual_minutes: 0,
            completion_percentage: 0,
            sort_order: 0,
            created_at: now,
            updated_at: now,
            completed_at: None,
        })
    }

    /// 更新任务信息
    pub fn update(&mut self, data: UpdateTaskData) -> AppResult<()> {
        // 验证任务标题
        if let Some(ref title) = data.title {
            Self::validate_title(title)?;
        }

        // 验证完成百分比
        if let Some(percentage) = data.completion_percentage {
            if percentage > 100 {
                return Err(AppError::validation("完成百分比不能超过100%"));
            }
        }

        // 验证父任务不能是自己
        if let Some(ref parent_id) = data.parent_task_id {
            if parent_id == &self.id {
                return Err(AppError::validation("任务不能设置自己为父任务"));
            }
        }

        // 更新字段
        if let Some(title) = data.title {
            self.title = title;
        }
        if let Some(description) = data.description {
            self.description = Some(description);
        }
        if let Some(status) = data.status {
            self.update_status(status)?;
        }
        if let Some(priority) = data.priority {
            self.priority = priority;
        }
        if let Some(parent_task_id) = data.parent_task_id {
            self.parent_task_id = Some(parent_task_id);
        }
        if let Some(project_id) = data.project_id {
            self.project_id = Some(project_id);
        }
        if let Some(area_id) = data.area_id {
            self.area_id = Some(area_id);
        }
        if let Some(assigned_to) = data.assigned_to {
            self.assigned_to = Some(assigned_to);
        }
        if let Some(due_date) = data.due_date {
            self.due_date = Some(due_date);
        }
        if let Some(estimated_minutes) = data.estimated_minutes {
            self.estimated_minutes = Some(estimated_minutes);
        }
        if let Some(completion_percentage) = data.completion_percentage {
            self.update_completion_percentage(completion_percentage)?;
        }
        if let Some(sort_order) = data.sort_order {
            self.sort_order = sort_order;
        }

        // 验证更新后任务必须关联到项目或领域
        if self.project_id.is_none() && self.area_id.is_none() {
            return Err(AppError::validation("任务必须关联到项目或领域"));
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// 更新任务状态
    pub fn update_status(&mut self, new_status: TaskStatus) -> AppResult<()> {
        // 验证状态转换
        match (&self.status, &new_status) {
            (TaskStatus::Cancelled, _) if new_status != TaskStatus::Todo => {
                return Err(AppError::business_logic("已取消的任务只能恢复为待办状态"));
            }
            _ => {}
        }

        let old_status = self.status.clone();
        self.status = new_status;
        
        // 自动更新完成时间和百分比
        match self.status {
            TaskStatus::Completed => {
                if old_status != TaskStatus::Completed {
                    self.completed_at = Some(Utc::now());
                    self.completion_percentage = 100;
                }
            }
            _ => {
                if old_status == TaskStatus::Completed {
                    self.completed_at = None;
                    if self.completion_percentage == 100 {
                        self.completion_percentage = 90; // 设为接近完成但未完成
                    }
                }
            }
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// 更新完成百分比
    pub fn update_completion_percentage(&mut self, percentage: u8) -> AppResult<()> {
        if percentage > 100 {
            return Err(AppError::validation("完成百分比不能超过100%"));
        }

        self.completion_percentage = percentage;
        
        // 自动更新状态
        match percentage {
            0 if self.status == TaskStatus::InProgress => {
                self.status = TaskStatus::Todo;
            }
            1..=99 if self.status == TaskStatus::Todo => {
                self.status = TaskStatus::InProgress;
            }
            100 if self.status != TaskStatus::Completed => {
                self.status = TaskStatus::Completed;
                self.completed_at = Some(Utc::now());
            }
            _ => {}
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// 开始任务
    pub fn start(&mut self) -> AppResult<()> {
        if self.status == TaskStatus::Cancelled {
            return Err(AppError::business_logic("已取消的任务不能开始"));
        }

        self.status = TaskStatus::InProgress;
        if self.completion_percentage == 0 {
            self.completion_percentage = 1;
        }
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 完成任务
    pub fn complete(&mut self) -> AppResult<()> {
        if self.status == TaskStatus::Cancelled {
            return Err(AppError::business_logic("已取消的任务不能完成"));
        }

        self.status = TaskStatus::Completed;
        self.completion_percentage = 100;
        self.completed_at = Some(Utc::now());
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 取消任务
    pub fn cancel(&mut self) -> AppResult<()> {
        self.status = TaskStatus::Cancelled;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 设置等待状态
    pub fn set_waiting(&mut self) -> AppResult<()> {
        if self.status == TaskStatus::Cancelled {
            return Err(AppError::business_logic("已取消的任务不能设置为等待"));
        }

        self.status = TaskStatus::Waiting;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 添加工作时间
    pub fn add_work_time(&mut self, minutes: u32) -> AppResult<()> {
        self.actual_minutes += minutes;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 验证任务标题
    fn validate_title(title: &str) -> AppResult<()> {
        if title.trim().is_empty() {
            return Err(AppError::validation("任务标题不能为空"));
        }
        
        if title.len() > 500 {
            return Err(AppError::validation("任务标题长度不能超过500个字符"));
        }

        Ok(())
    }

    /// 检查是否逾期
    pub fn is_overdue(&self) -> bool {
        if let Some(due_date) = self.due_date {
            let today = chrono::Utc::now().date_naive();
            due_date < today && self.status.is_active()
        } else {
            false
        }
    }

    /// 获取剩余天数
    pub fn days_remaining(&self) -> Option<i64> {
        self.due_date.map(|due_date| {
            let today = chrono::Utc::now().date_naive();
            (due_date - today).num_days()
        })
    }

    /// 检查是否为根任务
    pub fn is_root_task(&self) -> bool {
        self.parent_task_id.is_none()
    }

    /// 检查是否为子任务
    pub fn is_subtask(&self) -> bool {
        self.parent_task_id.is_some()
    }

    /// 计算完成率
    pub fn completion_rate(&self) -> f64 {
        self.completion_percentage as f64 / 100.0
    }

    /// 计算时间效率
    pub fn time_efficiency(&self) -> Option<f64> {
        self.estimated_minutes.map(|estimated| {
            if estimated == 0 {
                0.0
            } else {
                self.actual_minutes as f64 / estimated as f64
            }
        })
    }

    /// 获取任务统计信息
    pub fn get_stats(&self) -> TaskStats {
        TaskStats {
            completion_rate: self.completion_rate(),
            is_overdue: self.is_overdue(),
            days_remaining: self.days_remaining(),
            time_efficiency: self.time_efficiency(),
            is_root_task: self.is_root_task(),
            days_since_created: (Utc::now() - self.created_at).num_days(),
            days_since_updated: (Utc::now() - self.updated_at).num_days(),
            days_to_complete: self.completed_at.map(|completed| {
                (completed - self.created_at).num_days()
            }),
        }
    }
}

/// 任务统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStats {
    pub completion_rate: f64,
    pub is_overdue: bool,
    pub days_remaining: Option<i64>,
    pub time_efficiency: Option<f64>,
    pub is_root_task: bool,
    pub days_since_created: i64,
    pub days_since_updated: i64,
    pub days_to_complete: Option<i64>,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_task_creation() {
        let data = CreateTaskData {
            title: "Test Task".to_string(),
            description: Some("A test task".to_string()),
            priority: Some(Priority::High),
            parent_task_id: None,
            project_id: Some("project1".to_string()),
            area_id: None,
            assigned_to: Some("user1".to_string()),
            due_date: None,
            estimated_minutes: Some(120),
        };

        let task = Task::new(data).unwrap();
        assert_eq!(task.title, "Test Task");
        assert_eq!(task.status, TaskStatus::Todo);
        assert_eq!(task.completion_percentage, 0);
        assert_eq!(task.priority, Priority::High);
    }

    #[test]
    fn test_task_validation() {
        // 测试空标题
        let data = CreateTaskData {
            title: "".to_string(),
            description: None,
            priority: None,
            parent_task_id: None,
            project_id: Some("project1".to_string()),
            area_id: None,
            assigned_to: None,
            due_date: None,
            estimated_minutes: None,
        };
        assert!(Task::new(data).is_err());

        // 测试没有关联项目或领域
        let data = CreateTaskData {
            title: "Test Task".to_string(),
            description: None,
            priority: None,
            parent_task_id: None,
            project_id: None,
            area_id: None,
            assigned_to: None,
            due_date: None,
            estimated_minutes: None,
        };
        assert!(Task::new(data).is_err());
    }

    #[test]
    fn test_task_status_transitions() {
        let data = CreateTaskData {
            title: "Test Task".to_string(),
            description: None,
            priority: None,
            parent_task_id: None,
            project_id: Some("project1".to_string()),
            area_id: None,
            assigned_to: None,
            due_date: None,
            estimated_minutes: None,
        };

        let mut task = Task::new(data).unwrap();
        
        // 开始任务
        task.start().unwrap();
        assert_eq!(task.status, TaskStatus::InProgress);
        assert_eq!(task.completion_percentage, 1);
        
        // 完成任务
        task.complete().unwrap();
        assert_eq!(task.status, TaskStatus::Completed);
        assert_eq!(task.completion_percentage, 100);
        assert!(task.completed_at.is_some());
    }

    #[test]
    fn test_completion_percentage_update() {
        let data = CreateTaskData {
            title: "Test Task".to_string(),
            description: None,
            priority: None,
            parent_task_id: None,
            project_id: Some("project1".to_string()),
            area_id: None,
            assigned_to: None,
            due_date: None,
            estimated_minutes: None,
        };

        let mut task = Task::new(data).unwrap();
        
        // 更新完成百分比应该自动更新状态
        task.update_completion_percentage(50).unwrap();
        assert_eq!(task.completion_percentage, 50);
        assert_eq!(task.status, TaskStatus::InProgress);
        
        task.update_completion_percentage(100).unwrap();
        assert_eq!(task.completion_percentage, 100);
        assert_eq!(task.status, TaskStatus::Completed);
        assert!(task.completed_at.is_some());
    }
}
