// 项目实体
// 项目管理的核心业务逻辑，基于P.A.R.A.方法论

use crate::shared::types::{EntityId, Timestamp, ProjectStatus, Priority};
use crate::shared::errors::{AppError, AppResult};
use chrono::{DateTime, Utc, NaiveDate};
use serde::{Deserialize, Serialize};

/// 项目实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Project {
    pub id: EntityId,
    pub name: String,
    pub description: Option<String>,
    pub status: ProjectStatus,
    pub progress: u8, // 0-100
    pub priority: Priority,
    pub start_date: Option<NaiveDate>,
    pub deadline: Option<NaiveDate>,
    pub estimated_hours: Option<u32>,
    pub actual_hours: u32,
    pub area_id: Option<EntityId>,
    pub created_by: EntityId,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub archived_at: Option<Timestamp>,
}

/// 项目创建数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct CreateProjectData {
    pub name: String,
    pub description: Option<String>,
    pub priority: Option<Priority>,
    pub start_date: Option<NaiveDate>,
    pub deadline: Option<NaiveDate>,
    pub estimated_hours: Option<u32>,
    pub area_id: Option<EntityId>,
    pub created_by: EntityId,
}

/// 项目更新数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateProjectData {
    pub name: Option<String>,
    pub description: Option<String>,
    pub status: Option<ProjectStatus>,
    pub progress: Option<u8>,
    pub priority: Option<Priority>,
    pub start_date: Option<NaiveDate>,
    pub deadline: Option<NaiveDate>,
    pub estimated_hours: Option<u32>,
    pub area_id: Option<EntityId>,
}

/// 项目查询条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectQuery {
    pub name: Option<String>,
    pub status: Option<ProjectStatus>,
    pub priority: Option<Priority>,
    pub area_id: Option<EntityId>,
    pub created_by: Option<EntityId>,
    pub deadline_before: Option<NaiveDate>,
    pub deadline_after: Option<NaiveDate>,
    pub created_after: Option<Timestamp>,
    pub created_before: Option<Timestamp>,
}

impl Project {
    /// 创建新项目
    pub fn new(data: CreateProjectData) -> AppResult<Self> {
        // 验证项目名称
        Self::validate_name(&data.name)?;
        
        // 验证日期
        if let (Some(start), Some(deadline)) = (data.start_date, data.deadline) {
            if start > deadline {
                return Err(AppError::validation("开始日期不能晚于截止日期"));
            }
        }

        let now = Utc::now();
        
        Ok(Self {
            id: crate::shared::utils::generate_id(),
            name: data.name,
            description: data.description,
            status: ProjectStatus::NotStarted,
            progress: 0,
            priority: data.priority.unwrap_or(Priority::Medium),
            start_date: data.start_date,
            deadline: data.deadline,
            estimated_hours: data.estimated_hours,
            actual_hours: 0,
            area_id: data.area_id,
            created_by: data.created_by,
            created_at: now,
            updated_at: now,
            archived_at: None,
        })
    }

    /// 更新项目信息
    pub fn update(&mut self, data: UpdateProjectData) -> AppResult<()> {
        // 验证项目名称
        if let Some(ref name) = data.name {
            Self::validate_name(name)?;
        }

        // 验证进度
        if let Some(progress) = data.progress {
            if progress > 100 {
                return Err(AppError::validation("项目进度不能超过100%"));
            }
        }

        // 更新字段
        if let Some(name) = data.name {
            self.name = name;
        }
        if let Some(description) = data.description {
            self.description = Some(description);
        }
        if let Some(status) = data.status {
            self.update_status(status)?;
        }
        if let Some(progress) = data.progress {
            self.update_progress(progress)?;
        }
        if let Some(priority) = data.priority {
            self.priority = priority;
        }
        if let Some(start_date) = data.start_date {
            self.start_date = Some(start_date);
        }
        if let Some(deadline) = data.deadline {
            self.deadline = Some(deadline);
        }
        if let Some(estimated_hours) = data.estimated_hours {
            self.estimated_hours = Some(estimated_hours);
        }
        if let Some(area_id) = data.area_id {
            self.area_id = Some(area_id);
        }

        // 验证更新后的日期
        if let (Some(start), Some(deadline)) = (self.start_date, self.deadline) {
            if start > deadline {
                return Err(AppError::validation("开始日期不能晚于截止日期"));
            }
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// 更新项目状态
    pub fn update_status(&mut self, new_status: ProjectStatus) -> AppResult<()> {
        // 验证状态转换
        match (&self.status, &new_status) {
            (ProjectStatus::Archived, _) => {
                return Err(AppError::business_logic("已归档的项目不能更改状态"));
            }
            (ProjectStatus::Completed, ProjectStatus::NotStarted) => {
                return Err(AppError::business_logic("已完成的项目不能回到未开始状态"));
            }
            _ => {}
        }

        self.status = new_status;
        
        // 自动更新进度
        match self.status {
            ProjectStatus::NotStarted => self.progress = 0,
            ProjectStatus::Completed => self.progress = 100,
            _ => {}
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// 更新项目进度
    pub fn update_progress(&mut self, new_progress: u8) -> AppResult<()> {
        if new_progress > 100 {
            return Err(AppError::validation("项目进度不能超过100%"));
        }

        self.progress = new_progress;
        
        // 自动更新状态
        match new_progress {
            0 if self.status == ProjectStatus::InProgress => {
                self.status = ProjectStatus::NotStarted;
            }
            1..=99 if self.status == ProjectStatus::NotStarted => {
                self.status = ProjectStatus::InProgress;
            }
            100 if self.status != ProjectStatus::Completed => {
                self.status = ProjectStatus::Completed;
            }
            _ => {}
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// 开始项目
    pub fn start(&mut self) -> AppResult<()> {
        if self.status == ProjectStatus::Archived {
            return Err(AppError::business_logic("已归档的项目不能开始"));
        }

        self.status = ProjectStatus::InProgress;
        if self.progress == 0 {
            self.progress = 1;
        }
        if self.start_date.is_none() {
            self.start_date = Some(chrono::Utc::now().date_naive());
        }
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 完成项目
    pub fn complete(&mut self) -> AppResult<()> {
        if self.status == ProjectStatus::Archived {
            return Err(AppError::business_logic("已归档的项目不能完成"));
        }

        self.status = ProjectStatus::Completed;
        self.progress = 100;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 暂停项目
    pub fn pause(&mut self) -> AppResult<()> {
        if !self.status.is_active() {
            return Err(AppError::business_logic("只有活跃的项目才能暂停"));
        }

        self.status = ProjectStatus::Paused;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 归档项目
    pub fn archive(&mut self) -> AppResult<()> {
        self.status = ProjectStatus::Archived;
        self.archived_at = Some(Utc::now());
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 取消归档
    pub fn unarchive(&mut self) -> AppResult<()> {
        if self.status != ProjectStatus::Archived {
            return Err(AppError::business_logic("只有已归档的项目才能取消归档"));
        }

        self.status = if self.progress == 100 {
            ProjectStatus::Completed
        } else if self.progress > 0 {
            ProjectStatus::InProgress
        } else {
            ProjectStatus::NotStarted
        };
        
        self.archived_at = None;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 添加工作时间
    pub fn add_work_hours(&mut self, hours: u32) -> AppResult<()> {
        self.actual_hours += hours;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 验证项目名称
    fn validate_name(name: &str) -> AppResult<()> {
        if name.trim().is_empty() {
            return Err(AppError::validation("项目名称不能为空"));
        }
        
        if name.len() > 200 {
            return Err(AppError::validation("项目名称长度不能超过200个字符"));
        }

        Ok(())
    }

    /// 检查是否逾期
    pub fn is_overdue(&self) -> bool {
        if let Some(deadline) = self.deadline {
            let today = chrono::Utc::now().date_naive();
            deadline < today && !self.status.is_completed()
        } else {
            false
        }
    }

    /// 获取剩余天数
    pub fn days_remaining(&self) -> Option<i64> {
        self.deadline.map(|deadline| {
            let today = chrono::Utc::now().date_naive();
            (deadline - today).num_days()
        })
    }

    /// 获取项目持续时间
    pub fn duration_days(&self) -> Option<i64> {
        self.start_date.map(|start| {
            let end = self.deadline.unwrap_or_else(|| chrono::Utc::now().date_naive());
            (end - start).num_days()
        })
    }

    /// 计算完成率
    pub fn completion_rate(&self) -> f64 {
        self.progress as f64 / 100.0
    }

    /// 计算工时效率
    pub fn hour_efficiency(&self) -> Option<f64> {
        self.estimated_hours.map(|estimated| {
            if estimated == 0 {
                0.0
            } else {
                self.actual_hours as f64 / estimated as f64
            }
        })
    }

    /// 获取项目统计信息
    pub fn get_stats(&self) -> ProjectStats {
        ProjectStats {
            completion_rate: self.completion_rate(),
            is_overdue: self.is_overdue(),
            days_remaining: self.days_remaining(),
            duration_days: self.duration_days(),
            hour_efficiency: self.hour_efficiency(),
            days_since_created: (Utc::now() - self.created_at).num_days(),
            days_since_updated: (Utc::now() - self.updated_at).num_days(),
        }
    }
}

/// 项目统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectStats {
    pub completion_rate: f64,
    pub is_overdue: bool,
    pub days_remaining: Option<i64>,
    pub duration_days: Option<i64>,
    pub hour_efficiency: Option<f64>,
    pub days_since_created: i64,
    pub days_since_updated: i64,
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_project_creation() {
        let data = CreateProjectData {
            name: "Test Project".to_string(),
            description: Some("A test project".to_string()),
            priority: Some(Priority::High),
            start_date: None,
            deadline: None,
            estimated_hours: Some(40),
            area_id: None,
            created_by: "user1".to_string(),
        };

        let project = Project::new(data).unwrap();
        assert_eq!(project.name, "Test Project");
        assert_eq!(project.status, ProjectStatus::NotStarted);
        assert_eq!(project.progress, 0);
        assert_eq!(project.priority, Priority::High);
    }

    #[test]
    fn test_project_progress_update() {
        let data = CreateProjectData {
            name: "Test Project".to_string(),
            description: None,
            priority: None,
            start_date: None,
            deadline: None,
            estimated_hours: None,
            area_id: None,
            created_by: "user1".to_string(),
        };

        let mut project = Project::new(data).unwrap();
        
        // 更新进度应该自动更新状态
        project.update_progress(50).unwrap();
        assert_eq!(project.progress, 50);
        assert_eq!(project.status, ProjectStatus::InProgress);
        
        project.update_progress(100).unwrap();
        assert_eq!(project.progress, 100);
        assert_eq!(project.status, ProjectStatus::Completed);
    }

    #[test]
    fn test_project_status_transitions() {
        let data = CreateProjectData {
            name: "Test Project".to_string(),
            description: None,
            priority: None,
            start_date: None,
            deadline: None,
            estimated_hours: None,
            area_id: None,
            created_by: "user1".to_string(),
        };

        let mut project = Project::new(data).unwrap();
        
        // 开始项目
        project.start().unwrap();
        assert_eq!(project.status, ProjectStatus::InProgress);
        assert_eq!(project.progress, 1);
        
        // 完成项目
        project.complete().unwrap();
        assert_eq!(project.status, ProjectStatus::Completed);
        assert_eq!(project.progress, 100);
        
        // 归档项目
        project.archive().unwrap();
        assert_eq!(project.status, ProjectStatus::Archived);
        assert!(project.archived_at.is_some());
    }

    #[test]
    fn test_project_validation() {
        let data = CreateProjectData {
            name: "".to_string(), // 空名称
            description: None,
            priority: None,
            start_date: None,
            deadline: None,
            estimated_hours: None,
            area_id: None,
            created_by: "user1".to_string(),
        };

        assert!(Project::new(data).is_err());
    }

    #[test]
    fn test_date_validation() {
        let start_date = chrono::NaiveDate::from_ymd_opt(2024, 1, 15).unwrap();
        let deadline = chrono::NaiveDate::from_ymd_opt(2024, 1, 10).unwrap(); // 早于开始日期

        let data = CreateProjectData {
            name: "Test Project".to_string(),
            description: None,
            priority: None,
            start_date: Some(start_date),
            deadline: Some(deadline),
            estimated_hours: None,
            area_id: None,
            created_by: "user1".to_string(),
        };

        assert!(Project::new(data).is_err());
    }
}
