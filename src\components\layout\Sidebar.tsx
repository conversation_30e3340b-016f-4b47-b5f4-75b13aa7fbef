// 侧边栏组件
// 应用左侧导航栏，包含主要功能菜单

import { Component, createSignal, Show, For } from 'solid-js';
import { A, useLocation } from '@solidjs/router';
import { useAuth } from '../../contexts/AuthContext';

interface SidebarProps {
  collapsed?: boolean;
  onToggleCollapse?: () => void;
  onClose?: () => void;
  isMobile?: boolean;
}

interface MenuItem {
  id: string;
  label: string;
  href: string;
  icon: () => JSX.Element;
  badge?: string;
  children?: MenuItem[];
}

export const Sidebar: Component<SidebarProps> = (props) => {
  const location = useLocation();
  const { user } = useAuth();
  
  const [expandedItems, setExpandedItems] = createSignal<Set<string>>(new Set());

  // 菜单项配置
  const menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: '仪表板',
      href: '/dashboard',
      icon: () => (
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z" />
        </svg>
      )
    },
    {
      id: 'projects',
      label: '项目',
      href: '/projects',
      icon: () => (
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      ),
      children: [
        {
          id: 'projects-all',
          label: '所有项目',
          href: '/projects',
          icon: () => <div class="w-2 h-2 rounded-full bg-gray-400" />
        },
        {
          id: 'projects-active',
          label: '进行中',
          href: '/projects?status=active',
          icon: () => <div class="w-2 h-2 rounded-full bg-green-400" />
        },
        {
          id: 'projects-completed',
          label: '已完成',
          href: '/projects?status=completed',
          icon: () => <div class="w-2 h-2 rounded-full bg-blue-400" />
        }
      ]
    },
    {
      id: 'tasks',
      label: '任务',
      href: '/tasks',
      icon: () => (
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
        </svg>
      ),
      badge: '12',
      children: [
        {
          id: 'tasks-all',
          label: '所有任务',
          href: '/tasks',
          icon: () => <div class="w-2 h-2 rounded-full bg-gray-400" />
        },
        {
          id: 'tasks-today',
          label: '今日任务',
          href: '/tasks?filter=today',
          icon: () => <div class="w-2 h-2 rounded-full bg-orange-400" />
        },
        {
          id: 'tasks-overdue',
          label: '逾期任务',
          href: '/tasks?filter=overdue',
          icon: () => <div class="w-2 h-2 rounded-full bg-red-400" />
        }
      ]
    },
    {
      id: 'areas',
      label: '领域',
      href: '/areas',
      icon: () => (
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      )
    },
    {
      id: 'calendar',
      label: '日历',
      href: '/calendar',
      icon: () => (
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
        </svg>
      )
    },
    {
      id: 'analytics',
      label: '统计分析',
      href: '/analytics',
      icon: () => (
        <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      )
    }
  ];

  // 检查菜单项是否激活
  const isActive = (href: string) => {
    const currentPath = location.pathname;
    if (href === '/dashboard') {
      return currentPath === '/' || currentPath === '/dashboard';
    }
    return currentPath.startsWith(href);
  };

  // 切换子菜单展开状态
  const toggleExpanded = (itemId: string) => {
    const expanded = expandedItems();
    if (expanded.has(itemId)) {
      expanded.delete(itemId);
    } else {
      expanded.add(itemId);
    }
    setExpandedItems(new Set(expanded));
  };

  // 检查是否有激活的子菜单
  const hasActiveChild = (children?: MenuItem[]) => {
    return children?.some(child => isActive(child.href)) || false;
  };

  return (
    <div class="flex flex-col h-full bg-gray-900">
      {/* Logo区域 */}
      <div class="flex items-center h-16 flex-shrink-0 px-4 bg-gray-900">
        <Show
          when={!props.collapsed}
          fallback={
            <div class="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg">
              <span class="text-white font-bold text-lg">P</span>
            </div>
          }
        >
          <div class="flex items-center">
            <div class="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-lg mr-3">
              <span class="text-white font-bold text-lg">P</span>
            </div>
            <span class="text-white text-xl font-semibold">PaoLife</span>
          </div>
        </Show>
      </div>

      {/* 用户信息 */}
      <Show when={!props.collapsed}>
        <div class="px-4 py-3 border-b border-gray-700">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                <span class="text-sm font-medium text-white">
                  {user()?.username?.charAt(0).toUpperCase() || 'U'}
                </span>
              </div>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-white">
                {user()?.full_name || user()?.username}
              </p>
              <p class="text-xs text-gray-300">
                {user()?.email}
              </p>
            </div>
          </div>
        </div>
      </Show>

      {/* 导航菜单 */}
      <nav class="mt-5 flex-1 px-2 space-y-1 overflow-y-auto">
        <For each={menuItems}>
          {(item) => (
            <div>
              {/* 主菜单项 */}
              <div class="group">
                <Show
                  when={item.children}
                  fallback={
                    <A
                      href={item.href}
                      class={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                        isActive(item.href)
                          ? 'bg-gray-800 text-white'
                          : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                      }`}
                      onClick={props.onClose}
                    >
                      <div class="mr-3 flex-shrink-0 h-6 w-6">
                        {item.icon()}
                      </div>
                      <Show when={!props.collapsed}>
                        <span class="flex-1">{item.label}</span>
                        <Show when={item.badge}>
                          <span class="ml-3 inline-block py-0.5 px-2 text-xs font-medium rounded-full bg-gray-800 text-gray-300">
                            {item.badge}
                          </span>
                        </Show>
                      </Show>
                    </A>
                  }
                >
                  <button
                    type="button"
                    class={`w-full group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                      isActive(item.href) || hasActiveChild(item.children)
                        ? 'bg-gray-800 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    }`}
                    onClick={() => {
                      if (props.collapsed) {
                        // 折叠状态下直接跳转
                        window.location.href = item.href;
                      } else {
                        toggleExpanded(item.id);
                      }
                    }}
                  >
                    <div class="mr-3 flex-shrink-0 h-6 w-6">
                      {item.icon()}
                    </div>
                    <Show when={!props.collapsed}>
                      <span class="flex-1 text-left">{item.label}</span>
                      <Show when={item.badge}>
                        <span class="ml-3 inline-block py-0.5 px-2 text-xs font-medium rounded-full bg-gray-800 text-gray-300">
                          {item.badge}
                        </span>
                      </Show>
                      <svg
                        class={`ml-3 h-5 w-5 transform transition-transform ${
                          expandedItems().has(item.id) ? 'rotate-90' : ''
                        }`}
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </Show>
                  </button>
                </Show>
              </div>

              {/* 子菜单 */}
              <Show when={item.children && !props.collapsed && expandedItems().has(item.id)}>
                <div class="mt-1 space-y-1">
                  <For each={item.children}>
                    {(child) => (
                      <A
                        href={child.href}
                        class={`group flex items-center pl-11 pr-2 py-2 text-sm font-medium rounded-md transition-colors ${
                          isActive(child.href)
                            ? 'bg-gray-800 text-white'
                            : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                        }`}
                        onClick={props.onClose}
                      >
                        <div class="mr-3 flex-shrink-0 h-4 w-4">
                          {child.icon()}
                        </div>
                        <span>{child.label}</span>
                      </A>
                    )}
                  </For>
                </div>
              </Show>
            </div>
          )}
        </For>
      </nav>

      {/* 底部操作 */}
      <div class="flex-shrink-0 p-4 border-t border-gray-700">
        <Show
          when={!props.collapsed}
          fallback={
            <button
              type="button"
              class="w-full flex justify-center items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white"
              onClick={props.onToggleCollapse}
            >
              <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
              </svg>
            </button>
          }
        >
          <div class="space-y-2">
            <button
              type="button"
              class="w-full flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white"
              onClick={() => window.location.href = '/settings'}
            >
              <svg class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              设置
            </button>
            
            <Show when={props.onToggleCollapse}>
              <button
                type="button"
                class="w-full flex items-center px-2 py-2 text-sm font-medium text-gray-300 rounded-md hover:bg-gray-700 hover:text-white"
                onClick={props.onToggleCollapse}
              >
                <svg class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                </svg>
                收起侧栏
              </button>
            </Show>
          </div>
        </Show>
      </div>
    </div>
  );
};

export default Sidebar;
