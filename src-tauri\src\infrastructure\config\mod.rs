// 配置管理模块
// 负责应用配置的加载、验证和管理

use crate::shared::errors::{AppError, AppResult};
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tracing::Level;

pub mod database;
pub mod logging;

/// 应用配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    pub database: DatabaseConfig,
    pub logging: LoggingConfig,
    pub cache: CacheConfig,
    pub security: SecurityConfig,
    pub performance: PerformanceConfig,
}

/// 数据库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub url: String,
    pub max_connections: u32,
    pub min_connections: u32,
    pub connection_timeout_seconds: u64,
    pub idle_timeout_seconds: u64,
    pub max_lifetime_seconds: u64,
    pub enable_migrations: bool,
    pub enable_foreign_keys: bool,
}

/// 日志配置
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct LoggingConfig {
    pub level: String,
    pub format: LogFormat,
    pub output: LogOutput,
    pub file_path: Option<PathBuf>,
    pub max_file_size_mb: u64,
    pub max_files: u32,
    pub enable_console: bool,
    pub enable_file: bool,
    pub filter_targets: Vec<String>,
}

/// 缓存配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CacheConfig {
    pub enable_memory_cache: bool,
    pub memory_cache_size_mb: u64,
    pub enable_disk_cache: bool,
    pub disk_cache_path: PathBuf,
    pub disk_cache_size_mb: u64,
    pub default_ttl_seconds: u64,
}

/// 安全配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SecurityConfig {
    pub enable_csrf_protection: bool,
    pub session_timeout_minutes: u64,
    pub max_login_attempts: u32,
    pub lockout_duration_minutes: u64,
    pub password_min_length: u32,
    pub require_special_chars: bool,
}

/// 性能配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceConfig {
    pub worker_threads: Option<usize>,
    pub blocking_threads: Option<usize>,
    pub enable_metrics: bool,
    pub metrics_interval_seconds: u64,
    pub query_timeout_seconds: u64,
}

/// 日志格式枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LogFormat {
    Json,
    Pretty,
    Compact,
}

/// 日志输出枚举
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "lowercase")]
pub enum LogOutput {
    Console,
    File,
    Both,
}

impl Default for AppConfig {
    fn default() -> Self {
        Self {
            database: DatabaseConfig::default(),
            logging: LoggingConfig::default(),
            cache: CacheConfig::default(),
            security: SecurityConfig::default(),
            performance: PerformanceConfig::default(),
        }
    }
}

impl Default for DatabaseConfig {
    fn default() -> Self {
        Self {
            url: "sqlite:./data/paolife.db".to_string(),
            max_connections: 10,
            min_connections: 1,
            connection_timeout_seconds: 30,
            idle_timeout_seconds: 600,
            max_lifetime_seconds: 3600,
            enable_migrations: true,
            enable_foreign_keys: true,
        }
    }
}

impl Default for LoggingConfig {
    fn default() -> Self {
        Self {
            level: "info".to_string(),
            format: LogFormat::Pretty,
            output: LogOutput::Both,
            file_path: Some(PathBuf::from("./logs/paolife.log")),
            max_file_size_mb: 10,
            max_files: 5,
            enable_console: true,
            enable_file: true,
            filter_targets: vec![
                "paolife".to_string(),
                "sqlx".to_string(),
                "tauri".to_string(),
            ],
        }
    }
}

impl Default for CacheConfig {
    fn default() -> Self {
        Self {
            enable_memory_cache: true,
            memory_cache_size_mb: 100,
            enable_disk_cache: true,
            disk_cache_path: PathBuf::from("./cache"),
            disk_cache_size_mb: 500,
            default_ttl_seconds: 3600,
        }
    }
}

impl Default for SecurityConfig {
    fn default() -> Self {
        Self {
            enable_csrf_protection: true,
            session_timeout_minutes: 480, // 8 hours
            max_login_attempts: 5,
            lockout_duration_minutes: 15,
            password_min_length: 8,
            require_special_chars: true,
        }
    }
}

impl Default for PerformanceConfig {
    fn default() -> Self {
        Self {
            worker_threads: None, // 使用默认值
            blocking_threads: None, // 使用默认值
            enable_metrics: true,
            metrics_interval_seconds: 60,
            query_timeout_seconds: 30,
        }
    }
}

impl LoggingConfig {
    /// 将字符串级别转换为tracing::Level
    pub fn to_tracing_level(&self) -> AppResult<Level> {
        match self.level.to_lowercase().as_str() {
            "trace" => Ok(Level::TRACE),
            "debug" => Ok(Level::DEBUG),
            "info" => Ok(Level::INFO),
            "warn" => Ok(Level::WARN),
            "error" => Ok(Level::ERROR),
            _ => Err(AppError::configuration(format!(
                "Invalid log level: {}",
                self.level
            ))),
        }
    }

    /// 获取日志文件路径
    pub fn get_log_file_path(&self) -> AppResult<PathBuf> {
        self.file_path
            .clone()
            .ok_or_else(|| AppError::configuration("Log file path not configured"))
    }
}

/// 配置加载器
pub struct ConfigLoader;

impl ConfigLoader {
    /// 从文件加载配置
    pub fn load_from_file(path: &PathBuf) -> AppResult<AppConfig> {
        if !path.exists() {
            tracing::info!("Config file not found, creating default config: {:?}", path);
            let default_config = AppConfig::default();
            Self::save_to_file(&default_config, path)?;
            return Ok(default_config);
        }

        let content = std::fs::read_to_string(path)
            .map_err(|e| AppError::configuration(format!("Failed to read config file: {}", e)))?;

        let config: AppConfig = toml::from_str(&content)
            .map_err(|e| AppError::configuration(format!("Failed to parse config file: {}", e)))?;

        Self::validate_config(&config)?;
        Ok(config)
    }

    /// 保存配置到文件
    pub fn save_to_file(config: &AppConfig, path: &PathBuf) -> AppResult<()> {
        if let Some(parent) = path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| {
                AppError::configuration(format!("Failed to create config directory: {}", e))
            })?;
        }

        let content = toml::to_string_pretty(config)
            .map_err(|e| AppError::configuration(format!("Failed to serialize config: {}", e)))?;

        std::fs::write(path, content)
            .map_err(|e| AppError::configuration(format!("Failed to write config file: {}", e)))?;

        Ok(())
    }

    /// 验证配置
    fn validate_config(config: &AppConfig) -> AppResult<()> {
        // 验证数据库配置
        if config.database.max_connections == 0 {
            return Err(AppError::configuration(
                "Database max_connections must be greater than 0",
            ));
        }

        if config.database.min_connections > config.database.max_connections {
            return Err(AppError::configuration(
                "Database min_connections cannot be greater than max_connections",
            ));
        }

        // 验证日志配置
        config.logging.to_tracing_level()?;

        if config.logging.max_file_size_mb == 0 {
            return Err(AppError::configuration(
                "Log max_file_size_mb must be greater than 0",
            ));
        }

        // 验证缓存配置
        if config.cache.memory_cache_size_mb == 0 && config.cache.enable_memory_cache {
            return Err(AppError::configuration(
                "Memory cache size must be greater than 0 when enabled",
            ));
        }

        // 验证安全配置
        if config.security.password_min_length < 6 {
            return Err(AppError::configuration(
                "Password minimum length must be at least 6",
            ));
        }

        Ok(())
    }

    /// 获取默认配置文件路径
    pub fn get_default_config_path() -> AppResult<PathBuf> {
        let app_dir = tauri::api::path::app_config_dir(&tauri::Config::default())
            .ok_or_else(|| AppError::configuration("Failed to get app config directory"))?;

        Ok(app_dir.join("config.toml"))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use tempfile::tempdir;

    #[test]
    fn test_default_config() {
        let config = AppConfig::default();
        assert_eq!(config.database.max_connections, 10);
        assert_eq!(config.logging.level, "info");
        assert!(config.cache.enable_memory_cache);
    }

    #[test]
    fn test_config_validation() {
        let mut config = AppConfig::default();
        
        // 测试无效的数据库配置
        config.database.max_connections = 0;
        assert!(ConfigLoader::validate_config(&config).is_err());
        
        // 修复配置
        config.database.max_connections = 10;
        config.database.min_connections = 15; // 大于max_connections
        assert!(ConfigLoader::validate_config(&config).is_err());
    }

    #[test]
    fn test_log_level_conversion() {
        let mut config = LoggingConfig::default();
        
        config.level = "debug".to_string();
        assert_eq!(config.to_tracing_level().unwrap(), Level::DEBUG);
        
        config.level = "invalid".to_string();
        assert!(config.to_tracing_level().is_err());
    }

    #[test]
    fn test_config_file_operations() {
        let temp_dir = tempdir().unwrap();
        let config_path = temp_dir.path().join("test_config.toml");
        
        let original_config = AppConfig::default();
        
        // 保存配置
        ConfigLoader::save_to_file(&original_config, &config_path).unwrap();
        assert!(config_path.exists());
        
        // 加载配置
        let loaded_config = ConfigLoader::load_from_file(&config_path).unwrap();
        assert_eq!(loaded_config.database.max_connections, original_config.database.max_connections);
    }
}
