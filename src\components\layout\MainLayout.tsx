// 主布局组件
// 提供应用的主要布局结构，包括导航栏、侧边栏和内容区域

import { Component, JSX, createSignal, Show, onMount } from 'solid-js';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from '@solidjs/router';
import { Sidebar } from './Sidebar';
import { Header } from './Header';
import { MobileMenu } from './MobileMenu';

interface MainLayoutProps {
  children: JSX.Element;
  title?: string;
  showSidebar?: boolean;
  sidebarCollapsed?: boolean;
}

export const MainLayout: Component<MainLayoutProps> = (props) => {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  
  const [sidebarOpen, setSidebarOpen] = createSignal(false);
  const [sidebarCollapsed, setSidebarCollapsed] = createSignal(props.sidebarCollapsed || false);
  const [isMobile, setIsMobile] = createSignal(false);

  // 检查是否为移动设备
  onMount(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    
    return () => window.removeEventListener('resize', checkMobile);
  });

  // 如果未认证且不在加载中，重定向到登录页
  if (!isLoading() && !isAuthenticated()) {
    navigate('/auth/login', { replace: true });
    return null;
  }

  // 加载中显示
  if (isLoading()) {
    return (
      <div class="min-h-screen flex items-center justify-center bg-gray-50">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p class="mt-4 text-gray-600">加载中...</p>
        </div>
      </div>
    );
  }

  const showSidebar = () => props.showSidebar !== false;

  return (
    <div class="h-screen flex overflow-hidden bg-gray-100">
      {/* 移动端菜单遮罩 */}
      <Show when={isMobile() && sidebarOpen()}>
        <div
          class="fixed inset-0 flex z-40 md:hidden"
          onClick={() => setSidebarOpen(false)}
        >
          <div class="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </div>
      </Show>

      {/* 侧边栏 */}
      <Show when={showSidebar()}>
        <div class={`
          ${isMobile() 
            ? `fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out ${
                sidebarOpen() ? 'translate-x-0' : '-translate-x-full'
              }`
            : `relative flex-shrink-0 transition-all duration-300 ${
                sidebarCollapsed() ? 'w-16' : 'w-64'
              }`
          }
        `}>
          <Sidebar
            collapsed={sidebarCollapsed()}
            onToggleCollapse={() => setSidebarCollapsed(!sidebarCollapsed())}
            onClose={() => setSidebarOpen(false)}
            isMobile={isMobile()}
          />
        </div>
      </Show>

      {/* 主内容区域 */}
      <div class="flex flex-col w-0 flex-1 overflow-hidden">
        {/* 头部 */}
        <Header
          title={props.title}
          onMenuClick={() => setSidebarOpen(true)}
          showMenuButton={isMobile() && showSidebar()}
          sidebarCollapsed={sidebarCollapsed()}
          onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed())}
          showSidebarToggle={!isMobile() && showSidebar()}
        />

        {/* 内容区域 */}
        <main class="flex-1 relative overflow-y-auto focus:outline-none">
          <div class="py-6">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
              {props.children}
            </div>
          </div>
        </main>
      </div>

      {/* 移动端菜单 */}
      <Show when={isMobile()}>
        <MobileMenu
          isOpen={sidebarOpen()}
          onClose={() => setSidebarOpen(false)}
        />
      </Show>
    </div>
  );
};

// 简化的布局组件，用于不需要侧边栏的页面
export const SimpleLayout: Component<{ children: JSX.Element; title?: string }> = (props) => {
  return (
    <div class="min-h-screen bg-gray-50">
      <Show when={props.title}>
        <div class="bg-white shadow">
          <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
            <h1 class="text-3xl font-bold text-gray-900">{props.title}</h1>
          </div>
        </div>
      </Show>
      
      <main>
        <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {props.children}
        </div>
      </main>
    </div>
  );
};

// 全屏布局组件，用于特殊页面
export const FullscreenLayout: Component<{ children: JSX.Element }> = (props) => {
  return (
    <div class="h-screen w-screen overflow-hidden">
      {props.children}
    </div>
  );
};

// 居中布局组件，用于表单页面等
export const CenteredLayout: Component<{ 
  children: JSX.Element; 
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
  title?: string;
}> = (props) => {
  const maxWidth = () => {
    switch (props.maxWidth || 'md') {
      case 'sm': return 'max-w-sm';
      case 'md': return 'max-w-md';
      case 'lg': return 'max-w-lg';
      case 'xl': return 'max-w-xl';
      case '2xl': return 'max-w-2xl';
      default: return 'max-w-md';
    }
  };

  return (
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class={`${maxWidth()} w-full space-y-8`}>
        <Show when={props.title}>
          <div class="text-center">
            <h2 class="text-3xl font-extrabold text-gray-900">
              {props.title}
            </h2>
          </div>
        </Show>
        {props.children}
      </div>
    </div>
  );
};

// 分栏布局组件
export const TwoColumnLayout: Component<{
  left: JSX.Element;
  right: JSX.Element;
  leftWidth?: 'narrow' | 'normal' | 'wide';
  gap?: 'sm' | 'md' | 'lg';
}> = (props) => {
  const leftWidth = () => {
    switch (props.leftWidth || 'normal') {
      case 'narrow': return 'w-1/4';
      case 'normal': return 'w-1/3';
      case 'wide': return 'w-1/2';
      default: return 'w-1/3';
    }
  };

  const gap = () => {
    switch (props.gap || 'md') {
      case 'sm': return 'gap-4';
      case 'md': return 'gap-6';
      case 'lg': return 'gap-8';
      default: return 'gap-6';
    }
  };

  return (
    <div class={`flex ${gap()} h-full`}>
      <div class={`${leftWidth()} flex-shrink-0`}>
        {props.left}
      </div>
      <div class="flex-1 min-w-0">
        {props.right}
      </div>
    </div>
  );
};

// 三栏布局组件
export const ThreeColumnLayout: Component<{
  left: JSX.Element;
  center: JSX.Element;
  right: JSX.Element;
  leftWidth?: string;
  rightWidth?: string;
  gap?: 'sm' | 'md' | 'lg';
}> = (props) => {
  const gap = () => {
    switch (props.gap || 'md') {
      case 'sm': return 'gap-4';
      case 'md': return 'gap-6';
      case 'lg': return 'gap-8';
      default: return 'gap-6';
    }
  };

  return (
    <div class={`flex ${gap()} h-full`}>
      <div class={`${props.leftWidth || 'w-64'} flex-shrink-0`}>
        {props.left}
      </div>
      <div class="flex-1 min-w-0">
        {props.center}
      </div>
      <div class={`${props.rightWidth || 'w-64'} flex-shrink-0`}>
        {props.right}
      </div>
    </div>
  );
};

// 卡片布局组件
export const CardLayout: Component<{
  children: JSX.Element;
  title?: string;
  subtitle?: string;
  actions?: JSX.Element;
  padding?: 'sm' | 'md' | 'lg';
}> = (props) => {
  const padding = () => {
    switch (props.padding || 'md') {
      case 'sm': return 'p-4';
      case 'md': return 'p-6';
      case 'lg': return 'p-8';
      default: return 'p-6';
    }
  };

  return (
    <div class="bg-white shadow rounded-lg">
      <Show when={props.title || props.actions}>
        <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <div>
            <Show when={props.title}>
              <h3 class="text-lg font-medium text-gray-900">{props.title}</h3>
            </Show>
            <Show when={props.subtitle}>
              <p class="mt-1 text-sm text-gray-500">{props.subtitle}</p>
            </Show>
          </div>
          <Show when={props.actions}>
            <div class="flex items-center space-x-2">
              {props.actions}
            </div>
          </Show>
        </div>
      </Show>
      
      <div class={padding()}>
        {props.children}
      </div>
    </div>
  );
};

export default MainLayout;
