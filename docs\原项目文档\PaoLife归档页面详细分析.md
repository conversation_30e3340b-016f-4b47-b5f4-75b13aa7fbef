# PaoLife项目详细技术文档

## 第九部分：归档页面详细分析

### 页面概览

归档页面是PaoLife应用的数据管理核心，实现了项目、领域、资源的完整归档生命周期管理。包含归档列表展示、分类统计、搜索过滤、恢复操作和永久删除等功能，确保用户可以有效管理已完成或不再活跃的内容。

### 核心架构设计

#### 1. 归档数据结构

**归档项接口**:
```typescript
export interface ArchiveItem {
  id: string
  title: string
  description?: string
  type: 'project' | 'area' | 'resource'
  originalStatus: string
  archivedAt: string
  archivedReason: 'completed' | 'cancelled' | 'inactive' | 'outdated' | 'manual'
  tags: string[]
  metadata: {
    originalId: string
    completionRate?: number
    lastActivity?: string
    size?: number
    attachments?: number
  }
}
```

**归档原因分类**:
```typescript
const ArchiveReasons = {
  COMPLETED: 'completed',     // 已完成
  CANCELLED: 'cancelled',     // 已取消
  INACTIVE: 'inactive',       // 不活跃
  OUTDATED: 'outdated',       // 已过时
  MANUAL: 'manual'            // 手动归档
} as const

// 归档原因颜色映射
const getReasonColor = (reason: string) => {
  const colors = {
    'completed': 'text-green-600 bg-green-50 border-green-200',
    'cancelled': 'text-red-600 bg-red-50 border-red-200',
    'inactive': 'text-yellow-600 bg-yellow-50 border-yellow-200',
    'outdated': 'text-gray-600 bg-gray-50 border-gray-200',
    'manual': 'text-blue-600 bg-blue-50 border-blue-200'
  }
  return colors[reason] || colors.manual
}

// 归档原因标签
const getReasonLabel = (reason: string) => {
  const labels = {
    'completed': '已完成',
    'cancelled': '已取消',
    'inactive': '不活跃',
    'outdated': '已过时',
    'manual': '手动归档'
  }
  return labels[reason] || reason
}
```

#### 2. 归档页面组件 (ArchivePage.tsx)

**页面状态管理**:
```typescript
export function ArchivePage() {
  const [items, setItems] = useState<ArchiveItemType[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [filterType, setFilterType] = useState<string>('all')
  const [filterReason, setFilterReason] = useState<string>('all')
  const [sortBy, setSortBy] = useState<string>('archivedAt')
  const [isLoading, setIsLoading] = useState(true)

  // 归档数据刷新
  const refreshArchivedItems = async () => {
    setIsLoading(true)
    try {
      const [projectsResult, areasResult] = await Promise.all([
        databaseApi.getArchivedProjects(),
        databaseApi.getArchivedAreas()
      ])

      const archivedItems: ArchiveItemType[] = []

      // 处理归档项目
      if (projectsResult.success && projectsResult.data) {
        const projectItems: ArchiveItemType[] = projectsResult.data.map((project: any) => ({
          id: project.id,
          title: project.name,
          description: project.description || '',
          type: 'project' as const,
          originalStatus: project.status || 'Unknown',
          archivedAt: project.updatedAt,
          archivedReason: 'manual' as const,
          tags: [],
          metadata: {
            originalId: project.id,
            lastActivity: project.updatedAt
          }
        }))
        archivedItems.push(...projectItems)
      }

      // 处理归档领域
      if (areasResult.success && areasResult.data) {
        const areaItems: ArchiveItemType[] = areasResult.data.map((area: any) => ({
          id: area.id,
          title: area.name,
          description: area.description || '',
          type: 'area' as const,
          originalStatus: area.status || 'Unknown',
          archivedAt: area.updatedAt,
          archivedReason: 'manual' as const,
          tags: [],
          metadata: {
            originalId: area.id,
            lastActivity: area.updatedAt
          }
        }))
        archivedItems.push(...areaItems)
      }

      setItems(archivedItems)
    } catch (error) {
      console.error('Failed to load archived items:', error)
      addNotification({
        type: 'error',
        title: '加载失败',
        message: '归档数据加载失败，请重试'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // 计算分类统计
  const counts = useMemo(() => {
    return {
      projects: items.filter(item => item.type === 'project').length,
      areas: items.filter(item => item.type === 'area').length,
      resources: items.filter(item => item.type === 'resource').length
    }
  }, [items])

  // 过滤和排序
  const filteredItems = useMemo(() => {
    let filtered = items

    // 搜索过滤
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(query) ||
        item.description?.toLowerCase().includes(query)
      )
    }

    // 类型过滤
    if (filterType !== 'all') {
      filtered = filtered.filter(item => item.type === filterType)
    }

    // 归档原因过滤
    if (filterReason !== 'all') {
      filtered = filtered.filter(item => item.archivedReason === filterReason)
    }

    // 排序
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'archivedAt':
          return new Date(b.archivedAt).getTime() - new Date(a.archivedAt).getTime()
        case 'title':
          return a.title.localeCompare(b.title)
        case 'type':
          return a.type.localeCompare(b.type)
        default:
          return 0
      }
    })

    return filtered
  }, [items, searchQuery, filterType, filterReason, sortBy])

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">归档管理</h1>
          <p className="text-muted-foreground">
            管理已完成、取消或不再活跃的项目和领域
          </p>
        </div>
        <Button onClick={refreshArchivedItems} disabled={isLoading}>
          <RefreshCw className={cn("h-4 w-4 mr-2", isLoading && "animate-spin")} />
          刷新
        </Button>
      </div>
    </div>
  )
}
```

#### 3. 归档项组件 (ArchiveItem.tsx)

**归档项展示组件**:
```typescript
export function ArchiveItem({ item, onRestore, onDelete, onView, className }: ArchiveItemProps) {
  const { confirm, ConfirmDialog: ConfirmDialogComponent } = useConfirmDialog()
  const { t } = useLanguage()

  // 格式化日期
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // 恢复确认
  const handleRestore = async () => {
    const confirmed = await confirm({
      title: '恢复项目',
      description: `确定要将"${item.title}"恢复到活跃${item.type === 'project' ? '项目' : item.type === 'area' ? '领域' : '资源'}中吗？`,
      variant: 'default',
      confirmText: '恢复',
      cancelText: '取消'
    })

    if (confirmed) {
      onRestore?.(item)
    }
  }

  // 删除确认
  const handleDelete = async () => {
    const confirmed = await confirm({
      title: '永久删除',
      description: `确定要永久删除"${item.title}"吗？此操作无法撤销。`,
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消'
    })

    if (confirmed) {
      onDelete?.(item.id)
    }
  }

  return (
    <>
      <Card className={cn('group transition-all duration-200 hover:shadow-md', className)}>
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            {/* 类型图标 */}
            <div className={cn('flex-shrink-0 p-2 rounded-lg border', getTypeColor(item.type))}>
              {getTypeIcon(item.type)}
            </div>

            {/* 内容区域 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between mb-2">
                <div className="flex-1 min-w-0">
                  <h3 className="font-medium text-sm truncate">{item.title}</h3>
                  {item.description && (
                    <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
                      {item.description}
                    </p>
                  )}
                </div>

                {/* 操作菜单 */}
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <MoreHorizontal className="w-3 h-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => onView?.(item)}>
                      <Eye className="h-4 w-4 mr-2" />
                      查看详情
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleRestore}>
                      <RotateCcw className="h-4 w-4 mr-2" />
                      恢复
                    </DropdownMenuItem>
                    <DropdownMenuItem
                      onClick={handleDelete}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      永久删除
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>

              {/* 元数据标签 */}
              <div className="flex items-center gap-2 mb-2">
                <Badge
                  variant="outline"
                  className={cn('text-xs', getReasonColor(item.archivedReason))}
                >
                  {getReasonLabel(item.archivedReason)}
                </Badge>
                <Badge variant="secondary" className="text-xs">
                  {item.type === 'project' ? '项目' : item.type === 'area' ? '领域' : '资源'}
                </Badge>
                {item.metadata.completionRate !== undefined && (
                  <Badge variant="outline" className="text-xs">
                    {item.metadata.completionRate}% 完成
                  </Badge>
                )}
              </div>

              {/* 底部信息 */}
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>归档于 {formatDate(item.archivedAt)}</span>
                <div className="flex items-center gap-3">
                  {item.metadata.attachments && (
                    <span className="flex items-center gap-1">
                      <Paperclip className="w-3 h-3" />
                      {item.metadata.attachments}
                    </span>
                  )}
                  {item.metadata.size && (
                    <span>{(item.metadata.size / 1024).toFixed(1)}KB</span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      <ConfirmDialogComponent />
    </>
  )
}
```
        </Button>
      </div>

      {/* 分类统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-project">P</Badge>
              归档项目
            </CardTitle>
            <CardDescription>已完成或取消的项目</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.projects}</div>
            <p className="text-xs text-muted-foreground">
              {counts.projects > 0 ? '最近有归档项目' : '暂无归档项目'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-area">A</Badge>
              归档领域
            </CardTitle>
            <CardDescription>不再维护的生活领域</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.areas}</div>
            <p className="text-xs text-muted-foreground">
              {counts.areas > 0 ? '最近有归档领域' : '暂无归档领域'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Badge className="para-resource">R</Badge>
              归档资源
            </CardTitle>
            <CardDescription>不再相关的资源文件</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{counts.resources}</div>
            <p className="text-xs text-muted-foreground">
              {counts.resources > 0 ? '最近有归档资源' : '暂无归档资源'}
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
```

#### 4. 恢复和删除操作

**恢复操作处理**:
```typescript
const handleRestore = async (item: ArchiveItemType) => {
  try {
    if (item.type === 'project') {
      // 先更新 store，避免恢复瞬间列表"丢失"
      await useProjectStore.getState().restoreProject(item.id)
    } else if (item.type === 'area') {
      // 领域也先本地标记再刷新
      useAreaStore.getState().updateArea(item.id, {
        archived: false,
        updatedAt: new Date() as any
      })
      const result = await databaseApi.restoreArea(item.id)
      if (!result.success) {
        throw new Error(result.error || 'Failed to restore area')
      }
    } else {
      console.warn('Resource restoration not implemented yet')
      return
    }

    // 恢复成功后统一刷新归档与主列表
    await refreshArchivedItems()
    if (item.type === 'project') {
      await fetchProjects()
    } else if (item.type === 'area') {
      await fetchAreas()
    }

    addNotification({
      type: 'success',
      title: '恢复成功',
      message: `"${item.title}" 已成功恢复`
    })

    // 体验优化：自动导航到详情页
    if (item.type === 'project') {
      navigate(`/projects/${item.id}`)
    } else if (item.type === 'area') {
      navigate(`/areas/${item.id}`)
    }
  } catch (error) {
    console.error('Error restoring item:', error)
    addNotification({
      type: 'error',
      title: '恢复失败',
      message: '恢复过程中发生错误，请重试'
    })
  }
}
```

**永久删除处理**:
```typescript
const handleDelete = async (itemId: string) => {
  try {
    const item = items.find(i => i.id === itemId)
    if (!item) return

    if (item.type === 'project') {
      const result = await databaseApi.deleteProject(itemId)
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete project')
      }
    } else if (item.type === 'area') {
      const result = await databaseApi.deleteArea(itemId)
      if (!result.success) {
        throw new Error(result.error || 'Failed to delete area')
      }
    }

    // 从本地列表中移除
    setItems(prev => prev.filter(i => i.id !== itemId))

    addNotification({
      type: 'success',
      title: '删除成功',
      message: `"${item.title}" 已永久删除`
    })
  } catch (error) {
    console.error('Error deleting item:', error)
    addNotification({
      type: 'error',
      title: '删除失败',
      message: '删除过程中发生错误，请重试'
    })
  }
}
```

### 总结

归档页面作为PaoLife应用的数据管理核心，具有以下特点：

1. **完整的归档生命周期**: 归档、查看、恢复、永久删除的全流程管理
2. **多类型支持**: 项目、领域、资源的统一归档管理
3. **智能分类**: 按归档原因、类型、时间的多维度分类
4. **安全操作**: 确认对话框、错误处理、状态同步
5. **用户体验**: 自动导航、实时反馈、批量操作
6. **数据完整性**: Store同步、数据库一致性、事务处理

这个设计为用户提供了专业级的数据归档能力，确保重要信息的安全管理和有效恢复。
```
