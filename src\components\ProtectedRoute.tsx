import { Component, ParentComponent, Show } from 'solid-js';
import { Navigate } from '@solidjs/router';
import { useAuth } from '../contexts/AuthContext';

// 受保护路由组件
export const ProtectedRoute: ParentComponent = (props) => {
  const { isAuthenticated, isLoading } = useAuth();

  return (
    <Show
      when={!isLoading()}
      fallback={
        <div class="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div class="text-center">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p class="text-gray-600 dark:text-gray-400">验证身份中...</p>
          </div>
        </div>
      }
    >
      <Show
        when={isAuthenticated()}
        fallback={<Navigate href="/login" />}
      >
        {props.children}
      </Show>
    </Show>
  );
};
