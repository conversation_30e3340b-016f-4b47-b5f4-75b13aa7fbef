[package]
name = "paolife"
version = "0.1.0"
description = "A Tauri App"
authors = ["you"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "paolife"
crate-type = ["staticlib", "cdylib", "rlib"]

[[bin]]
name = "migration_runner"
path = "src/bin/migration_runner.rs"

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
# Tauri 核心和插件
tauri = { version = "2", features = [] }
tauri-plugin-opener = "2"
tauri-plugin-global-shortcut = "2"
tauri-plugin-window-state = "2"
tauri-plugin-updater = "2"
tauri-plugin-clipboard-manager = "2"
tauri-plugin-store = "2"
tauri-plugin-fs = "2"
tauri-plugin-dialog = "2"
tauri-plugin-shell = "2"

# 序列化和数据处理
serde = { version = "1", features = ["derive"] }
serde_json = "1"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }

# 数据库和搜索
sqlx = { version = "0.8", features = ["sqlite", "runtime-tokio-rustls", "chrono", "uuid", "migrate"] }
tantivy = "0.24"

# 异步运行时和工具
tokio = { version = "1.46", features = ["full"] }
async-trait = "0.1"

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志和可观测性
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }

# 工具库
regex = "1.0"
url = "2.0"
sha2 = "0.10"
rand = "0.8"

# 配置管理
toml = "0.8"

# 日志轮转
tracing-appender = "0.2"



# 目录管理
dirs = "5.0"

# 密码哈希
bcrypt = "0.15"

