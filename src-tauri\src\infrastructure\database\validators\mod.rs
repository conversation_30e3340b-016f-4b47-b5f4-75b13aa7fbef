// 数据验证器模块
// 实现输入数据验证和业务规则约束检查

use crate::shared::errors::{AppError, AppResult};
use regex::Regex;
use std::collections::HashMap;

pub mod user_validator;
pub mod project_validator;
pub mod task_validator;
pub mod area_validator;

// 重新导出所有验证器
pub use user_validator::*;
pub use project_validator::*;
pub use task_validator::*;
pub use area_validator::*;

/// 通用验证器trait
pub trait Validator<T> {
    /// 验证数据
    fn validate(&self, data: &T) -> AppResult<()>;
    
    /// 验证并返回清理后的数据
    fn validate_and_sanitize(&self, data: T) -> AppResult<T> {
        self.validate(&data)?;
        Ok(self.sanitize(data))
    }
    
    /// 清理数据
    fn sanitize(&self, data: T) -> T;
}

/// 验证工具类
pub struct ValidationUtils;

impl ValidationUtils {
    /// 验证字符串长度
    pub fn validate_length(value: &str, field_name: &str, min: usize, max: usize) -> AppResult<()> {
        let len = value.trim().len();
        if len < min {
            return Err(AppError::validation(&format!("{} 至少需要 {} 个字符", field_name, min)));
        }
        if len > max {
            return Err(AppError::validation(&format!("{} 不能超过 {} 个字符", field_name, max)));
        }
        Ok(())
    }

    /// 验证必需字段
    pub fn validate_required(value: &str, field_name: &str) -> AppResult<()> {
        if value.trim().is_empty() {
            return Err(AppError::validation(&format!("{} 不能为空", field_name)));
        }
        Ok(())
    }

    /// 验证可选字段长度
    pub fn validate_optional_length(value: &Option<String>, field_name: &str, max: usize) -> AppResult<()> {
        if let Some(val) = value {
            if val.len() > max {
                return Err(AppError::validation(&format!("{} 不能超过 {} 个字符", field_name, max)));
            }
        }
        Ok(())
    }

    /// 验证邮箱格式
    pub fn validate_email(email: &str) -> AppResult<()> {
        let email_regex = Regex::new(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$")
            .map_err(|e| AppError::internal(&format!("Failed to compile email regex: {}", e)))?;
        
        if !email_regex.is_match(email) {
            return Err(AppError::validation("邮箱格式不正确"));
        }
        Ok(())
    }

    /// 验证用户名格式
    pub fn validate_username(username: &str) -> AppResult<()> {
        // 用户名只能包含字母、数字、下划线
        let username_regex = Regex::new(r"^[a-zA-Z0-9_]+$")
            .map_err(|e| AppError::internal(&format!("Failed to compile username regex: {}", e)))?;
        
        if !username_regex.is_match(username) {
            return Err(AppError::validation("用户名只能包含字母、数字和下划线"));
        }
        Ok(())
    }

    /// 验证密码强度
    pub fn validate_password_strength(password: &str) -> AppResult<()> {
        if password.len() < 6 {
            return Err(AppError::validation("密码至少需要6个字符"));
        }
        
        if password.len() > 128 {
            return Err(AppError::validation("密码不能超过128个字符"));
        }

        // 检查是否包含至少一个字母和一个数字
        let has_letter = password.chars().any(|c| c.is_alphabetic());
        let has_digit = password.chars().any(|c| c.is_numeric());
        
        if !has_letter || !has_digit {
            return Err(AppError::validation("密码必须包含至少一个字母和一个数字"));
        }

        Ok(())
    }

    /// 验证数值范围
    pub fn validate_range<T>(value: T, field_name: &str, min: T, max: T) -> AppResult<()>
    where
        T: PartialOrd + std::fmt::Display,
    {
        if value < min || value > max {
            return Err(AppError::validation(&format!(
                "{} 必须在 {} 到 {} 之间",
                field_name, min, max
            )));
        }
        Ok(())
    }

    /// 验证枚举值
    pub fn validate_enum<T>(value: &str, field_name: &str, valid_values: &[&str]) -> AppResult<()> {
        if !valid_values.contains(&value) {
            return Err(AppError::validation(&format!(
                "{} 必须是以下值之一: {}",
                field_name,
                valid_values.join(", ")
            )));
        }
        Ok(())
    }

    /// 验证日期范围
    pub fn validate_date_range(
        start_date: Option<chrono::NaiveDate>,
        end_date: Option<chrono::NaiveDate>,
        field_name: &str,
    ) -> AppResult<()> {
        if let (Some(start), Some(end)) = (start_date, end_date) {
            if start > end {
                return Err(AppError::validation(&format!(
                    "{} 的开始日期不能晚于结束日期",
                    field_name
                )));
            }
        }
        Ok(())
    }

    /// 验证URL格式
    pub fn validate_url(url: &str) -> AppResult<()> {
        url::Url::parse(url)
            .map_err(|_| AppError::validation("URL格式不正确"))?;
        Ok(())
    }

    /// 验证颜色十六进制格式
    pub fn validate_hex_color(color: &str) -> AppResult<()> {
        let hex_regex = Regex::new(r"^#[0-9A-Fa-f]{6}$")
            .map_err(|e| AppError::internal(&format!("Failed to compile hex color regex: {}", e)))?;
        
        if !hex_regex.is_match(color) {
            return Err(AppError::validation("颜色必须是有效的十六进制格式 (例如: #FF0000)"));
        }
        Ok(())
    }

    /// 清理字符串（去除首尾空格）
    pub fn sanitize_string(value: String) -> String {
        value.trim().to_string()
    }

    /// 清理可选字符串
    pub fn sanitize_optional_string(value: Option<String>) -> Option<String> {
        value.map(|s| {
            let trimmed = s.trim();
            if trimmed.is_empty() {
                None
            } else {
                Some(trimmed.to_string())
            }
        }).flatten()
    }

    /// 标准化邮箱（转为小写）
    pub fn normalize_email(email: String) -> String {
        email.trim().to_lowercase()
    }

    /// 验证业务规则
    pub fn validate_business_rule<F>(condition: F, message: &str) -> AppResult<()>
    where
        F: FnOnce() -> bool,
    {
        if !condition() {
            return Err(AppError::validation(message));
        }
        Ok(())
    }

    /// 批量验证
    pub fn validate_all(validations: Vec<AppResult<()>>) -> AppResult<()> {
        let mut errors = Vec::new();
        
        for validation in validations {
            if let Err(e) = validation {
                errors.push(e.to_string());
            }
        }

        if !errors.is_empty() {
            return Err(AppError::validation(&errors.join("; ")));
        }

        Ok(())
    }

    /// 验证唯一性约束
    pub async fn validate_uniqueness<F, Fut>(
        check_fn: F,
        field_name: &str,
        value: &str,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        let exists = check_fn().await?;
        if exists {
            return Err(AppError::validation(&format!("{} '{}' 已存在", field_name, value)));
        }
        Ok(())
    }

    /// 验证外键引用
    pub async fn validate_foreign_key<F, Fut>(
        check_fn: F,
        field_name: &str,
        value: &str,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        let exists = check_fn().await?;
        if !exists {
            return Err(AppError::validation(&format!("{} '{}' 不存在", field_name, value)));
        }
        Ok(())
    }

    /// 创建验证上下文
    pub fn create_validation_context() -> ValidationContext {
        ValidationContext::new()
    }
}

/// 验证上下文，用于收集多个验证错误
pub struct ValidationContext {
    errors: Vec<String>,
}

impl ValidationContext {
    pub fn new() -> Self {
        Self {
            errors: Vec::new(),
        }
    }

    /// 添加验证
    pub fn validate<F>(&mut self, validation: F, field_name: &str)
    where
        F: FnOnce() -> AppResult<()>,
    {
        if let Err(e) = validation() {
            self.errors.push(format!("{}: {}", field_name, e));
        }
    }

    /// 检查是否有错误
    pub fn has_errors(&self) -> bool {
        !self.errors.is_empty()
    }

    /// 获取所有错误
    pub fn get_errors(&self) -> &[String] {
        &self.errors
    }

    /// 完成验证，如果有错误则返回错误
    pub fn finish(self) -> AppResult<()> {
        if self.has_errors() {
            Err(AppError::validation(&self.errors.join("; ")))
        } else {
            Ok(())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_length() {
        assert!(ValidationUtils::validate_length("test", "field", 3, 10).is_ok());
        assert!(ValidationUtils::validate_length("ab", "field", 3, 10).is_err());
        assert!(ValidationUtils::validate_length("this is too long", "field", 3, 10).is_err());
    }

    #[test]
    fn test_validate_email() {
        assert!(ValidationUtils::validate_email("<EMAIL>").is_ok());
        assert!(ValidationUtils::validate_email("<EMAIL>").is_ok());
        assert!(ValidationUtils::validate_email("invalid-email").is_err());
        assert!(ValidationUtils::validate_email("@domain.com").is_err());
    }

    #[test]
    fn test_validate_username() {
        assert!(ValidationUtils::validate_username("user123").is_ok());
        assert!(ValidationUtils::validate_username("test_user").is_ok());
        assert!(ValidationUtils::validate_username("user-name").is_err());
        assert!(ValidationUtils::validate_username("user@name").is_err());
    }

    #[test]
    fn test_validate_hex_color() {
        assert!(ValidationUtils::validate_hex_color("#FF0000").is_ok());
        assert!(ValidationUtils::validate_hex_color("#123abc").is_ok());
        assert!(ValidationUtils::validate_hex_color("FF0000").is_err());
        assert!(ValidationUtils::validate_hex_color("#GG0000").is_err());
    }

    #[test]
    fn test_validation_context() {
        let mut ctx = ValidationContext::new();
        
        ctx.validate(|| ValidationUtils::validate_required("", "name"), "name");
        ctx.validate(|| ValidationUtils::validate_email("invalid"), "email");
        
        assert!(ctx.has_errors());
        assert_eq!(ctx.get_errors().len(), 2);
        assert!(ctx.finish().is_err());
    }
}
