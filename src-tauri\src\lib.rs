// PaoLife 应用库入口
// 基于领域驱动设计(DDD)的模块化架构

pub mod commands;
pub mod domain;
pub mod infrastructure;
pub mod application;
pub mod shared;

// 重新导出核心组件
pub use commands::*;
pub use shared::*;

// 应用程序初始化和运行
use infrastructure::config::{AppConfig, ConfigLoader};
use infrastructure::config::logging::LoggingManager;
use infrastructure::config::database::DatabaseManager;

/// 应用程序状态
pub struct AppState {
    pub config: AppConfig,
    pub database: DatabaseManager,
}

/// 初始化应用程序
async fn initialize_app() -> shared::errors::AppResult<AppState> {
    // 加载配置
    let config_path = ConfigLoader::get_default_config_path()?;
    let config = ConfigLoader::load_from_file(&config_path)?;

    // 初始化日志系统
    let logging_manager = LoggingManager::new(config.logging.clone());
    logging_manager.initialize()?;

    tracing::info!("Starting PaoLife application");
    tracing::debug!("Configuration loaded from: {:?}", config_path);

    // 初始化数据库
    let mut database_manager = DatabaseManager::new(config.database.clone());
    database_manager.initialize().await?;

    Ok(AppState {
        config,
        database: database_manager,
    })
}

// 临时保留的测试命令
#[tauri::command]
fn greet(name: &str) -> String {
    format!("Hello, {}! You've been greeted from Rust!", name)
}

// 健康检查命令
#[tauri::command]
async fn health_check(state: tauri::State<'_, AppState>) -> Result<String, String> {
    match state.database.health_check().await {
        Ok(status) => {
            if status.is_healthy {
                Ok(format!("Healthy - Response time: {}ms", status.response_time_ms))
            } else {
                Err(status.error_message.unwrap_or_else(|| "Unknown error".to_string()))
            }
        }
        Err(e) => Err(e.to_string()),
    }
}

#[cfg_attr(mobile, tauri::mobile_entry_point)]
pub fn run() {
    // 使用tokio运行时来处理异步初始化
    let rt = tokio::runtime::Runtime::new().expect("Failed to create tokio runtime");

    let app_state = rt.block_on(async {
        initialize_app().await.expect("Failed to initialize application")
    });

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_window_state::Builder::default().build())
        .plugin(tauri_plugin_store::Builder::default().build())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_shell::init())
        .manage(app_state)
        .invoke_handler(tauri::generate_handler![greet, health_check])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
