// 设置页面
// 用户个人设置和系统配置

import { Component, createSignal, Show } from 'solid-js';
import { useAuth } from '../../contexts/AuthContext';
import { MainLayout } from '../../components/layout/MainLayout';
import { Button } from '../../components/ui/Button';
import { Input, PasswordInput, Select, Checkbox } from '../../components/ui/Input';
import { api } from '../../utils/api';
import toast from 'solid-toast';

interface UserSettings {
  full_name: string;
  email: string;
  timezone: string;
  language: string;
  avatar_url?: string;
  notifications: {
    email_enabled: boolean;
    push_enabled: boolean;
    deadline_reminders: boolean;
    daily_summary: boolean;
    weekly_report: boolean;
  };
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    default_view: 'list' | 'kanban' | 'calendar';
    items_per_page: number;
    auto_save: boolean;
    compact_mode: boolean;
  };
}

export const SettingsPage: Component = () => {
  const { user, updateUser } = useAuth();
  
  const [activeTab, setActiveTab] = createSignal<'profile' | 'notifications' | 'preferences' | 'security'>('profile');
  const [loading, setLoading] = createSignal(false);
  const [settings, setSettings] = createSignal<UserSettings>({
    full_name: user()?.full_name || '',
    email: user()?.email || '',
    timezone: user()?.timezone || 'Asia/Shanghai',
    language: user()?.language || 'zh-CN',
    avatar_url: user()?.avatar_url,
    notifications: {
      email_enabled: true,
      push_enabled: true,
      deadline_reminders: true,
      daily_summary: false,
      weekly_report: true,
    },
    preferences: {
      theme: 'light',
      default_view: 'list',
      items_per_page: 20,
      auto_save: true,
      compact_mode: false,
    },
  });

  // 密码修改表单
  const [passwordForm, setPasswordForm] = createSignal({
    current_password: '',
    new_password: '',
    confirm_password: '',
  });

  // 保存个人资料
  const saveProfile = async () => {
    try {
      setLoading(true);
      
      const profileData = {
        full_name: settings().full_name,
        email: settings().email,
        timezone: settings().timezone,
        language: settings().language,
        avatar_url: settings().avatar_url,
      };

      await api.call('update_user_profile', { profile: profileData });
      await updateUser();
      toast.success('个人资料已更新');
    } catch (error) {
      toast.error('更新个人资料失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存通知设置
  const saveNotifications = async () => {
    try {
      setLoading(true);
      
      await api.call('update_notification_settings', { 
        notifications: settings().notifications 
      });
      
      toast.success('通知设置已更新');
    } catch (error) {
      toast.error('更新通知设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存偏好设置
  const savePreferences = async () => {
    try {
      setLoading(true);
      
      await api.call('update_user_preferences', { 
        preferences: settings().preferences 
      });
      
      toast.success('偏好设置已更新');
    } catch (error) {
      toast.error('更新偏好设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 修改密码
  const changePassword = async () => {
    const form = passwordForm();
    
    if (!form.current_password || !form.new_password || !form.confirm_password) {
      toast.error('请填写所有密码字段');
      return;
    }

    if (form.new_password !== form.confirm_password) {
      toast.error('新密码和确认密码不匹配');
      return;
    }

    if (form.new_password.length < 8) {
      toast.error('新密码至少需要8个字符');
      return;
    }

    try {
      setLoading(true);
      
      await api.changePassword(form.current_password, form.new_password);
      
      setPasswordForm({
        current_password: '',
        new_password: '',
        confirm_password: '',
      });
      
      toast.success('密码已更新');
    } catch (error) {
      toast.error('修改密码失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新设置
  const updateSettings = (section: keyof UserSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const tabs = [
    { id: 'profile', label: '个人资料', icon: '👤' },
    { id: 'notifications', label: '通知设置', icon: '🔔' },
    { id: 'preferences', label: '偏好设置', icon: '⚙️' },
    { id: 'security', label: '安全设置', icon: '🔒' },
  ];

  return (
    <MainLayout title="设置">
      <div class="max-w-4xl mx-auto">
        <div class="bg-white shadow rounded-lg">
          {/* 标签页导航 */}
          <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  type="button"
                  class={`py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab() === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab(tab.id as any)}
                >
                  <span class="mr-2">{tab.icon}</span>
                  {tab.label}
                </button>
              ))}
            </nav>
          </div>

          <div class="p-6">
            {/* 个人资料 */}
            <Show when={activeTab() === 'profile'}>
              <div class="space-y-6">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">个人资料</h3>
                  <p class="mt-1 text-sm text-gray-500">
                    更新您的个人信息和头像
                  </p>
                </div>

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <Input
                    label="全名"
                    value={settings().full_name}
                    onInput={(e) => updateSettings('full_name', '', e.currentTarget.value)}
                    placeholder="请输入您的全名"
                  />

                  <Input
                    label="邮箱地址"
                    type="email"
                    value={settings().email}
                    onInput={(e) => updateSettings('email', '', e.currentTarget.value)}
                    placeholder="请输入邮箱地址"
                  />

                  <Select
                    label="时区"
                    value={settings().timezone}
                    onChange={(e) => updateSettings('timezone', '', e.currentTarget.value)}
                    options={[
                      { value: 'Asia/Shanghai', label: '中国标准时间 (UTC+8)' },
                      { value: 'Asia/Tokyo', label: '日本标准时间 (UTC+9)' },
                      { value: 'America/New_York', label: '美国东部时间 (UTC-5)' },
                      { value: 'America/Los_Angeles', label: '美国西部时间 (UTC-8)' },
                      { value: 'Europe/London', label: '英国时间 (UTC+0)' },
                    ]}
                  />

                  <Select
                    label="语言"
                    value={settings().language}
                    onChange={(e) => updateSettings('language', '', e.currentTarget.value)}
                    options={[
                      { value: 'zh-CN', label: '简体中文' },
                      { value: 'zh-TW', label: '繁体中文' },
                      { value: 'en-US', label: 'English' },
                      { value: 'ja-JP', label: '日本語' },
                    ]}
                  />
                </div>

                <div>
                  <Input
                    label="头像URL"
                    value={settings().avatar_url || ''}
                    onInput={(e) => updateSettings('avatar_url', '', e.currentTarget.value)}
                    placeholder="请输入头像图片URL"
                    helperText="支持 JPG、PNG 格式，建议尺寸 200x200 像素"
                  />
                </div>

                <div class="flex justify-end">
                  <Button
                    onClick={saveProfile}
                    loading={loading()}
                    disabled={loading()}
                  >
                    保存更改
                  </Button>
                </div>
              </div>
            </Show>

            {/* 通知设置 */}
            <Show when={activeTab() === 'notifications'}>
              <div class="space-y-6">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">通知设置</h3>
                  <p class="mt-1 text-sm text-gray-500">
                    管理您希望接收的通知类型
                  </p>
                </div>

                <div class="space-y-4">
                  <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">通知渠道</h4>
                    <div class="space-y-3">
                      <Checkbox
                        label="邮件通知"
                        description="通过邮件接收重要通知"
                        checked={settings().notifications.email_enabled}
                        onChange={(e) => updateSettings('notifications', 'email_enabled', e.currentTarget.checked)}
                      />
                      <Checkbox
                        label="推送通知"
                        description="在浏览器中接收推送通知"
                        checked={settings().notifications.push_enabled}
                        onChange={(e) => updateSettings('notifications', 'push_enabled', e.currentTarget.checked)}
                      />
                    </div>
                  </div>

                  <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">通知内容</h4>
                    <div class="space-y-3">
                      <Checkbox
                        label="截止日期提醒"
                        description="在任务或项目即将到期时提醒我"
                        checked={settings().notifications.deadline_reminders}
                        onChange={(e) => updateSettings('notifications', 'deadline_reminders', e.currentTarget.checked)}
                      />
                      <Checkbox
                        label="每日总结"
                        description="每天发送工作总结和明日计划"
                        checked={settings().notifications.daily_summary}
                        onChange={(e) => updateSettings('notifications', 'daily_summary', e.currentTarget.checked)}
                      />
                      <Checkbox
                        label="周报"
                        description="每周发送工作进展报告"
                        checked={settings().notifications.weekly_report}
                        onChange={(e) => updateSettings('notifications', 'weekly_report', e.currentTarget.checked)}
                      />
                    </div>
                  </div>
                </div>

                <div class="flex justify-end">
                  <Button
                    onClick={saveNotifications}
                    loading={loading()}
                    disabled={loading()}
                  >
                    保存设置
                  </Button>
                </div>
              </div>
            </Show>

            {/* 偏好设置 */}
            <Show when={activeTab() === 'preferences'}>
              <div class="space-y-6">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">偏好设置</h3>
                  <p class="mt-1 text-sm text-gray-500">
                    自定义您的使用体验
                  </p>
                </div>

                <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                  <Select
                    label="主题"
                    value={settings().preferences.theme}
                    onChange={(e) => updateSettings('preferences', 'theme', e.currentTarget.value)}
                    options={[
                      { value: 'light', label: '浅色主题' },
                      { value: 'dark', label: '深色主题' },
                      { value: 'auto', label: '跟随系统' },
                    ]}
                  />

                  <Select
                    label="默认视图"
                    value={settings().preferences.default_view}
                    onChange={(e) => updateSettings('preferences', 'default_view', e.currentTarget.value)}
                    options={[
                      { value: 'list', label: '列表视图' },
                      { value: 'kanban', label: '看板视图' },
                      { value: 'calendar', label: '日历视图' },
                    ]}
                  />

                  <Select
                    label="每页显示项目数"
                    value={settings().preferences.items_per_page.toString()}
                    onChange={(e) => updateSettings('preferences', 'items_per_page', parseInt(e.currentTarget.value))}
                    options={[
                      { value: '10', label: '10 项' },
                      { value: '20', label: '20 项' },
                      { value: '50', label: '50 项' },
                      { value: '100', label: '100 项' },
                    ]}
                  />
                </div>

                <div class="space-y-4">
                  <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-900 mb-3">界面选项</h4>
                    <div class="space-y-3">
                      <Checkbox
                        label="自动保存"
                        description="编辑时自动保存更改"
                        checked={settings().preferences.auto_save}
                        onChange={(e) => updateSettings('preferences', 'auto_save', e.currentTarget.checked)}
                      />
                      <Checkbox
                        label="紧凑模式"
                        description="使用更紧凑的界面布局"
                        checked={settings().preferences.compact_mode}
                        onChange={(e) => updateSettings('preferences', 'compact_mode', e.currentTarget.checked)}
                      />
                    </div>
                  </div>
                </div>

                <div class="flex justify-end">
                  <Button
                    onClick={savePreferences}
                    loading={loading()}
                    disabled={loading()}
                  >
                    保存设置
                  </Button>
                </div>
              </div>
            </Show>

            {/* 安全设置 */}
            <Show when={activeTab() === 'security'}>
              <div class="space-y-6">
                <div>
                  <h3 class="text-lg font-medium text-gray-900">安全设置</h3>
                  <p class="mt-1 text-sm text-gray-500">
                    管理您的账户安全
                  </p>
                </div>

                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="text-sm font-medium text-gray-900 mb-4">修改密码</h4>
                  <div class="space-y-4">
                    <PasswordInput
                      label="当前密码"
                      value={passwordForm().current_password}
                      onInput={(e) => setPasswordForm(prev => ({ ...prev, current_password: e.currentTarget.value }))}
                      placeholder="请输入当前密码"
                    />

                    <PasswordInput
                      label="新密码"
                      value={passwordForm().new_password}
                      onInput={(e) => setPasswordForm(prev => ({ ...prev, new_password: e.currentTarget.value }))}
                      placeholder="请输入新密码"
                      helperText="密码至少需要8个字符"
                    />

                    <PasswordInput
                      label="确认新密码"
                      value={passwordForm().confirm_password}
                      onInput={(e) => setPasswordForm(prev => ({ ...prev, confirm_password: e.currentTarget.value }))}
                      placeholder="请再次输入新密码"
                    />

                    <div class="flex justify-end">
                      <Button
                        onClick={changePassword}
                        loading={loading()}
                        disabled={loading()}
                      >
                        更新密码
                      </Button>
                    </div>
                  </div>
                </div>

                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-yellow-800">
                        安全提示
                      </h3>
                      <div class="mt-2 text-sm text-yellow-700">
                        <ul class="list-disc list-inside space-y-1">
                          <li>定期更换密码以保护账户安全</li>
                          <li>不要在公共设备上保存登录状态</li>
                          <li>如发现异常活动，请立即修改密码</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div class="flex">
                    <div class="flex-shrink-0">
                      <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                      </svg>
                    </div>
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-red-800">
                        危险操作
                      </h3>
                      <div class="mt-2 text-sm text-red-700">
                        <p>删除账户将永久删除您的所有数据，此操作不可撤销。</p>
                      </div>
                      <div class="mt-4">
                        <Button
                          variant="danger"
                          size="sm"
                          onClick={() => {
                            if (confirm('确定要删除账户吗？此操作不可撤销！')) {
                              toast.error('账户删除功能暂未开放');
                            }
                          }}
                        >
                          删除账户
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Show>
          </div>
        </div>
      </div>
    </MainLayout>
  );
};

export default SettingsPage;
