// 项目仓储接口
// 定义项目数据访问的抽象接口

use crate::domain::entities::project::{Project, CreateProjectData, UpdateProjectData, ProjectQuery, ProjectStats};
use crate::shared::types::{EntityId, QueryParams, ProjectStatus, Priority};
use crate::shared::errors::AppResult;
use async_trait::async_trait;
use chrono::NaiveDate;

/// 项目仓储接口
#[async_trait]
pub trait ProjectRepository: Send + Sync {
    /// 创建项目
    async fn create(&self, data: CreateProjectData) -> AppResult<Project>;

    /// 根据ID查找项目
    async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<Project>>;

    /// 更新项目
    async fn update(&self, id: &EntityId, data: UpdateProjectData) -> AppResult<Project>;

    /// 删除项目
    async fn delete(&self, id: &EntityId) -> AppResult<()>;

    /// 查询项目列表
    async fn find_all(&self, query: ProjectQuery, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 统计项目数量
    async fn count(&self, query: ProjectQuery) -> AppResult<u64>;

    /// 根据领域ID查找项目
    async fn find_by_area_id(&self, area_id: &EntityId, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 根据创建者查找项目
    async fn find_by_creator(&self, creator_id: &EntityId, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 根据状态查找项目
    async fn find_by_status(&self, status: ProjectStatus, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 根据优先级查找项目
    async fn find_by_priority(&self, priority: Priority, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 查找逾期项目
    async fn find_overdue(&self, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 查找即将到期的项目
    async fn find_due_soon(&self, days: i32, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 查找活跃项目
    async fn find_active(&self, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 查找已完成项目
    async fn find_completed(&self, params: QueryParams) -> AppResult<Vec<Project>>;

    /// 更新项目状态
    async fn update_status(&self, id: &EntityId, status: ProjectStatus) -> AppResult<()>;

    /// 更新项目进度
    async fn update_progress(&self, id: &EntityId, progress: u8) -> AppResult<()>;

    /// 添加工作时间
    async fn add_work_hours(&self, id: &EntityId, hours: u32) -> AppResult<()>;

    /// 归档项目
    async fn archive(&self, id: &EntityId) -> AppResult<()>;

    /// 取消归档项目
    async fn unarchive(&self, id: &EntityId) -> AppResult<()>;

    /// 获取项目统计信息
    async fn get_stats(&self, id: &EntityId) -> AppResult<ProjectStats>;

    /// 获取用户的项目统计
    async fn get_user_project_stats(&self, user_id: &EntityId) -> AppResult<UserProjectStats>;

    /// 获取领域的项目统计
    async fn get_area_project_stats(&self, area_id: &EntityId) -> AppResult<AreaProjectStats>;

    /// 批量更新项目状态
    async fn batch_update_status(&self, ids: Vec<EntityId>, status: ProjectStatus) -> AppResult<u64>;

    /// 搜索项目
    async fn search(&self, keyword: &str, params: QueryParams) -> AppResult<Vec<Project>>;
}

/// 用户项目统计
#[derive(Debug, Clone)]
pub struct UserProjectStats {
    pub total_projects: u64,
    pub active_projects: u64,
    pub completed_projects: u64,
    pub overdue_projects: u64,
    pub completion_rate: f64,
    pub average_progress: f64,
}

/// 领域项目统计
#[derive(Debug, Clone)]
pub struct AreaProjectStats {
    pub total_projects: u64,
    pub active_projects: u64,
    pub completed_projects: u64,
    pub overdue_projects: u64,
    pub completion_rate: f64,
    pub average_progress: f64,
}
