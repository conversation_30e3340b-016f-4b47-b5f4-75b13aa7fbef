-- PaoLife 数据库初始化脚本
-- 基于15表精简架构的数据库设计
-- 创建时间: 2025-01-XX

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 1. 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT NOT NULL UNIQUE,
    email TEXT UNIQUE,
    password_hash TEXT NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    timezone TEXT DEFAULT 'UTC',
    language TEXT DEFAULT 'zh-CN',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP
);

-- 2. 应用配置表
CREATE TABLE app_settings (
    setting_key TEXT PRIMARY KEY,
    setting_value TEXT NOT NULL,
    setting_type TEXT NOT NULL DEFAULT 'string'
        CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    description TEXT,
    is_user_configurable BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 3. 用户设置表
CREATE TABLE user_settings (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    setting_key TEXT NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type TEXT NOT NULL DEFAULT 'string'
        CHECK (setting_type IN ('string', 'number', 'boolean', 'json')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, setting_key)
);

-- 4. 领域表
CREATE TABLE areas (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    standard TEXT,
    icon_name TEXT,
    color_hex TEXT CHECK (color_hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    archived_at TIMESTAMP
);

-- 5. 项目表
CREATE TABLE projects (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'not_started' 
        CHECK (status IN ('not_started', 'in_progress', 'at_risk', 'paused', 'completed', 'archived')),
    progress INTEGER NOT NULL DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    start_date DATE,
    deadline DATE,
    estimated_hours INTEGER,
    actual_hours INTEGER DEFAULT 0,
    area_id TEXT REFERENCES areas(id) ON DELETE SET NULL,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    archived_at TIMESTAMP
);

-- 6. 任务表
CREATE TABLE tasks (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    status TEXT NOT NULL DEFAULT 'todo'
        CHECK (status IN ('todo', 'in_progress', 'waiting', 'completed', 'cancelled')),
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    parent_task_id TEXT REFERENCES tasks(id) ON DELETE CASCADE,
    project_id TEXT REFERENCES projects(id) ON DELETE SET NULL,
    area_id TEXT REFERENCES areas(id) ON DELETE SET NULL,
    assigned_to TEXT REFERENCES users(id) ON DELETE SET NULL,
    due_date DATE,
    estimated_minutes INTEGER,
    actual_minutes INTEGER DEFAULT 0,
    completion_percentage INTEGER DEFAULT 0 CHECK (completion_percentage >= 0 AND completion_percentage <= 100),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP,

    -- 确保任务不能自己作为父任务
    CHECK (id != parent_task_id),
    -- 确保任务至少关联到项目或领域之一
    CHECK (project_id IS NOT NULL OR area_id IS NOT NULL)
);

-- 7. 统一指标表 (替代ProjectKPI和AreaMetric)
CREATE TABLE metrics (
    id TEXT PRIMARY KEY,
    owner_type TEXT NOT NULL CHECK (owner_type IN ('project', 'area')),
    owner_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    current_value DECIMAL(15,4) NOT NULL DEFAULT 0,
    target_value DECIMAL(15,4),
    unit TEXT,
    direction TEXT NOT NULL DEFAULT 'increase'
        CHECK (direction IN ('increase', 'decrease', 'maintain')),
    frequency TEXT NOT NULL DEFAULT 'daily'
        CHECK (frequency IN ('daily', 'weekly', 'monthly', 'quarterly')),
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 8. 指标记录表
CREATE TABLE metric_records (
    id TEXT PRIMARY KEY,
    metric_id TEXT NOT NULL REFERENCES metrics(id) ON DELETE CASCADE,
    recorded_value DECIMAL(15,4) NOT NULL,
    note TEXT,
    source TEXT DEFAULT 'manual'
        CHECK (source IN ('manual', 'habit', 'import', 'calculated')),
    confidence_score DECIMAL(3,2) CHECK (confidence_score >= 0 AND confidence_score <= 1),
    recorded_date DATE NOT NULL,
    recorded_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 9. 习惯表
CREATE TABLE habits (
    id TEXT PRIMARY KEY,
    area_id TEXT NOT NULL REFERENCES areas(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    target_frequency INTEGER NOT NULL DEFAULT 1,
    frequency_unit TEXT NOT NULL DEFAULT 'daily'
        CHECK (frequency_unit IN ('daily', 'weekly', 'monthly')),
    difficulty_level INTEGER DEFAULT 3 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    reward_points INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 10. 习惯记录表
CREATE TABLE habit_records (
    id TEXT PRIMARY KEY,
    habit_id TEXT NOT NULL REFERENCES habits(id) ON DELETE CASCADE,
    completed_date DATE NOT NULL,
    completion_value INTEGER DEFAULT 1,
    note TEXT,
    mood_rating INTEGER CHECK (mood_rating >= 1 AND mood_rating <= 5),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(habit_id, completed_date)
);

-- 11. 资源中央仓库表
CREATE TABLE resources (
    id TEXT PRIMARY KEY,
    resource_type TEXT NOT NULL DEFAULT 'file'
        CHECK (resource_type IN ('file', 'folder', 'url', 'note', 'reference')),
    path TEXT NOT NULL UNIQUE,
    title TEXT,
    mime_type TEXT,
    file_size INTEGER,
    file_hash TEXT,
    metadata TEXT, -- JSON格式存储扩展信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_accessed_at TIMESTAMP
);

-- 12. 统一引用/关联表
CREATE TABLE references (
    id TEXT PRIMARY KEY,
    source_entity_type TEXT NOT NULL
        CHECK (source_entity_type IN ('project', 'area', 'task', 'resource', 'review')),
    source_entity_id TEXT NOT NULL,
    target_resource_id TEXT NOT NULL REFERENCES resources(id) ON DELETE CASCADE,
    reference_type TEXT NOT NULL DEFAULT 'attachment'
        CHECK (reference_type IN ('attachment', 'wikilink', 'embed', 'citation', 'dependency')),
    context TEXT,
    display_text TEXT,
    line_number INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    -- 防止重复引用
    UNIQUE(source_entity_type, source_entity_id, target_resource_id, reference_type, line_number)
);

-- 13. 标签表
CREATE TABLE tags (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL UNIQUE,
    color_hex TEXT CHECK (color_hex GLOB '#[0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f][0-9A-Fa-f]'),
    description TEXT,
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 14. 通用标签关联表
CREATE TABLE taggables (
    tag_id TEXT NOT NULL REFERENCES tags(id) ON DELETE CASCADE,
    taggable_type TEXT NOT NULL
        CHECK (taggable_type IN ('project', 'area', 'task', 'resource', 'review', 'habit')),
    taggable_id TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    PRIMARY KEY (tag_id, taggable_type, taggable_id)
);

-- 15. 收件箱项目表
CREATE TABLE inbox_items (
    id TEXT PRIMARY KEY,
    content TEXT NOT NULL,
    item_type TEXT DEFAULT 'quick_note'
        CHECK (item_type IN ('quick_note', 'idea', 'task', 'reference', 'meeting_note')),
    source TEXT DEFAULT 'manual'
        CHECK (source IN ('manual', 'email', 'web_clipper', 'voice', 'api')),
    processing_status TEXT DEFAULT 'unprocessed'
        CHECK (processing_status IN ('unprocessed', 'processing', 'processed', 'archived')),
    processed_into_type TEXT,
    processed_into_id TEXT,
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    created_by TEXT NOT NULL REFERENCES users(id),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed_at TIMESTAMP,
    processed_by TEXT REFERENCES users(id)
);

-- ================================
-- 索引定义
-- ================================

-- 用户表索引
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_is_active ON users(is_active);
CREATE INDEX idx_users_created_at ON users(created_at);

-- 应用配置表索引
CREATE INDEX idx_app_settings_type ON app_settings(setting_type);

-- 用户设置表索引
CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX idx_user_settings_key ON user_settings(setting_key);

-- 领域表索引
CREATE INDEX idx_areas_created_by ON areas(created_by);
CREATE INDEX idx_areas_is_active ON areas(is_active);
CREATE INDEX idx_areas_sort_order ON areas(sort_order);

-- 项目表索引
CREATE INDEX idx_projects_area_id ON projects(area_id);
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_priority ON projects(priority);
CREATE INDEX idx_projects_deadline ON projects(deadline);
CREATE INDEX idx_projects_created_at ON projects(created_at);

-- 任务表索引
CREATE INDEX idx_tasks_parent_task_id ON tasks(parent_task_id);
CREATE INDEX idx_tasks_project_id ON tasks(project_id);
CREATE INDEX idx_tasks_area_id ON tasks(area_id);
CREATE INDEX idx_tasks_assigned_to ON tasks(assigned_to);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_tasks_sort_order ON tasks(sort_order);

-- 指标表索引
CREATE INDEX idx_metrics_owner ON metrics(owner_type, owner_id);
CREATE INDEX idx_metrics_is_active ON metrics(is_active);
CREATE INDEX idx_metrics_frequency ON metrics(frequency);

-- 指标记录表索引
CREATE INDEX idx_metric_records_metric_id ON metric_records(metric_id);
CREATE INDEX idx_metric_records_date ON metric_records(recorded_date);
CREATE INDEX idx_metric_records_recorded_by ON metric_records(recorded_by);
CREATE INDEX idx_metric_records_source ON metric_records(source);

-- 习惯表索引
CREATE INDEX idx_habits_area_id ON habits(area_id);
CREATE INDEX idx_habits_is_active ON habits(is_active);
CREATE INDEX idx_habits_frequency ON habits(frequency_unit);

-- 习惯记录表索引
CREATE INDEX idx_habit_records_habit_id ON habit_records(habit_id);
CREATE INDEX idx_habit_records_date ON habit_records(completed_date);

-- 资源表索引
CREATE INDEX idx_resources_type ON resources(resource_type);
CREATE INDEX idx_resources_path ON resources(path);
CREATE INDEX idx_resources_mime_type ON resources(mime_type);
CREATE INDEX idx_resources_created_at ON resources(created_at);
CREATE INDEX idx_resources_last_accessed ON resources(last_accessed_at);

-- 引用表索引
CREATE INDEX idx_references_source ON references(source_entity_type, source_entity_id);
CREATE INDEX idx_references_target ON references(target_resource_id);
CREATE INDEX idx_references_type ON references(reference_type);

-- 标签表索引
CREATE INDEX idx_tags_name ON tags(name);
CREATE INDEX idx_tags_created_by ON tags(created_by);

-- 标签关联表索引
CREATE INDEX idx_taggables_entity ON taggables(taggable_type, taggable_id);
CREATE INDEX idx_taggables_tag ON taggables(tag_id);

-- 收件箱表索引
CREATE INDEX idx_inbox_items_created_by ON inbox_items(created_by);
CREATE INDEX idx_inbox_items_status ON inbox_items(processing_status);
CREATE INDEX idx_inbox_items_type ON inbox_items(item_type);
CREATE INDEX idx_inbox_items_priority ON inbox_items(priority);
CREATE INDEX idx_inbox_items_created_at ON inbox_items(created_at);
CREATE INDEX idx_inbox_items_processed_into ON inbox_items(processed_into_type, processed_into_id);

-- ================================
-- 触发器定义
-- ================================

-- 自动更新 updated_at 字段的触发器
CREATE TRIGGER trigger_users_updated_at
    AFTER UPDATE ON users
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_app_settings_updated_at
    AFTER UPDATE ON app_settings
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE app_settings SET updated_at = CURRENT_TIMESTAMP WHERE setting_key = NEW.setting_key;
END;

CREATE TRIGGER trigger_user_settings_updated_at
    AFTER UPDATE ON user_settings
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE user_settings SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_areas_updated_at
    AFTER UPDATE ON areas
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE areas SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_projects_updated_at
    AFTER UPDATE ON projects
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE projects SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_tasks_updated_at
    AFTER UPDATE ON tasks
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE tasks SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 任务完成时自动设置 completed_at
CREATE TRIGGER trigger_tasks_completed_at
    AFTER UPDATE ON tasks
    FOR EACH ROW
    WHEN NEW.status = 'completed' AND OLD.status != 'completed'
BEGIN
    UPDATE tasks SET completed_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 任务状态变更时清除 completed_at
CREATE TRIGGER trigger_tasks_uncompleted_at
    AFTER UPDATE ON tasks
    FOR EACH ROW
    WHEN NEW.status != 'completed' AND OLD.status = 'completed'
BEGIN
    UPDATE tasks SET completed_at = NULL WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_metrics_updated_at
    AFTER UPDATE ON metrics
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE metrics SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_habits_updated_at
    AFTER UPDATE ON habits
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE habits SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

CREATE TRIGGER trigger_resources_updated_at
    AFTER UPDATE ON resources
    FOR EACH ROW
    WHEN NEW.updated_at = OLD.updated_at
BEGIN
    UPDATE resources SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- 资源访问时自动更新 last_accessed_at
CREATE TRIGGER trigger_resources_last_accessed
    AFTER UPDATE ON resources
    FOR EACH ROW
    WHEN NEW.last_accessed_at IS NULL OR NEW.last_accessed_at = OLD.last_accessed_at
BEGIN
    UPDATE resources SET last_accessed_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;

-- ================================
-- 视图定义
-- ================================

-- 项目统计视图
CREATE VIEW project_stats AS
SELECT
    p.id,
    p.name,
    p.status,
    p.progress,
    COUNT(t.id) as total_tasks,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
    COUNT(CASE WHEN t.status IN ('todo', 'in_progress', 'waiting') THEN 1 END) as active_tasks,
    CASE
        WHEN COUNT(t.id) > 0 THEN
            ROUND(COUNT(CASE WHEN t.status = 'completed' THEN 1 END) * 100.0 / COUNT(t.id), 2)
        ELSE 0
    END as completion_percentage,
    p.created_at,
    p.deadline
FROM projects p
LEFT JOIN tasks t ON p.id = t.project_id
GROUP BY p.id, p.name, p.status, p.progress, p.created_at, p.deadline;

-- 领域统计视图
CREATE VIEW area_stats AS
SELECT
    a.id,
    a.name,
    COUNT(DISTINCT p.id) as total_projects,
    COUNT(DISTINCT t.id) as total_tasks,
    COUNT(DISTINCT h.id) as total_habits,
    COUNT(CASE WHEN p.status = 'completed' THEN 1 END) as completed_projects,
    COUNT(CASE WHEN t.status = 'completed' THEN 1 END) as completed_tasks,
    a.created_at
FROM areas a
LEFT JOIN projects p ON a.id = p.area_id
LEFT JOIN tasks t ON a.id = t.area_id
LEFT JOIN habits h ON a.id = h.area_id
GROUP BY a.id, a.name, a.created_at;

-- 任务层级视图
CREATE VIEW task_hierarchy AS
WITH RECURSIVE task_tree AS (
    -- 根任务
    SELECT
        id,
        title,
        parent_task_id,
        project_id,
        area_id,
        status,
        priority,
        0 as level,
        id as root_id,
        CAST(sort_order AS TEXT) as path
    FROM tasks
    WHERE parent_task_id IS NULL

    UNION ALL

    -- 子任务
    SELECT
        t.id,
        t.title,
        t.parent_task_id,
        t.project_id,
        t.area_id,
        t.status,
        t.priority,
        tt.level + 1,
        tt.root_id,
        tt.path || '.' || CAST(t.sort_order AS TEXT)
    FROM tasks t
    JOIN task_tree tt ON t.parent_task_id = tt.id
)
SELECT * FROM task_tree ORDER BY path;

-- ================================
-- 初始数据插入
-- ================================

-- 插入默认应用配置
INSERT INTO app_settings (setting_key, setting_value, setting_type, description, is_user_configurable) VALUES
('app_version', '1.0.0', 'string', '应用版本号', FALSE),
('database_version', '1', 'number', '数据库版本', FALSE),
('default_language', 'zh-CN', 'string', '默认语言', TRUE),
('default_timezone', 'Asia/Shanghai', 'string', '默认时区', TRUE),
('backup_enabled', 'true', 'boolean', '是否启用自动备份', TRUE),
('backup_interval_hours', '24', 'number', '备份间隔（小时）', TRUE),
('max_backup_files', '7', 'number', '最大备份文件数', TRUE),
('theme_mode', 'system', 'string', '主题模式：light/dark/system', TRUE),
('sidebar_collapsed', 'false', 'boolean', '侧边栏是否折叠', TRUE),
('show_completed_tasks', 'false', 'boolean', '是否显示已完成任务', TRUE),
('task_auto_archive_days', '30', 'number', '任务自动归档天数', TRUE),
('habit_streak_reset_hour', '6', 'number', '习惯连击重置时间（小时）', TRUE);

-- 插入默认标签
INSERT INTO tags (id, name, color_hex, description, created_by) VALUES
('tag_urgent', '紧急', '#FF4444', '紧急事项标签', 'system'),
('tag_important', '重要', '#FF8800', '重要事项标签', 'system'),
('tag_learning', '学习', '#4CAF50', '学习相关标签', 'system'),
('tag_work', '工作', '#2196F3', '工作相关标签', 'system'),
('tag_personal', '个人', '#9C27B0', '个人事务标签', 'system'),
('tag_health', '健康', '#00BCD4', '健康相关标签', 'system'),
('tag_finance', '财务', '#FFC107', '财务相关标签', 'system'),
('tag_family', '家庭', '#E91E63', '家庭相关标签', 'system');

-- 创建系统用户（用于默认数据）
INSERT INTO users (id, username, email, password_hash, full_name, is_active) VALUES
('system', 'system', '<EMAIL>', 'system', '系统用户', TRUE);
