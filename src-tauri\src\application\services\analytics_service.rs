// 数据统计和分析服务
// 提供项目、任务、领域等数据的统计分析功能

use crate::domain::repositories::{
    project_repository::ProjectRepository,
    task_repository::TaskRepository,
    area_repository::AreaRepository,
    user_repository::UserRepository,
};
use crate::shared::errors::{AppError, AppResult};
use crate::shared::types::{EntityId, ProjectStatus, TaskStatus};
use async_trait::async_trait;
use std::sync::Arc;
use std::collections::HashMap;
use chrono::{DateTime, Utc, NaiveDate, Duration};
use serde::{Deserialize, Serialize};

/// 用户仪表板数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DashboardData {
    pub user_stats: UserStats,
    pub project_stats: ProjectStats,
    pub task_stats: TaskStats,
    pub area_stats: AreaStats,
    pub recent_activities: Vec<RecentActivity>,
    pub productivity_trends: ProductivityTrends,
    pub upcoming_deadlines: Vec<UpcomingDeadline>,
}

/// 用户统计
#[derive(Debu<PERSON>, <PERSON><PERSON>, <PERSON>ialize, Deserialize)]
pub struct UserStats {
    pub total_projects: u64,
    pub active_projects: u64,
    pub completed_projects: u64,
    pub total_tasks: u64,
    pub completed_tasks: u64,
    pub completion_rate: f64,
    pub total_areas: u64,
    pub active_areas: u64,
}

/// 项目统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProjectStats {
    pub by_status: HashMap<String, u64>,
    pub by_priority: HashMap<String, u64>,
    pub by_area: HashMap<String, u64>,
    pub average_progress: f64,
    pub overdue_count: u64,
    pub completion_rate: f64,
    pub total_estimated_hours: u64,
    pub total_actual_hours: u64,
}

/// 任务统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TaskStats {
    pub by_status: HashMap<String, u64>,
    pub by_priority: HashMap<String, u64>,
    pub by_project: HashMap<String, u64>,
    pub average_completion: f64,
    pub overdue_count: u64,
    pub completion_rate: f64,
    pub total_estimated_minutes: u64,
    pub total_actual_minutes: u64,
}

/// 领域统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AreaStats {
    pub total_areas: u64,
    pub active_areas: u64,
    pub areas_with_projects: u64,
    pub areas_with_tasks: u64,
    pub most_active_area: Option<String>,
    pub least_active_area: Option<String>,
}

/// 最近活动
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RecentActivity {
    pub id: String,
    pub activity_type: ActivityType,
    pub title: String,
    pub description: String,
    pub timestamp: DateTime<Utc>,
    pub entity_id: String,
    pub entity_type: String,
}

/// 活动类型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ActivityType {
    ProjectCreated,
    ProjectCompleted,
    ProjectUpdated,
    TaskCreated,
    TaskCompleted,
    TaskUpdated,
    AreaCreated,
    AreaUpdated,
}

/// 生产力趋势
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProductivityTrends {
    pub daily_completions: Vec<DailyCompletion>,
    pub weekly_progress: Vec<WeeklyProgress>,
    pub monthly_summary: Vec<MonthlySummary>,
    pub efficiency_score: f64,
    pub trend_direction: TrendDirection,
}

/// 每日完成情况
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DailyCompletion {
    pub date: NaiveDate,
    pub tasks_completed: u64,
    pub projects_completed: u64,
    pub hours_worked: f64,
}

/// 每周进度
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WeeklyProgress {
    pub week_start: NaiveDate,
    pub tasks_completed: u64,
    pub projects_completed: u64,
    pub total_hours: f64,
    pub efficiency_score: f64,
}

/// 每月摘要
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MonthlySummary {
    pub month: String,
    pub year: u32,
    pub tasks_completed: u64,
    pub projects_completed: u64,
    pub total_hours: f64,
    pub goals_achieved: u64,
}

/// 趋势方向
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum TrendDirection {
    Improving,
    Stable,
    Declining,
}

/// 即将到期的截止日期
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpcomingDeadline {
    pub entity_id: String,
    pub entity_type: String,
    pub title: String,
    pub deadline: NaiveDate,
    pub days_remaining: i64,
    pub priority: String,
    pub status: String,
}

/// 数据统计和分析服务
pub struct AnalyticsService {
    project_repository: Arc<dyn ProjectRepository>,
    task_repository: Arc<dyn TaskRepository>,
    area_repository: Arc<dyn AreaRepository>,
    user_repository: Arc<dyn UserRepository>,
}

impl AnalyticsService {
    /// 创建新的分析服务
    pub fn new(
        project_repository: Arc<dyn ProjectRepository>,
        task_repository: Arc<dyn TaskRepository>,
        area_repository: Arc<dyn AreaRepository>,
        user_repository: Arc<dyn UserRepository>,
    ) -> Self {
        Self {
            project_repository,
            task_repository,
            area_repository,
            user_repository,
        }
    }

    /// 获取用户仪表板数据
    pub async fn get_dashboard_data(&self, user_id: &str) -> AppResult<DashboardData> {
        // 验证用户是否存在
        if !self.user_repository.exists(user_id).await? {
            return Err(AppError::not_found("用户不存在"));
        }

        // 并行获取各种统计数据
        let (user_stats, project_stats, task_stats, area_stats) = tokio::try_join!(
            self.get_user_stats(user_id),
            self.get_project_stats(user_id),
            self.get_task_stats(user_id),
            self.get_area_stats(user_id)
        )?;

        // 获取最近活动
        let recent_activities = self.get_recent_activities(user_id, 20).await?;

        // 获取生产力趋势
        let productivity_trends = self.get_productivity_trends(user_id).await?;

        // 获取即将到期的截止日期
        let upcoming_deadlines = self.get_upcoming_deadlines(user_id, 30).await?;

        Ok(DashboardData {
            user_stats,
            project_stats,
            task_stats,
            area_stats,
            recent_activities,
            productivity_trends,
            upcoming_deadlines,
        })
    }

    /// 获取用户统计
    async fn get_user_stats(&self, user_id: &str) -> AppResult<UserStats> {
        // 获取项目统计
        let total_projects = self.project_repository.count_by_user(user_id).await?;
        let active_projects = self.project_repository.count_active_by_user(user_id).await?;
        let completed_projects = self.project_repository.count_completed_by_user(user_id).await?;

        // 获取任务统计
        let total_tasks = self.task_repository.count_by_user(user_id).await?;
        let completed_tasks = self.task_repository.count_completed_by_user(user_id).await?;

        // 计算完成率
        let completion_rate = if total_tasks > 0 {
            completed_tasks as f64 / total_tasks as f64
        } else {
            0.0
        };

        // 获取领域统计
        let total_areas = self.area_repository.count_by_user(user_id).await?;
        let active_areas = self.area_repository.count_active_by_user(user_id).await?;

        Ok(UserStats {
            total_projects,
            active_projects,
            completed_projects,
            total_tasks,
            completed_tasks,
            completion_rate,
            total_areas,
            active_areas,
        })
    }

    /// 获取项目统计
    async fn get_project_stats(&self, user_id: &str) -> AppResult<ProjectStats> {
        // 按状态统计
        let by_status = self.project_repository.count_by_status(user_id).await?;
        
        // 按优先级统计
        let by_priority = self.project_repository.count_by_priority(user_id).await?;
        
        // 按领域统计
        let by_area = self.project_repository.count_by_area(user_id).await?;

        // 获取平均进度
        let average_progress = self.project_repository.get_average_progress(user_id).await?;

        // 获取逾期项目数量
        let overdue_count = self.project_repository.count_overdue(user_id).await?;

        // 计算完成率
        let total_projects = by_status.values().sum::<u64>();
        let completed_projects = by_status.get("completed").unwrap_or(&0);
        let completion_rate = if total_projects > 0 {
            *completed_projects as f64 / total_projects as f64
        } else {
            0.0
        };

        // 获取工时统计
        let (total_estimated_hours, total_actual_hours) = 
            self.project_repository.get_hours_summary(user_id).await?;

        Ok(ProjectStats {
            by_status,
            by_priority,
            by_area,
            average_progress,
            overdue_count,
            completion_rate,
            total_estimated_hours,
            total_actual_hours,
        })
    }

    /// 获取任务统计
    async fn get_task_stats(&self, user_id: &str) -> AppResult<TaskStats> {
        // 按状态统计
        let by_status = self.task_repository.count_by_status(user_id).await?;
        
        // 按优先级统计
        let by_priority = self.task_repository.count_by_priority(user_id).await?;
        
        // 按项目统计
        let by_project = self.task_repository.count_by_project(user_id).await?;

        // 获取平均完成度
        let average_completion = self.task_repository.get_average_completion(user_id).await?;

        // 获取逾期任务数量
        let overdue_count = self.task_repository.count_overdue(user_id).await?;

        // 计算完成率
        let total_tasks = by_status.values().sum::<u64>();
        let completed_tasks = by_status.get("completed").unwrap_or(&0);
        let completion_rate = if total_tasks > 0 {
            *completed_tasks as f64 / total_tasks as f64
        } else {
            0.0
        };

        // 获取时间统计
        let (total_estimated_minutes, total_actual_minutes) = 
            self.task_repository.get_time_summary(user_id).await?;

        Ok(TaskStats {
            by_status,
            by_priority,
            by_project,
            average_completion,
            overdue_count,
            completion_rate,
            total_estimated_minutes,
            total_actual_minutes,
        })
    }

    /// 获取领域统计
    async fn get_area_stats(&self, user_id: &str) -> AppResult<AreaStats> {
        let total_areas = self.area_repository.count_by_user(user_id).await?;
        let active_areas = self.area_repository.count_active_by_user(user_id).await?;
        let areas_with_projects = self.area_repository.count_with_projects(user_id).await?;
        let areas_with_tasks = self.area_repository.count_with_tasks(user_id).await?;

        // 获取最活跃和最不活跃的领域
        let activity_stats = self.area_repository.get_activity_stats(user_id).await?;
        let most_active_area = activity_stats.first().map(|(name, _)| name.clone());
        let least_active_area = activity_stats.last().map(|(name, _)| name.clone());

        Ok(AreaStats {
            total_areas,
            active_areas,
            areas_with_projects,
            areas_with_tasks,
            most_active_area,
            least_active_area,
        })
    }

    /// 获取最近活动
    async fn get_recent_activities(&self, user_id: &str, limit: usize) -> AppResult<Vec<RecentActivity>> {
        // 这里需要实现活动日志系统
        // 暂时返回空列表
        Ok(vec![])
    }

    /// 获取生产力趋势
    async fn get_productivity_trends(&self, user_id: &str) -> AppResult<ProductivityTrends> {
        let end_date = Utc::now().date_naive();
        let start_date = end_date - Duration::days(30);

        // 获取每日完成情况
        let daily_completions = self.get_daily_completions(user_id, start_date, end_date).await?;

        // 获取每周进度
        let weekly_progress = self.get_weekly_progress(user_id, start_date, end_date).await?;

        // 获取每月摘要
        let monthly_summary = self.get_monthly_summary(user_id, 6).await?;

        // 计算效率分数
        let efficiency_score = self.calculate_efficiency_score(user_id).await?;

        // 确定趋势方向
        let trend_direction = self.determine_trend_direction(&weekly_progress);

        Ok(ProductivityTrends {
            daily_completions,
            weekly_progress,
            monthly_summary,
            efficiency_score,
            trend_direction,
        })
    }

    /// 获取即将到期的截止日期
    async fn get_upcoming_deadlines(&self, user_id: &str, days_ahead: i64) -> AppResult<Vec<UpcomingDeadline>> {
        let today = Utc::now().date_naive();
        let end_date = today + Duration::days(days_ahead);

        let mut deadlines = Vec::new();

        // 获取项目截止日期
        let project_deadlines = self.project_repository
            .find_with_deadlines_between(user_id, today, end_date).await?;
        
        for project in project_deadlines {
            if let Some(deadline) = project.deadline() {
                let days_remaining = (deadline - today).num_days();
                deadlines.push(UpcomingDeadline {
                    entity_id: project.id().clone(),
                    entity_type: "project".to_string(),
                    title: project.name().clone(),
                    deadline,
                    days_remaining,
                    priority: project.priority().to_string(),
                    status: project.status().to_string(),
                });
            }
        }

        // 获取任务截止日期
        let task_deadlines = self.task_repository
            .find_with_deadlines_between(user_id, today, end_date).await?;
        
        for task in task_deadlines {
            if let Some(deadline) = task.due_date() {
                let days_remaining = (deadline - today).num_days();
                deadlines.push(UpcomingDeadline {
                    entity_id: task.id().clone(),
                    entity_type: "task".to_string(),
                    title: task.title().clone(),
                    deadline,
                    days_remaining,
                    priority: task.priority().to_string(),
                    status: task.status().to_string(),
                });
            }
        }

        // 按截止日期排序
        deadlines.sort_by(|a, b| a.deadline.cmp(&b.deadline));

        Ok(deadlines)
    }

    /// 获取每日完成情况
    async fn get_daily_completions(
        &self,
        user_id: &str,
        start_date: NaiveDate,
        end_date: NaiveDate,
    ) -> AppResult<Vec<DailyCompletion>> {
        // 这里需要实现每日统计查询
        // 暂时返回空列表
        Ok(vec![])
    }

    /// 获取每周进度
    async fn get_weekly_progress(
        &self,
        user_id: &str,
        start_date: NaiveDate,
        end_date: NaiveDate,
    ) -> AppResult<Vec<WeeklyProgress>> {
        // 这里需要实现每周统计查询
        // 暂时返回空列表
        Ok(vec![])
    }

    /// 获取每月摘要
    async fn get_monthly_summary(&self, user_id: &str, months: usize) -> AppResult<Vec<MonthlySummary>> {
        // 这里需要实现每月统计查询
        // 暂时返回空列表
        Ok(vec![])
    }

    /// 计算效率分数
    async fn calculate_efficiency_score(&self, user_id: &str) -> AppResult<f64> {
        // 这里需要实现效率分数计算逻辑
        // 暂时返回固定值
        Ok(75.0)
    }

    /// 确定趋势方向
    fn determine_trend_direction(&self, weekly_progress: &[WeeklyProgress]) -> TrendDirection {
        if weekly_progress.len() < 2 {
            return TrendDirection::Stable;
        }

        let recent_avg = weekly_progress.iter()
            .rev()
            .take(2)
            .map(|w| w.efficiency_score)
            .sum::<f64>() / 2.0;

        let earlier_avg = weekly_progress.iter()
            .take(weekly_progress.len() - 2)
            .map(|w| w.efficiency_score)
            .sum::<f64>() / (weekly_progress.len() - 2) as f64;

        if recent_avg > earlier_avg + 5.0 {
            TrendDirection::Improving
        } else if recent_avg < earlier_avg - 5.0 {
            TrendDirection::Declining
        } else {
            TrendDirection::Stable
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_trend_direction_calculation() {
        let service = AnalyticsService::new(
            Arc::new(MockProjectRepository::new()),
            Arc::new(MockTaskRepository::new()),
            Arc::new(MockAreaRepository::new()),
            Arc::new(MockUserRepository::new()),
        );

        // 测试改善趋势
        let improving_progress = vec![
            WeeklyProgress {
                week_start: NaiveDate::from_ymd_opt(2024, 1, 1).unwrap(),
                tasks_completed: 10,
                projects_completed: 2,
                total_hours: 40.0,
                efficiency_score: 60.0,
            },
            WeeklyProgress {
                week_start: NaiveDate::from_ymd_opt(2024, 1, 8).unwrap(),
                tasks_completed: 15,
                projects_completed: 3,
                total_hours: 45.0,
                efficiency_score: 80.0,
            },
        ];

        let trend = service.determine_trend_direction(&improving_progress);
        assert!(matches!(trend, TrendDirection::Improving));
    }
}
