// 项目应用服务
// 实现项目相关的业务用例

use crate::application::services::{Service, ServiceContext, ServiceResult, PagedResult, ServiceUtils};
use crate::domain::entities::project::{Project, CreateProjectData, UpdateProjectData, ProjectQuery};
use crate::domain::repositories::project_repository::{ProjectRepository, UserProjectStats, AreaProjectStats};
use crate::shared::types::{EntityId, QueryParams, Pagination, ProjectStatus, Priority};
use crate::shared::errors::{AppError, AppResult};
use std::sync::Arc;

/// 项目应用服务
pub struct ProjectService {
    project_repository: Arc<dyn ProjectRepository>,
}

impl ProjectService {
    /// 创建新的项目服务
    pub fn new(project_repository: Arc<dyn ProjectRepository>) -> Self {
        Self { project_repository }
    }

    /// 创建项目
    pub async fn create_project(
        &self,
        context: &ServiceContext,
        data: CreateProjectData,
    ) -> AppResult<ServiceResult<Project>> {
        // 验证用户已认证
        let user_id = context.user_id()?;

        // 确保创建者是当前用户
        if data.created_by != *user_id {
            return Err(AppError::forbidden("只能创建自己的项目"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            project_name = %data.name,
            "Creating new project"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.project_repository.create(data).await
        }).await;

        match result {
            Ok(project) => {
                ServiceUtils::log_audit_event(
                    context,
                    "create_project",
                    "project",
                    &project.id,
                    Some(&format!("Created project: {}", project.name)),
                );

                Ok(ServiceResult::new(project)
                    .with_execution_time(execution_time)
                    .with_affected_entity(project.id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to create project"
                );
                Err(e)
            }
        }
    }

    /// 获取项目
    pub async fn get_project(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<Option<Project>>> {
        tracing::debug!(
            request_id = %context.request_id,
            project_id = %id,
            "Getting project"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.project_repository.find_by_id(id).await
        }).await;

        match result {
            Ok(project_opt) => {
                if let Some(ref project) = project_opt {
                    // 检查权限：只能查看自己的项目
                    let user_id = context.user_id()?;
                    if project.created_by != *user_id {
                        return Err(AppError::forbidden("无权访问此项目"));
                    }

                    ServiceUtils::log_audit_event(
                        context,
                        "get_project",
                        "project",
                        id,
                        Some("Project retrieved successfully"),
                    );
                }

                Ok(ServiceResult::new(project_opt).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    project_id = %id,
                    "Failed to get project"
                );
                Err(e)
            }
        }
    }

    /// 更新项目
    pub async fn update_project(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        data: UpdateProjectData,
    ) -> AppResult<ServiceResult<Project>> {
        // 先获取项目检查权限
        let existing_project = self.project_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("项目"))?;

        let user_id = context.user_id()?;
        if existing_project.created_by != *user_id {
            return Err(AppError::forbidden("无权修改此项目"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            project_id = %id,
            "Updating project"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.project_repository.update(id, data).await
        }).await;

        match result {
            Ok(project) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_project",
                    "project",
                    &project.id,
                    Some(&format!("Updated project: {}", project.name)),
                );

                Ok(ServiceResult::new(project)
                    .with_execution_time(execution_time)
                    .with_affected_entity(project.id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    project_id = %id,
                    "Failed to update project"
                );
                Err(e)
            }
        }
    }

    /// 删除项目
    pub async fn delete_project(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 先获取项目检查权限
        let existing_project = self.project_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("项目"))?;

        let user_id = context.user_id()?;
        if existing_project.created_by != *user_id {
            return Err(AppError::forbidden("无权删除此项目"));
        }

        tracing::warn!(
            request_id = %context.request_id,
            user_id = %user_id,
            project_id = %id,
            "Deleting project"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.project_repository.delete(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "delete_project",
                    "project",
                    id,
                    Some("Project deleted successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    project_id = %id,
                    "Failed to delete project"
                );
                Err(e)
            }
        }
    }

    /// 列出用户的项目
    pub async fn list_user_projects(
        &self,
        context: &ServiceContext,
        page: u32,
        page_size: u32,
        status_filter: Option<ProjectStatus>,
    ) -> AppResult<ServiceResult<PagedResult<Project>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            page = %page,
            page_size = %page_size,
            "Listing user projects"
        );

        let query = ProjectQuery {
            name: None,
            status: status_filter,
            priority: None,
            area_id: None,
            created_by: Some(user_id.clone()),
            deadline_before: None,
            deadline_after: None,
            created_after: None,
            created_before: None,
        };

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: None,
            sort: None,
            filters: None,
        };

        let (projects_result, execution_time) = ServiceUtils::measure_time(async {
            let projects = self.project_repository.find_all(query.clone(), params).await?;
            let total_count = self.project_repository.count(query).await?;
            Ok::<(Vec<Project>, u64), AppError>((projects, total_count))
        }).await;

        match projects_result {
            Ok((projects, total_count)) => {
                let paged_result = PagedResult::new(projects, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "list_user_projects",
                    "project",
                    "multiple",
                    Some(&format!("Retrieved {} projects", paged_result.len())),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to list user projects"
                );
                Err(e)
            }
        }
    }

    /// 更新项目状态
    pub async fn update_project_status(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        status: ProjectStatus,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限
        let existing_project = self.project_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("项目"))?;

        let user_id = context.user_id()?;
        if existing_project.created_by != *user_id {
            return Err(AppError::forbidden("无权修改此项目状态"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            project_id = %id,
            new_status = ?status,
            "Updating project status"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.project_repository.update_status(id, status).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_project_status",
                    "project",
                    id,
                    Some(&format!("Updated project status to: {:?}", status)),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    project_id = %id,
                    "Failed to update project status"
                );
                Err(e)
            }
        }
    }

    /// 更新项目进度
    pub async fn update_project_progress(
        &self,
        context: &ServiceContext,
        id: &EntityId,
        progress: u8,
    ) -> AppResult<ServiceResult<()>> {
        if progress > 100 {
            return Err(AppError::validation("进度不能超过100%"));
        }

        // 检查权限
        let existing_project = self.project_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("项目"))?;

        let user_id = context.user_id()?;
        if existing_project.created_by != *user_id {
            return Err(AppError::forbidden("无权修改此项目进度"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            project_id = %id,
            progress = %progress,
            "Updating project progress"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.project_repository.update_progress(id, progress).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "update_project_progress",
                    "project",
                    id,
                    Some(&format!("Updated project progress to: {}%", progress)),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    project_id = %id,
                    "Failed to update project progress"
                );
                Err(e)
            }
        }
    }

    /// 获取逾期项目
    pub async fn get_overdue_projects(
        &self,
        context: &ServiceContext,
        page: u32,
        page_size: u32,
    ) -> AppResult<ServiceResult<PagedResult<Project>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            "Getting overdue projects"
        );

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: None,
            sort: None,
            filters: None,
        };

        let (result, execution_time) = ServiceUtils::measure_time(async {
            let mut projects = self.project_repository.find_overdue(params).await?;
            // 只返回当前用户的项目
            projects.retain(|p| p.created_by == *user_id);
            let total_count = projects.len() as u64;
            Ok::<(Vec<Project>, u64), AppError>((projects, total_count))
        }).await;

        match result {
            Ok((projects, total_count)) => {
                let paged_result = PagedResult::new(projects, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "get_overdue_projects",
                    "project",
                    "multiple",
                    Some(&format!("Retrieved {} overdue projects", paged_result.len())),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    "Failed to get overdue projects"
                );
                Err(e)
            }
        }
    }

    /// 获取用户项目统计
    pub async fn get_user_project_stats(
        &self,
        context: &ServiceContext,
    ) -> AppResult<ServiceResult<UserProjectStats>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            "Getting user project stats"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.project_repository.get_user_project_stats(user_id).await
        }).await;

        match result {
            Ok(stats) => {
                ServiceUtils::log_audit_event(
                    context,
                    "get_user_project_stats",
                    "project",
                    user_id,
                    Some("Retrieved user project statistics"),
                );

                Ok(ServiceResult::new(stats).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    user_id = %user_id,
                    "Failed to get user project stats"
                );
                Err(e)
            }
        }
    }

    /// 归档项目
    pub async fn archive_project(
        &self,
        context: &ServiceContext,
        id: &EntityId,
    ) -> AppResult<ServiceResult<()>> {
        // 检查权限
        let existing_project = self.project_repository.find_by_id(id).await?
            .ok_or_else(|| AppError::not_found("项目"))?;

        let user_id = context.user_id()?;
        if existing_project.created_by != *user_id {
            return Err(AppError::forbidden("无权归档此项目"));
        }

        tracing::info!(
            request_id = %context.request_id,
            user_id = %user_id,
            project_id = %id,
            "Archiving project"
        );

        let (result, execution_time) = ServiceUtils::measure_time(async {
            self.project_repository.archive(id).await
        }).await;

        match result {
            Ok(()) => {
                ServiceUtils::log_audit_event(
                    context,
                    "archive_project",
                    "project",
                    id,
                    Some("Project archived successfully"),
                );

                Ok(ServiceResult::new(())
                    .with_execution_time(execution_time)
                    .with_affected_entity(id.clone()))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    project_id = %id,
                    "Failed to archive project"
                );
                Err(e)
            }
        }
    }

    /// 搜索项目
    pub async fn search_projects(
        &self,
        context: &ServiceContext,
        keyword: &str,
        page: u32,
        page_size: u32,
    ) -> AppResult<ServiceResult<PagedResult<Project>>> {
        let user_id = context.user_id()?;

        tracing::debug!(
            request_id = %context.request_id,
            user_id = %user_id,
            keyword = %keyword,
            "Searching projects"
        );

        let pagination = Some(Pagination::new(page, page_size));
        let params = QueryParams {
            pagination,
            search: Some(keyword.to_string()),
            sort: None,
            filters: None,
        };

        let (result, execution_time) = ServiceUtils::measure_time(async {
            let mut projects = self.project_repository.search(keyword, params).await?;
            // 只返回当前用户的项目
            projects.retain(|p| p.created_by == *user_id);
            let total_count = projects.len() as u64;
            Ok::<(Vec<Project>, u64), AppError>((projects, total_count))
        }).await;

        match result {
            Ok((projects, total_count)) => {
                let paged_result = PagedResult::new(projects, total_count, page, page_size);

                ServiceUtils::log_audit_event(
                    context,
                    "search_projects",
                    "project",
                    "multiple",
                    Some(&format!("Found {} projects for keyword: {}", paged_result.len(), keyword)),
                );

                Ok(ServiceResult::new(paged_result).with_execution_time(execution_time))
            }
            Err(e) => {
                tracing::error!(
                    error = %e,
                    request_id = %context.request_id,
                    keyword = %keyword,
                    "Failed to search projects"
                );
                Err(e)
            }
        }
    }
}

impl Service for ProjectService {
    fn name(&self) -> &'static str {
        "ProjectService"
    }

    async fn health_check(&self) -> AppResult<()> {
        let query = ProjectQuery {
            name: None,
            status: None,
            priority: None,
            area_id: None,
            created_by: None,
            deadline_before: None,
            deadline_after: None,
            created_after: None,
            created_before: None,
        };
        
        let params = QueryParams {
            pagination: Some(Pagination::new(1, 1)),
            search: None,
            sort: None,
            filters: None,
        };

        self.project_repository.find_all(query, params).await?;
        Ok(())
    }
}
