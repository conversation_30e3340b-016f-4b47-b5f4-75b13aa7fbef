// 按钮组件
// 提供统一的按钮样式和行为

import { Component, JSX, splitProps } from 'solid-js';
import { Dynamic } from 'solid-js/web';

export interface ButtonProps extends JSX.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
  leftIcon?: Component;
  rightIcon?: Component;
  as?: keyof JSX.IntrinsicElements | Component;
}

export const Button: Component<ButtonProps> = (props) => {
  const [local, others] = splitProps(props, [
    'variant',
    'size',
    'loading',
    'leftIcon',
    'rightIcon',
    'as',
    'children',
    'class',
    'disabled'
  ]);

  const variant = () => local.variant || 'primary';
  const size = () => local.size || 'md';
  const component = () => local.as || 'button';

  const baseClasses = () => [
    'inline-flex items-center justify-center font-medium rounded-lg transition-colors',
    'focus:outline-none focus:ring-2 focus:ring-offset-2',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    // Size classes
    size() === 'sm' && 'px-3 py-1.5 text-sm',
    size() === 'md' && 'px-4 py-2 text-sm',
    size() === 'lg' && 'px-6 py-3 text-base',
    // Variant classes
    variant() === 'primary' && [
      'bg-blue-600 text-white hover:bg-blue-700',
      'focus:ring-blue-500'
    ],
    variant() === 'secondary' && [
      'bg-gray-600 text-white hover:bg-gray-700',
      'focus:ring-gray-500'
    ],
    variant() === 'outline' && [
      'border border-gray-300 bg-white text-gray-700 hover:bg-gray-50',
      'focus:ring-blue-500'
    ],
    variant() === 'ghost' && [
      'text-gray-700 hover:bg-gray-100',
      'focus:ring-gray-500'
    ],
    variant() === 'danger' && [
      'bg-red-600 text-white hover:bg-red-700',
      'focus:ring-red-500'
    ],
    local.class
  ].filter(Boolean).flat().join(' ');

  return (
    <Dynamic
      component={component()}
      class={baseClasses()}
      disabled={local.disabled || local.loading}
      {...others}
    >
      {local.loading && (
        <svg
          class="animate-spin -ml-1 mr-2 h-4 w-4"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          />
          <path
            class="opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      
      {local.leftIcon && !local.loading && (
        <Dynamic component={local.leftIcon} class="mr-2 h-4 w-4" />
      )}
      
      {local.children}
      
      {local.rightIcon && (
        <Dynamic component={local.rightIcon} class="ml-2 h-4 w-4" />
      )}
    </Dynamic>
  );
};

// 图标按钮组件
export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon'> {
  icon: Component;
  'aria-label': string;
}

export const IconButton: Component<IconButtonProps> = (props) => {
  const [local, others] = splitProps(props, ['icon', 'children']);

  return (
    <Button {...others}>
      <Dynamic component={local.icon} class="h-4 w-4" />
      {local.children}
    </Button>
  );
};

// 按钮组组件
export interface ButtonGroupProps {
  children: JSX.Element;
  class?: string;
}

export const ButtonGroup: Component<ButtonGroupProps> = (props) => {
  return (
    <div class={`inline-flex rounded-lg shadow-sm ${props.class || ''}`}>
      {props.children}
    </div>
  );
};

// 预定义的常用按钮
export const PrimaryButton: Component<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="primary" {...props} />
);

export const SecondaryButton: Component<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="secondary" {...props} />
);

export const OutlineButton: Component<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="outline" {...props} />
);

export const GhostButton: Component<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="ghost" {...props} />
);

export const DangerButton: Component<Omit<ButtonProps, 'variant'>> = (props) => (
  <Button variant="danger" {...props} />
);

// 加载按钮组件
export interface LoadingButtonProps extends ButtonProps {
  loadingText?: string;
}

export const LoadingButton: Component<LoadingButtonProps> = (props) => {
  const [local, others] = splitProps(props, ['loadingText', 'children']);

  return (
    <Button {...others}>
      {props.loading ? (local.loadingText || '加载中...') : local.children}
    </Button>
  );
};

// 确认按钮组件
export interface ConfirmButtonProps extends ButtonProps {
  confirmText?: string;
  onConfirm?: () => void | Promise<void>;
  requireConfirm?: boolean;
}

export const ConfirmButton: Component<ConfirmButtonProps> = (props) => {
  const [local, others] = splitProps(props, [
    'confirmText',
    'onConfirm',
    'requireConfirm',
    'onClick'
  ]);

  const handleClick = async (e: MouseEvent) => {
    if (local.requireConfirm) {
      const confirmed = confirm(local.confirmText || '确定要执行此操作吗？');
      if (!confirmed) return;
    }

    if (local.onConfirm) {
      await local.onConfirm();
    }

    if (local.onClick) {
      local.onClick(e);
    }
  };

  return (
    <Button onClick={handleClick} {...others} />
  );
};

// 链接样式按钮
export const LinkButton: Component<ButtonProps> = (props) => {
  const [local, others] = splitProps(props, ['class']);

  return (
    <Button
      variant="ghost"
      class={`text-blue-600 hover:text-blue-800 hover:bg-transparent p-0 h-auto font-normal ${local.class || ''}`}
      {...others}
    />
  );
};

// 浮动操作按钮
export interface FabProps extends Omit<ButtonProps, 'size' | 'variant'> {
  size?: 'sm' | 'md' | 'lg';
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

export const Fab: Component<FabProps> = (props) => {
  const [local, others] = splitProps(props, ['size', 'position', 'class']);

  const size = () => local.size || 'md';
  const position = () => local.position || 'bottom-right';

  const sizeClasses = () => {
    switch (size()) {
      case 'sm': return 'w-12 h-12';
      case 'lg': return 'w-16 h-16';
      default: return 'w-14 h-14';
    }
  };

  const positionClasses = () => {
    switch (position()) {
      case 'bottom-left': return 'bottom-6 left-6';
      case 'top-right': return 'top-6 right-6';
      case 'top-left': return 'top-6 left-6';
      default: return 'bottom-6 right-6';
    }
  };

  return (
    <Button
      variant="primary"
      class={`
        fixed ${positionClasses()} ${sizeClasses()}
        rounded-full shadow-lg hover:shadow-xl
        ${local.class || ''}
      `}
      {...others}
    />
  );
};

// 按钮工具函数
export const buttonUtils = {
  // 获取按钮样式类
  getButtonClasses: (variant: ButtonProps['variant'], size: ButtonProps['size']) => {
    const button = Button({ variant, size, children: '' });
    return button.class;
  },

  // 创建自定义按钮变体
  createVariant: (baseClasses: string) => {
    return (props: Omit<ButtonProps, 'variant'>) => (
      <Button class={`${baseClasses} ${props.class || ''}`} {...props} />
    );
  }
};

export default Button;
