# PaoLife API接口规范

## 项目概述

本文档定义了PaoLife项目的Tauri命令接口规范，包括数据类型、接口定义、错误处理等，确保前后端协作的一致性和类型安全。

## 第一部分：技术架构

### 1. 接口技术栈

#### 1.1 Tauri命令系统
```rust
// Tauri命令定义
#[tauri::command]
#[specta::specta]
async fn create_project(
    state: tauri::State<'_, AppState>,
    request: CreateProjectRequest,
) -> Result<Project, AppError> {
    // 实现逻辑
}
```

#### 1.2 类型安全保证
```rust
// 使用 tauri-specta 自动生成 TypeScript 类型
use specta::Type;
use serde::{Deserialize, Serialize};

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct Project {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: ProjectStatus,
    pub created_at: chrono::DateTime<chrono::Utc>,
}
```

### 2. 数据类型定义

#### 2.1 基础类型
```rust
// 通用ID类型
pub type EntityId = String;

// 时间戳类型
pub type Timestamp = chrono::DateTime<chrono::Utc>;

// 分页参数
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct PaginationParams {
    pub page: u32,
    pub page_size: u32,
    pub sort_by: Option<String>,
    pub sort_order: Option<SortOrder>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum SortOrder {
    Asc,
    Desc,
}

// 分页响应
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct PaginatedResponse<T> {
    pub data: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
}
```

#### 2.2 枚举类型
```rust
// 项目状态
#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "project_status", rename_all = "snake_case")]
pub enum ProjectStatus {
    NotStarted,
    InProgress,
    AtRisk,
    Paused,
    Completed,
    Archived,
}

// 任务状态
#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "task_status", rename_all = "snake_case")]
pub enum TaskStatus {
    Todo,
    InProgress,
    Waiting,
    Completed,
    Cancelled,
}

// 优先级
#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "priority", rename_all = "snake_case")]
pub enum Priority {
    Low = 1,
    Medium = 2,
    High = 3,
    Urgent = 4,
}
```

#### 2.3 实体类型
```rust
// 项目实体
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct Project {
    pub id: EntityId,
    pub name: String,
    pub description: Option<String>,
    pub status: ProjectStatus,
    pub progress: u8, // 0-100
    pub priority: Option<Priority>,
    pub start_date: Option<chrono::NaiveDate>,
    pub deadline: Option<chrono::NaiveDate>,
    pub area_id: Option<EntityId>,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub archived_at: Option<Timestamp>,
}

// 领域实体
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct Area {
    pub id: EntityId,
    pub name: String,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
    pub sort_order: i32,
    pub is_active: bool,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
}

// 任务实体
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct Task {
    pub id: EntityId,
    pub title: String,
    pub description: Option<String>,
    pub status: TaskStatus,
    pub priority: Option<Priority>,
    pub parent_task_id: Option<EntityId>,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub due_date: Option<chrono::NaiveDate>,
    pub estimated_minutes: Option<u32>,
    pub actual_minutes: Option<u32>,
    pub completion_percentage: u8,
    pub sort_order: i32,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub completed_at: Option<Timestamp>,
}
```

## 第二部分：项目管理接口

### 1. 项目CRUD操作

#### 1.1 创建项目
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct CreateProjectRequest {
    pub name: String,
    pub description: Option<String>,
    pub area_id: Option<EntityId>,
    pub start_date: Option<chrono::NaiveDate>,
    pub deadline: Option<chrono::NaiveDate>,
    pub priority: Option<Priority>,
}

#[tauri::command]
#[specta::specta]
async fn create_project(
    state: tauri::State<'_, AppState>,
    request: CreateProjectRequest,
) -> Result<Project, AppError> {
    // 实现逻辑
}
```

#### 1.2 获取项目列表
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ProjectFilter {
    pub status: Option<ProjectStatus>,
    pub area_id: Option<EntityId>,
    pub search: Option<String>,
    pub archived: Option<bool>,
}

#[tauri::command]
#[specta::specta]
async fn get_projects(
    state: tauri::State<'_, AppState>,
    filter: Option<ProjectFilter>,
    pagination: Option<PaginationParams>,
) -> Result<PaginatedResponse<Project>, AppError> {
    // 实现逻辑
}
```

#### 1.3 获取项目详情
```rust
#[tauri::command]
#[specta::specta]
async fn get_project(
    state: tauri::State<'_, AppState>,
    id: EntityId,
) -> Result<Project, AppError> {
    // 实现逻辑
}
```

#### 1.4 更新项目
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct UpdateProjectRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub status: Option<ProjectStatus>,
    pub progress: Option<u8>,
    pub priority: Option<Priority>,
    pub start_date: Option<chrono::NaiveDate>,
    pub deadline: Option<chrono::NaiveDate>,
    pub area_id: Option<EntityId>,
}

#[tauri::command]
#[specta::specta]
async fn update_project(
    state: tauri::State<'_, AppState>,
    id: EntityId,
    request: UpdateProjectRequest,
) -> Result<Project, AppError> {
    // 实现逻辑
}
```

#### 1.5 删除项目
```rust
#[tauri::command]
#[specta::specta]
async fn delete_project(
    state: tauri::State<'_, AppState>,
    id: EntityId,
) -> Result<(), AppError> {
    // 实现逻辑
}
```

### 2. 项目统计接口

#### 2.1 项目统计数据
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ProjectStats {
    pub total_projects: u64,
    pub active_projects: u64,
    pub completed_projects: u64,
    pub overdue_projects: u64,
    pub average_completion_rate: f64,
}

#[tauri::command]
#[specta::specta]
async fn get_project_stats(
    state: tauri::State<'_, AppState>,
) -> Result<ProjectStats, AppError> {
    // 实现逻辑
}
```

## 第三部分：任务管理接口

### 1. 任务CRUD操作

#### 1.1 创建任务
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct CreateTaskRequest {
    pub title: String,
    pub description: Option<String>,
    pub parent_task_id: Option<EntityId>,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub priority: Option<Priority>,
    pub due_date: Option<chrono::NaiveDate>,
    pub estimated_minutes: Option<u32>,
}

#[tauri::command]
#[specta::specta]
async fn create_task(
    state: tauri::State<'_, AppState>,
    request: CreateTaskRequest,
) -> Result<Task, AppError> {
    // 实现逻辑
}
```

#### 1.2 获取任务列表
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct TaskFilter {
    pub status: Option<TaskStatus>,
    pub priority: Option<Priority>,
    pub project_id: Option<EntityId>,
    pub area_id: Option<EntityId>,
    pub parent_task_id: Option<EntityId>,
    pub search: Option<String>,
    pub due_date_from: Option<chrono::NaiveDate>,
    pub due_date_to: Option<chrono::NaiveDate>,
}

#[tauri::command]
#[specta::specta]
async fn get_tasks(
    state: tauri::State<'_, AppState>,
    filter: Option<TaskFilter>,
    pagination: Option<PaginationParams>,
) -> Result<PaginatedResponse<Task>, AppError> {
    // 实现逻辑
}
```

#### 1.3 获取任务树
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct TaskTreeNode {
    pub task: Task,
    pub children: Vec<TaskTreeNode>,
    pub level: u32,
}

#[tauri::command]
#[specta::specta]
async fn get_task_tree(
    state: tauri::State<'_, AppState>,
    root_id: Option<EntityId>, // None 表示获取根级任务
    max_depth: Option<u32>,
) -> Result<Vec<TaskTreeNode>, AppError> {
    // 实现逻辑
}
```

### 2. 任务操作接口

#### 2.1 批量更新任务
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct BatchUpdateTasksRequest {
    pub task_ids: Vec<EntityId>,
    pub updates: UpdateTaskRequest,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct UpdateTaskRequest {
    pub title: Option<String>,
    pub description: Option<String>,
    pub status: Option<TaskStatus>,
    pub priority: Option<Priority>,
    pub due_date: Option<chrono::NaiveDate>,
    pub completion_percentage: Option<u8>,
}

#[tauri::command]
#[specta::specta]
async fn batch_update_tasks(
    state: tauri::State<'_, AppState>,
    request: BatchUpdateTasksRequest,
) -> Result<Vec<Task>, AppError> {
    // 实现逻辑
}
```

#### 2.2 移动任务
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct MoveTaskRequest {
    pub task_id: EntityId,
    pub new_parent_id: Option<EntityId>,
    pub new_position: Option<u32>,
}

#[tauri::command]
#[specta::specta]
async fn move_task(
    state: tauri::State<'_, AppState>,
    request: MoveTaskRequest,
) -> Result<(), AppError> {
    // 实现逻辑
}
```

## 第四部分：领域管理接口

### 1. 领域CRUD操作

#### 1.1 创建领域
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct CreateAreaRequest {
    pub name: String,
    pub description: Option<String>,
    pub standard: Option<String>,
    pub icon_name: Option<String>,
    pub color_hex: Option<String>,
}

#[tauri::command]
#[specta::specta]
async fn create_area(
    state: tauri::State<'_, AppState>,
    request: CreateAreaRequest,
) -> Result<Area, AppError> {
    // 实现逻辑
}
```

### 2. 习惯管理接口

#### 2.1 习惯实体
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct Habit {
    pub id: EntityId,
    pub area_id: EntityId,
    pub name: String,
    pub description: Option<String>,
    pub target_frequency: u32,
    pub frequency_unit: FrequencyUnit,
    pub difficulty_level: u8, // 1-5
    pub reward_points: u32,
    pub is_active: bool,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "frequency_unit", rename_all = "snake_case")]
pub enum FrequencyUnit {
    Daily,
    Weekly,
    Monthly,
}
```

#### 2.2 习惯记录
```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct HabitRecord {
    pub id: EntityId,
    pub habit_id: EntityId,
    pub completed_date: chrono::NaiveDate,
    pub completion_value: u32,
    pub note: Option<String>,
    pub mood_rating: Option<u8>, // 1-5
    pub created_at: Timestamp,
}

#[tauri::command]
#[specta::specta]
async fn record_habit(
    state: tauri::State<'_, AppState>,
    habit_id: EntityId,
    date: chrono::NaiveDate,
    completion_value: u32,
    note: Option<String>,
    mood_rating: Option<u8>,
) -> Result<HabitRecord, AppError> {
    // 实现逻辑
}
```

## 第五部分：错误处理

### 1. 错误类型定义

```rust
#[derive(Debug, thiserror::Error, Serialize, Deserialize, Type)]
pub enum AppError {
    #[error("Database error: {message}")]
    Database { message: String },
    
    #[error("Validation error: {field}: {message}")]
    Validation { field: String, message: String },
    
    #[error("Not found: {resource} with id {id}")]
    NotFound { resource: String, id: String },
    
    #[error("Permission denied: {action} on {resource}")]
    PermissionDenied { action: String, resource: String },
    
    #[error("Conflict: {message}")]
    Conflict { message: String },
    
    #[error("Internal server error: {message}")]
    Internal { message: String },
}
```

### 2. 错误响应格式

```typescript
// 前端错误处理
interface ApiError {
  type: 'Database' | 'Validation' | 'NotFound' | 'PermissionDenied' | 'Conflict' | 'Internal';
  message: string;
  field?: string;
  resource?: string;
  id?: string;
  action?: string;
}

// 错误处理示例
async function handleApiCall<T>(apiCall: () => Promise<T>): Promise<T> {
  try {
    return await apiCall();
  } catch (error) {
    const apiError = error as ApiError;
    
    switch (apiError.type) {
      case 'Validation':
        showValidationError(apiError.field!, apiError.message);
        break;
      case 'NotFound':
        showNotFoundError(apiError.resource!, apiError.id!);
        break;
      case 'PermissionDenied':
        showPermissionError(apiError.action!, apiError.resource!);
        break;
      default:
        showGenericError(apiError.message);
    }
    
    throw error;
  }
}
```

## 第六部分：前端集成

### 1. TypeScript 类型生成

```rust
// 生成 TypeScript 类型
use specta::collect_types;
use tauri_specta::ts;

fn main() {
    ts::export(
        collect_types![
            // 实体类型
            Project, Area, Task, Habit, HabitRecord,
            // 请求类型
            CreateProjectRequest, UpdateProjectRequest,
            CreateTaskRequest, UpdateTaskRequest,
            CreateAreaRequest,
            // 响应类型
            PaginatedResponse<Project>,
            ProjectStats,
            TaskTreeNode,
            // 错误类型
            AppError,
        ],
        "../src/types/api.ts",
    ).unwrap();
}
```

### 2. 前端API调用

```typescript
// src/lib/api.ts
import { invoke } from '@tauri-apps/api/tauri';
import type { 
  Project, 
  CreateProjectRequest, 
  UpdateProjectRequest,
  PaginatedResponse,
  ProjectFilter,
  PaginationParams 
} from '@/types/api';

export class ProjectApi {
  static async create(request: CreateProjectRequest): Promise<Project> {
    return invoke('create_project', { request });
  }
  
  static async getList(
    filter?: ProjectFilter,
    pagination?: PaginationParams
  ): Promise<PaginatedResponse<Project>> {
    return invoke('get_projects', { filter, pagination });
  }
  
  static async getById(id: string): Promise<Project> {
    return invoke('get_project', { id });
  }
  
  static async update(id: string, request: UpdateProjectRequest): Promise<Project> {
    return invoke('update_project', { id, request });
  }
  
  static async delete(id: string): Promise<void> {
    return invoke('delete_project', { id });
  }
}
```

## 总结

这个API接口规范提供了：

1. **类型安全**: 使用 tauri-specta 确保前后端类型一致
2. **标准化**: 统一的请求/响应格式和错误处理
3. **完整性**: 覆盖所有核心功能的接口定义
4. **可维护性**: 清晰的接口文档和代码示例

## 第七部分：资源管理接口

### 1. 资源实体定义

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct Resource {
    pub id: EntityId,
    pub resource_type: ResourceType,
    pub path: String,
    pub title: Option<String>,
    pub mime_type: Option<String>,
    pub file_size: Option<u64>,
    pub file_hash: Option<String>,
    pub metadata: Option<serde_json::Value>,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub last_accessed_at: Option<Timestamp>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "resource_type", rename_all = "snake_case")]
pub enum ResourceType {
    File,
    Folder,
    Url,
    Note,
    Reference,
}
```

### 2. 资源关联接口

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ResourceReference {
    pub id: EntityId,
    pub source_entity_type: EntityType,
    pub source_entity_id: EntityId,
    pub target_resource_id: EntityId,
    pub reference_type: ReferenceType,
    pub context: Option<String>,
    pub display_text: Option<String>,
    pub line_number: Option<u32>,
    pub created_at: Timestamp,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "entity_type", rename_all = "snake_case")]
pub enum EntityType {
    Project,
    Area,
    Task,
    Resource,
    Review,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "reference_type", rename_all = "snake_case")]
pub enum ReferenceType {
    Attachment,
    Wikilink,
    Embed,
    Citation,
    Dependency,
}

#[tauri::command]
#[specta::specta]
async fn link_resource(
    state: tauri::State<'_, AppState>,
    source_type: EntityType,
    source_id: EntityId,
    resource_id: EntityId,
    reference_type: ReferenceType,
    context: Option<String>,
) -> Result<ResourceReference, AppError> {
    // 实现逻辑
}
```

### 3. 文档链接接口

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct DocumentLink {
    pub id: EntityId,
    pub source_path: String,
    pub target_path: String,
    pub link_type: ReferenceType,
    pub display_text: Option<String>,
    pub context_text: Option<String>,
    pub line_number: Option<u32>,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
}

#[tauri::command]
#[specta::specta]
async fn get_document_links(
    state: tauri::State<'_, AppState>,
    document_path: String,
) -> Result<Vec<DocumentLink>, AppError> {
    // 实现逻辑
}

#[tauri::command]
#[specta::specta]
async fn get_backlinks(
    state: tauri::State<'_, AppState>,
    document_path: String,
) -> Result<Vec<DocumentLink>, AppError> {
    // 实现逻辑
}
```

## 第八部分：收件箱管理接口

### 1. 收件箱实体

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct InboxItem {
    pub id: EntityId,
    pub content: String,
    pub item_type: InboxItemType,
    pub source: InboxSource,
    pub processing_status: ProcessingStatus,
    pub processed_into_type: Option<EntityType>,
    pub processed_into_id: Option<EntityId>,
    pub priority: Priority,
    pub created_at: Timestamp,
    pub processed_at: Option<Timestamp>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "inbox_item_type", rename_all = "snake_case")]
pub enum InboxItemType {
    QuickNote,
    Idea,
    Task,
    Reference,
    MeetingNote,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "inbox_source", rename_all = "snake_case")]
pub enum InboxSource {
    Manual,
    Email,
    WebClipper,
    Voice,
    Api,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "processing_status", rename_all = "snake_case")]
pub enum ProcessingStatus {
    Unprocessed,
    Processing,
    Processed,
    Archived,
}
```

### 2. 收件箱操作接口

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct CreateInboxItemRequest {
    pub content: String,
    pub item_type: InboxItemType,
    pub source: InboxSource,
    pub priority: Option<Priority>,
}

#[tauri::command]
#[specta::specta]
async fn create_inbox_item(
    state: tauri::State<'_, AppState>,
    request: CreateInboxItemRequest,
) -> Result<InboxItem, AppError> {
    // 实现逻辑
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ProcessInboxItemRequest {
    pub processed_into_type: EntityType,
    pub processed_into_id: EntityId,
}

#[tauri::command]
#[specta::specta]
async fn process_inbox_item(
    state: tauri::State<'_, AppState>,
    item_id: EntityId,
    request: ProcessInboxItemRequest,
) -> Result<InboxItem, AppError> {
    // 实现逻辑
}

#[tauri::command]
#[specta::specta]
async fn batch_process_inbox_items(
    state: tauri::State<'_, AppState>,
    item_ids: Vec<EntityId>,
    action: BatchInboxAction,
) -> Result<Vec<InboxItem>, AppError> {
    // 实现逻辑
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum BatchInboxAction {
    Archive,
    Delete,
    MarkAsProcessed,
    ChangePriority(Priority),
}
```

## 第九部分：分析和统计接口

### 1. 仪表盘数据

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct DashboardData {
    pub stats: DashboardStats,
    pub recent_activities: Vec<RecentActivity>,
    pub upcoming_deadlines: Vec<UpcomingDeadline>,
    pub habit_summary: HabitSummary,
    pub productivity_insights: Vec<ProductivityInsight>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct DashboardStats {
    pub total_projects: u64,
    pub active_projects: u64,
    pub total_areas: u64,
    pub pending_tasks: u64,
    pub completed_tasks_today: u64,
    pub inbox_items: u64,
    pub habit_streak: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct RecentActivity {
    pub id: EntityId,
    pub activity_type: ActivityType,
    pub entity_type: EntityType,
    pub entity_id: EntityId,
    pub entity_name: String,
    pub description: String,
    pub timestamp: Timestamp,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum ActivityType {
    Created,
    Updated,
    Completed,
    Deleted,
    Archived,
}

#[tauri::command]
#[specta::specta]
async fn get_dashboard_data(
    state: tauri::State<'_, AppState>,
) -> Result<DashboardData, AppError> {
    // 实现逻辑
}
```

### 2. 分析报告接口

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct AnalyticsRequest {
    pub date_range: DateRange,
    pub entity_types: Option<Vec<EntityType>>,
    pub metrics: Vec<AnalyticsMetric>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct DateRange {
    pub start_date: chrono::NaiveDate,
    pub end_date: chrono::NaiveDate,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum AnalyticsMetric {
    TaskCompletionRate,
    ProjectProgress,
    HabitConsistency,
    TimeSpent,
    ProductivityTrend,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct AnalyticsReport {
    pub date_range: DateRange,
    pub metrics: Vec<MetricData>,
    pub charts: Vec<ChartData>,
    pub insights: Vec<AnalyticsInsight>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct MetricData {
    pub metric: AnalyticsMetric,
    pub value: f64,
    pub change_percentage: Option<f64>,
    pub trend: Trend,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum Trend {
    Up,
    Down,
    Stable,
}

#[tauri::command]
#[specta::specta]
async fn generate_analytics_report(
    state: tauri::State<'_, AppState>,
    request: AnalyticsRequest,
) -> Result<AnalyticsReport, AppError> {
    // 实现逻辑
}
```

## 第十部分：系统管理接口

### 1. 配置管理

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct AppSetting {
    pub setting_key: String,
    pub setting_value: String,
    pub setting_type: SettingType,
    pub description: Option<String>,
    pub is_user_configurable: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type, sqlx::Type)]
#[sqlx(type_name = "setting_type", rename_all = "snake_case")]
pub enum SettingType {
    String,
    Number,
    Boolean,
    Json,
}

#[tauri::command]
#[specta::specta]
async fn get_settings(
    state: tauri::State<'_, AppState>,
) -> Result<Vec<AppSetting>, AppError> {
    // 实现逻辑
}

#[tauri::command]
#[specta::specta]
async fn update_setting(
    state: tauri::State<'_, AppState>,
    key: String,
    value: String,
) -> Result<AppSetting, AppError> {
    // 实现逻辑
}
```

### 2. 数据备份和恢复

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct BackupInfo {
    pub id: EntityId,
    pub backup_type: BackupType,
    pub file_path: String,
    pub file_size: u64,
    pub created_at: Timestamp,
    pub description: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum BackupType {
    Full,
    Incremental,
    Manual,
}

#[tauri::command]
#[specta::specta]
async fn create_backup(
    state: tauri::State<'_, AppState>,
    backup_type: BackupType,
    description: Option<String>,
) -> Result<BackupInfo, AppError> {
    // 实现逻辑
}

#[tauri::command]
#[specta::specta]
async fn restore_backup(
    state: tauri::State<'_, AppState>,
    backup_id: EntityId,
) -> Result<(), AppError> {
    // 实现逻辑
}

#[tauri::command]
#[specta::specta]
async fn get_backup_list(
    state: tauri::State<'_, AppState>,
) -> Result<Vec<BackupInfo>, AppError> {
    // 实现逻辑
}
```

### 3. 数据导入导出

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ExportRequest {
    pub format: ExportFormat,
    pub entity_types: Vec<EntityType>,
    pub date_range: Option<DateRange>,
    pub include_archived: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum ExportFormat {
    Json,
    Csv,
    Markdown,
    Pdf,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ExportResult {
    pub file_path: String,
    pub file_size: u64,
    pub exported_count: u64,
    pub created_at: Timestamp,
}

#[tauri::command]
#[specta::specta]
async fn export_data(
    state: tauri::State<'_, AppState>,
    request: ExportRequest,
) -> Result<ExportResult, AppError> {
    // 实现逻辑
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ImportRequest {
    pub file_path: String,
    pub format: ExportFormat,
    pub merge_strategy: MergeStrategy,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum MergeStrategy {
    Replace,
    Merge,
    Skip,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ImportResult {
    pub imported_count: u64,
    pub skipped_count: u64,
    pub error_count: u64,
    pub errors: Vec<ImportError>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct ImportError {
    pub line_number: u32,
    pub error_message: String,
    pub data: String,
}

#[tauri::command]
#[specta::specta]
async fn import_data(
    state: tauri::State<'_, AppState>,
    request: ImportRequest,
) -> Result<ImportResult, AppError> {
    // 实现逻辑
}
```

## 第十一部分：搜索接口

### 1. 全文搜索

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct SearchRequest {
    pub query: String,
    pub entity_types: Option<Vec<EntityType>>,
    pub filters: Option<SearchFilters>,
    pub sort_by: Option<SearchSortBy>,
    pub pagination: Option<PaginationParams>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct SearchFilters {
    pub date_range: Option<DateRange>,
    pub status: Option<String>,
    pub tags: Option<Vec<String>>,
    pub priority: Option<Priority>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum SearchSortBy {
    Relevance,
    Date,
    Name,
    Priority,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct SearchResult {
    pub entity_type: EntityType,
    pub entity_id: EntityId,
    pub title: String,
    pub snippet: String,
    pub score: f64,
    pub highlights: Vec<SearchHighlight>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct SearchHighlight {
    pub field: String,
    pub text: String,
    pub start: u32,
    pub end: u32,
}

#[tauri::command]
#[specta::specta]
async fn search(
    state: tauri::State<'_, AppState>,
    request: SearchRequest,
) -> Result<PaginatedResponse<SearchResult>, AppError> {
    // 实现逻辑
}
```

### 2. 智能建议

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct SuggestionRequest {
    pub context: SuggestionContext,
    pub limit: Option<u32>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum SuggestionContext {
    TaskCreation { project_id: Option<EntityId> },
    ProjectCreation { area_id: Option<EntityId> },
    TagSuggestion { entity_type: EntityType },
    RelatedItems { entity_type: EntityType, entity_id: EntityId },
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct Suggestion {
    pub suggestion_type: SuggestionType,
    pub title: String,
    pub description: Option<String>,
    pub confidence: f64,
    pub data: serde_json::Value,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum SuggestionType {
    Task,
    Project,
    Tag,
    RelatedItem,
    Template,
}

#[tauri::command]
#[specta::specta]
async fn get_suggestions(
    state: tauri::State<'_, AppState>,
    request: SuggestionRequest,
) -> Result<Vec<Suggestion>, AppError> {
    // 实现逻辑
}
```

## 第十二部分：实时通知接口

### 1. 事件系统

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct AppEvent {
    pub event_type: EventType,
    pub entity_type: EntityType,
    pub entity_id: EntityId,
    pub data: serde_json::Value,
    pub timestamp: Timestamp,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum EventType {
    Created,
    Updated,
    Deleted,
    StatusChanged,
    DeadlineApproaching,
    HabitReminder,
    BackupCompleted,
    SyncCompleted,
}

// 使用 Tauri 的事件系统
#[tauri::command]
#[specta::specta]
async fn subscribe_to_events(
    app_handle: tauri::AppHandle,
    event_types: Vec<EventType>,
) -> Result<(), AppError> {
    // 实现事件订阅逻辑
}
```

### 2. 通知管理

```rust
#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub struct Notification {
    pub id: EntityId,
    pub notification_type: NotificationType,
    pub title: String,
    pub message: String,
    pub priority: Priority,
    pub is_read: bool,
    pub action_url: Option<String>,
    pub created_at: Timestamp,
    pub expires_at: Option<Timestamp>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Type)]
pub enum NotificationType {
    Info,
    Warning,
    Error,
    Success,
    Reminder,
}

#[tauri::command]
#[specta::specta]
async fn get_notifications(
    state: tauri::State<'_, AppState>,
    unread_only: bool,
) -> Result<Vec<Notification>, AppError> {
    // 实现逻辑
}

#[tauri::command]
#[specta::specta]
async fn mark_notification_read(
    state: tauri::State<'_, AppState>,
    notification_id: EntityId,
) -> Result<(), AppError> {
    // 实现逻辑
}
```

遵循这个规范将确保前后端协作的顺利进行和代码质量的保证。
