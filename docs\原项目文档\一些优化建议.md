1. PaoLife 优化后完整数据库结构
本结构整合了所有优化建议，将原始的18+个核心表精简和重组为15个高度规范化和模块化的表。设计目标是统一性、可扩展性和清晰性。

1. 核心实体 (Core Entities)
这些是P.A.R.A.方法论的基础实体，结构被精简，关联关系被解耦。

User - 用户表

CREATE TABLE IF NOT EXISTS "User" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "username" TEXT NOT NULL UNIQUE,
  "password" TEXT NOT NULL,
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now'))
);

优化: 移除了JSON格式的settings字段，由独立的Setting表管理。

Project - 项目表

CREATE TABLE IF NOT EXISTS "Project" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "area_id" TEXT,
  "name" TEXT NOT NULL,
  "description" TEXT,
  "status" TEXT NOT NULL DEFAULT 'Not Started',
  "goal" TEXT,
  "start_date" TEXT,
  "deadline" TEXT,
  "archived" INTEGER NOT NULL DEFAULT 0,
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  "updated_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  FOREIGN KEY ("area_id") REFERENCES "Area" ("id") ON DELETE SET NULL
);

优化: 移除了progress等可计算字段。与Area的关联保持，但与其他实体的关联通过Reference表进行。

Area - 领域表

CREATE TABLE IF NOT EXISTS "Area" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "standard" TEXT,
  "description" TEXT,
  "icon" TEXT,
  "color" TEXT,
  "archived" INTEGER NOT NULL DEFAULT 0,
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  "updated_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now'))
);

优化: 保持结构纯粹，所有关联项（项目除外）都通过Reference表进行。

Task - 任务表

CREATE TABLE IF NOT EXISTS "Task" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "parent_id" TEXT,
  "content" TEXT NOT NULL,
  "description" TEXT,
  "completed" INTEGER NOT NULL DEFAULT 0,
  "priority" TEXT,
  "due_date" TEXT,
  "completed_at" TEXT,
  "position" REAL NOT NULL DEFAULT 0,
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  "updated_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  FOREIGN KEY ("parent_id") REFERENCES "Task" ("id") ON DELETE SET NULL
);

优化: 移除了projectId, areaId, resourceLinkId等直接外键，所有关联通过Reference表管理，使任务模型更加独立。

2. 统一指标与习惯 (Unified Metrics & Habits)
将原先分散在项目和领域中的指标系统合并，提升了复用性。

Metric - 统一指标表

CREATE TABLE IF NOT EXISTS "Metric" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "owner_type" TEXT NOT NULL, -- 'Project' 或 'Area'
  "owner_id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "target_value" TEXT,
  "unit" TEXT,
  "direction" TEXT NOT NULL DEFAULT 'increase', -- 'increase' 或 'decrease'
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  "updated_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now'))
);

优化: 全新合并表，替代了ProjectKPI和AreaMetric，通过owner_type和owner_id实现多态关联。

MetricRecord - 统一指标记录表

CREATE TABLE IF NOT EXISTS "MetricRecord" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "metric_id" TEXT NOT NULL,
  "value" TEXT NOT NULL,
  "note" TEXT,
  "recorded_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  FOREIGN KEY ("metric_id") REFERENCES "Metric" ("id") ON DELETE CASCADE
);

优化: 全新合并表，替代了KPIRecord和AreaMetricRecord。

Habit & HabitRecord - 习惯与记录表

CREATE TABLE IF NOT EXISTS "Habit" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "area_id" TEXT NOT NULL,
  "name" TEXT NOT NULL,
  "frequency" TEXT NOT NULL,
  "target" INTEGER NOT NULL,
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  "updated_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  FOREIGN KEY ("area_id") REFERENCES "Area" ("id") ON DELETE CASCADE
);

CREATE TABLE IF NOT EXISTS "HabitRecord" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "habit_id" TEXT NOT NULL,
  "date" TEXT NOT NULL, -- 格式: YYYY-MM-DD
  "completed" INTEGER NOT NULL DEFAULT 1,
  "value" REAL,
  "note" TEXT,
  FOREIGN KEY ("habit_id") REFERENCES "Habit" ("id") ON DELETE CASCADE,
  UNIQUE ("habit_id", "date")
);

优化: 结构保持稳定，字段名规范化。

3. 统一资源与引用 (Unified Resources & References)
这是本次优化的核心，建立了一个强大且可扩展的全局关联模型。

Resource - 资源定义表

CREATE TABLE IF NOT EXISTS "Resource" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "type" TEXT NOT NULL,         -- 'file', 未来可以是 'url', 'note' 等
  "path" TEXT NOT NULL UNIQUE,  -- 资源的唯一路径或标识符
  "title" TEXT,
  "metadata" TEXT,              -- JSON字段，存储文件大小、MIME类型等
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  "updated_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now'))
);

优化: 全新表，作为所有资源的中央仓库。

Reference - 统一引用/关联表

CREATE TABLE IF NOT EXISTS "Reference" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "source_entity_type" TEXT NOT NULL, -- e.g., 'Project', 'Task', 'Resource'
  "source_entity_id" TEXT NOT NULL,   -- 发起引用的实体ID
  "target_resource_id" TEXT NOT NULL, -- 被引用的资源ID
  "context" TEXT,                     -- 描述引用的上下文信息, e.g., WikiLink的链接文本
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  FOREIGN KEY ("target_resource_id") REFERENCES "Resource" ("id") ON DELETE CASCADE
);

优化: 全新表，替代了ResourceLink, DocumentLink等，用于处理应用内所有实体到资源的关联。

4. 统一标签 (Unified Tagging)
将标签系统通用化，可以为任何实体打标签。

Tag & Taggable - 标签与关联表

CREATE TABLE IF NOT EXISTS "Tag" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL UNIQUE,
  "color" TEXT
);

CREATE TABLE IF NOT EXISTS "Taggable" (
  "tag_id" TEXT NOT NULL,
  "taggable_type" TEXT NOT NULL, -- 'Task', 'Project', 'Area', 'Resource' 等
  "taggable_id" TEXT NOT NULL,
  PRIMARY KEY ("tag_id", "taggable_type", "taggable_id"),
  FOREIGN KEY ("tag_id") REFERENCES "Tag" ("id") ON DELETE CASCADE
);

优化: TaskTag被通用的Taggable表替代，实现了多态的标签关联。

5. 功能模块 (Feature Modules)
这些是支持核心工作流的独立功能模块。

InboxItem - 收件箱表

CREATE TABLE IF NOT EXISTS "InboxItem" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "content" TEXT NOT NULL,
  "processed" INTEGER NOT NULL DEFAULT 0,
  "processed_at" TEXT,
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now'))
);

优化: 从localStorage移入数据库，保证数据持久性和一致性。

Review & ReviewTemplate - 复盘与模板表

CREATE TABLE IF NOT EXISTS "ReviewTemplate" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "name" TEXT NOT NULL,
  "type" TEXT NOT NULL,
  "structure" TEXT NOT NULL, -- JSON
  "is_default" INTEGER NOT NULL DEFAULT 0
);

CREATE TABLE IF NOT EXISTS "Review" (
  "id" TEXT NOT NULL PRIMARY KEY,
  "template_id" TEXT,
  "type" TEXT NOT NULL,
  "period" TEXT NOT NULL,
  "title" TEXT,
  "content" TEXT NOT NULL, -- JSON
  "status" TEXT NOT NULL DEFAULT 'draft',
  "completed_at" TEXT,
  "created_at" TEXT NOT NULL DEFAULT (strftime('%Y-%m-%d %H:%M:%f', 'now')),
  FOREIGN KEY ("template_id") REFERENCES "ReviewTemplate" ("id") ON DELETE SET NULL
);

优化: 结构保持稳定，字段名规范化。

Setting - 配置表

CREATE TABLE IF NOT EXISTS "Setting" (
  "key" TEXT NOT NULL PRIMARY KEY,
  "value" TEXT NOT NULL
);

优化: 全新表，用于存储键值对形式的应用配置，更加规范。
2. 一、推荐技术栈（高性能 + 长期维护）

壳与系统层（Desktop First，兼顾未来 Mobile）

Tauri 2（Stable） + Rust：桌面端体积小、内存占用低、安全模型完善，且 2.x 已稳定并原生支持 iOS/Android，留好移动端拓展余地。
Tauri

WebView：Microsoft Edge WebView2（Windows 默认，用于内核托管）。
Winstall

前端 UI 层（重性能）

SolidJS + Vite 7 + TypeScript 5：Solid 信号模型在交互性能与内存占用上表现优异；Vite 7 启动与构建更快、默认目标更现代。
docs.solidjs.com
vitejs

Tailwind CSS v4（全新配置体系、更快的产物生成）。
Tailwind CSS

组件/样式：shadcn/ui 的 Solid 移植（solid-ui 或 shadcn-solid）+ Radix 思想的可访问性组件。
solid-ui.com
GitHub

列表虚拟化：TanStack Virtual（原生支持 Solid），用于超大数据集流畅滚动。
tanstack.com

Markdown 编辑/预览：CodeMirror 6（编辑）+ 你可在后端使用 comrak 渲染服务端预览（Rust）。

数据与搜索（本地优先，超快检索）

SQLite + SQLx（Rust 无运行时 ORM/DB 库，编译期校验 SQL；FTS5 可选）
GitHub

Tantivy 做全文检索（性能、索引规模优秀；中文可接 tantivy-jieba 分词）。
GitHub
Crates.io

Tauri 官方插件（稳定 + 高可用）

Filesystem、Dialog、Shell、Clipboard、Log、Window-State、Global-Shortcut、Updater 等，用官方 CLI 一键接入。
Tauri
+3
Tauri
+3
Tauri
+3
JonasKrückenberg
GitHub
Crates.io

工程化与质量

包管理：Node 22 LTS + pnpm（用 Corepack 启用/锁定版本）。
GitHub
pnpm.io

Lint/Format：Biome 2（类型感知 Lint，替代 ESLint + Prettier，极快）。
Biome

单测：Vitest 3（与 Vite 深度融合）。
vitest.dev

Rust 侧测试：cargo-nextest（更快的并行执行）。

CI/CD：GitHub Actions + tauri-action 一键出各平台安装包并发布。

3. 平台 & 运行时（后端/Rust）

Tauri 2（稳定版）：桌面（Win/macOS/Linux）与移动都支持，插件体系更完善；你需要的全局快捷键、窗口管理、事件总线都在 v2 里有现成 API/插件。
Tauri
+1

全局快捷键：@tauri-apps/plugin-global-shortcut。
Tauri
+1

窗口状态记忆/还原：@tauri-apps/plugin-window-state（适合多窗口工作台/灵感窗）。
Tauri
+1

更新器：@tauri-apps/plugin-updater（配 GitHub Release 或 CrabNebula Cloud 动态更新服务）。
Tauri
+2
Tauri
+2

剪贴板/捕捉：@tauri-apps/plugin-clipboard-manager（文本/HTML/图片）。
Tauri
+1

轻量 KV：@tauri-apps/plugin-store（做偏配置/偏缓存的数据）。
Tauri
+1

敏感信息加密：@tauri-apps/plugin-stronghold（令牌/密钥存放）。
Tauri
+1

Rust 1.81+：满足官方插件 MSRV 要求，并兼容常用观测/错误上报库（如 Sentry 的 MSRV 现已到 1.81）。
GitHub
+1

异步运行时：Tokio 1.46+（稳定、生态完备）。定时/后台任务可用 tokio-cron-scheduler。
GitHub
Crates.io

日志与可观测性：tracing / tracing-subscriber +（可选）sentry/sentry-tracing。
Docs.rs
+1
docs.sentry.io

数据层（SQLite 为主）

SQLite + SQLx（0.8 系列）：异步直连，SQL 直写，便于把你现有的表/索引“等价迁移”。必要时用 sqlite-unbundled 动态链接系统 SQLite。
Docs.rs
+1

（可选）SeaORM 1.0：如果更偏 ORM 与实体代码生成，1.x 已稳定且维护到 2025-10。
SeaQL
GitHub

全文检索：优先用 SQLite FTS5（一体化、部署简单）；若以后要做更复杂的排序/段位模型可切 Tantivy 0.24（Rust Lucene 同源思路）。
SQLite
GitHub

文本/Markdown & 知识网络

Markdown 解析：comrak（CommonMark+GFM，近年活跃迭代）。用于后端抽取标题、出链/回链、摘要等。
Release Monitoring
GitHub

前端编辑器：继续 Milkdown 或转 CodeMirror 6（更易做“所见即所得 + Markdown”混合模式，插件活跃）。
Milkdown
CodeMirror

前端（UI 壳）

React 19（稳定版）：已稳定发布，生态适配到位。
React

构建工具：Vite 7（Node 20/22），生态跟进良好；若暂不升级也可先锁 Vite 6。
GitHub
vitejs

样式：Tailwind CSS v4（稳定）。
Tailwind CSS
+1

状态管理：TanStack Query 管“服务端状态”（IPC/DB 数据），Zustand 管“UI/临时状态”。这是 2025 年 React 社区的主流组合。
SVAR
Bugra Gulculer

与 Rust 类型对齐：tauri-specta v2（自动导出 TS 类型 & 事件类型，让 invoke/事件彻底类型安全）。
GitHub
Docs.rs

测试、交付与更新

Rust 测试：cargo-nextest（更快并发、重试/超时控制好用）。
nexte.st

前端单元：Vitest 3.x；端到端：Playwright（活跃更新）。
vitest.dev
npm

CI/CD：GitHub Actions + 官方 tauri-action 发布到 GitHub Releases；或用 CrabNebula Cloud 托管多通道与自动更新。