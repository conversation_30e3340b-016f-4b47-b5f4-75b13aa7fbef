// 仓储接口 - 定义数据访问的抽象接口
// 领域层定义接口，基础设施层实现具体的数据访问逻辑

use crate::shared::errors::AppResult;
use async_trait::async_trait;

pub mod project_repository;
pub mod task_repository;
pub mod area_repository;
pub mod user_repository;
pub mod metric_repository;

// 重新导出所有仓储接口
pub use project_repository::*;
pub use task_repository::*;
pub use area_repository::*;
pub use user_repository::*;
pub use metric_repository::*;
