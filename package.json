{"name": "pao<PERSON>", "version": "0.1.0", "description": "", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "lint": "biome check .", "lint:fix": "biome check --apply .", "format": "biome format --write .", "type-check": "tsc --noEmit", "clean": "rm -rf dist node_modules/.vite src-tauri/target", "deps:update": "pnpm update --latest"}, "license": "MIT", "dependencies": {"solid-js": "^1.9.3", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "@tauri-apps/plugin-global-shortcut": "^2", "@tauri-apps/plugin-window-state": "^2", "@tauri-apps/plugin-updater": "^2", "@tauri-apps/plugin-clipboard-manager": "^2", "@tauri-apps/plugin-store": "^2", "@tauri-apps/plugin-fs": "^2", "@tauri-apps/plugin-dialog": "^2", "@tauri-apps/plugin-shell": "^2", "@kobalte/core": "^0.13.0", "solid-ui": "^0.1.0", "@corvu/primitives": "^0.4.0", "tailwindcss": "^4.0.0", "class-variance-authority": "^0.7.0", "tailwind-merge": "^2.0.0", "clsx": "^2.0.0", "@tabler/icons-solidjs": "^3.0.0", "lucide-solid": "^0.1.0", "@motionone/solid": "^10.16.0", "@solid-primitives/intersection-observer": "^2.1.0", "@unovis/solid": "^1.4.0", "solid-chartjs": "^1.3.0", "@tanstack/solid-virtual": "^3.0.0", "@solid-primitives/resize-observer": "^2.0.0", "@codemirror/state": "^6.4.0", "@codemirror/view": "^6.23.0", "@codemirror/lang-markdown": "^6.2.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.0", "@tanstack/solid-query": "^5.0.0", "solid-primitives": "^1.8.0", "date-fns": "^3.0.0", "fuse.js": "^7.0.0"}, "devDependencies": {"vite": "^6.0.3", "vite-plugin-solid": "^2.11.0", "@types/node": "^22.0.0", "typescript": "~5.6.2", "@biomejs/biome": "^1.9.0", "vitest": "^3.0.0", "@vitest/ui": "^3.0.0", "@solidjs/testing-library": "^0.8.0", "jsdom": "^25.0.0", "@tauri-apps/cli": "^2"}}