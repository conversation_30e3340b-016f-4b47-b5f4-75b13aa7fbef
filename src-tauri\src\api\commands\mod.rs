// Tauri命令定义
// 实现前后端通信的具体命令

pub mod user_commands;
pub mod project_commands;
pub mod task_commands;
pub mod area_commands;
pub mod auth_commands;

// 重新导出所有命令
pub use user_commands::*;
pub use project_commands::*;
pub use task_commands::*;
pub use area_commands::*;
pub use auth_commands::*;

use crate::api::{ApiResponse, ApiState};
use crate::application::services::ServiceContext;
use crate::shared::errors::AppResult;
use tauri::State;

/// 获取所有Tauri命令
pub fn get_all_commands() -> Vec<tauri::command::CommandItem> {
    vec![
        // 认证命令
        auth_commands::login,
        auth_commands::logout,
        auth_commands::register,
        auth_commands::get_current_user,
        
        // 用户命令
        user_commands::create_user,
        user_commands::get_user,
        user_commands::update_user,
        user_commands::delete_user,
        user_commands::list_users,
        user_commands::update_password,
        
        // 项目命令
        project_commands::create_project,
        project_commands::get_project,
        project_commands::update_project,
        project_commands::delete_project,
        project_commands::list_projects,
        project_commands::update_project_status,
        project_commands::get_project_stats,
        project_commands::search_projects,
        
        // 任务命令
        task_commands::create_task,
        task_commands::get_task,
        task_commands::update_task,
        task_commands::delete_task,
        task_commands::list_tasks,
        task_commands::update_task_status,
        task_commands::get_task_stats,
        task_commands::search_tasks,
        
        // 领域命令
        area_commands::create_area,
        area_commands::get_area,
        area_commands::update_area,
        area_commands::delete_area,
        area_commands::list_areas,
        area_commands::get_area_summary,
        area_commands::search_areas,
    ]
}

/// 命令工具函数
pub struct CommandUtils;

impl CommandUtils {
    /// 创建服务上下文
    pub fn create_service_context(user_id: Option<String>) -> ServiceContext {
        ServiceContext::new(user_id)
    }

    /// 验证用户认证状态
    pub fn validate_authentication(user_id: Option<String>) -> AppResult<String> {
        user_id.ok_or_else(|| {
            crate::shared::errors::AppError::unauthorized("用户未认证")
        })
    }

    /// 记录命令执行
    pub fn log_command_execution(
        command_name: &str,
        user_id: Option<&str>,
        success: bool,
        duration_ms: u64,
    ) {
        if success {
            tracing::info!(
                command = %command_name,
                user_id = ?user_id,
                duration_ms = %duration_ms,
                "Command executed successfully"
            );
        } else {
            tracing::warn!(
                command = %command_name,
                user_id = ?user_id,
                duration_ms = %duration_ms,
                "Command execution failed"
            );
        }
    }

    /// 处理命令错误
    pub fn handle_command_error<T>(
        error: crate::shared::errors::AppError,
        command_name: &str,
    ) -> ApiResponse<T> {
        tracing::error!(
            command = %command_name,
            error = %error,
            "Command failed with error"
        );

        match error {
            crate::shared::errors::AppError::Validation { message } => {
                ApiResponse::error(format!("验证错误: {}", message))
            }
            crate::shared::errors::AppError::NotFound { resource } => {
                ApiResponse::error(format!("资源未找到: {}", resource))
            }
            crate::shared::errors::AppError::Unauthorized { message } => {
                ApiResponse::error(format!("未授权: {}", message))
            }
            crate::shared::errors::AppError::Forbidden { message } => {
                ApiResponse::error(format!("禁止访问: {}", message))
            }
            crate::shared::errors::AppError::Database { message } => {
                ApiResponse::error(format!("数据库错误: {}", message))
            }
            crate::shared::errors::AppError::BusinessLogic { message } => {
                ApiResponse::error(format!("业务逻辑错误: {}", message))
            }
            crate::shared::errors::AppError::NotImplemented { message } => {
                ApiResponse::error(format!("功能未实现: {}", message))
            }
            crate::shared::errors::AppError::Internal { message } => {
                ApiResponse::error(format!("内部错误: {}", message))
            }
        }
    }
}

/// 命令执行宏
#[macro_export]
macro_rules! execute_command {
    ($command_name:expr, $user_id:expr, $operation:expr) => {{
        let start = std::time::Instant::now();
        let result = $operation.await;
        let duration = start.elapsed().as_millis() as u64;
        
        match result {
            Ok(service_result) => {
                CommandUtils::log_command_execution($command_name, $user_id.as_deref(), true, duration);
                ApiResponse::success(service_result.data)
                    .with_request_id(crate::shared::utils::generate_id())
            }
            Err(error) => {
                CommandUtils::log_command_execution($command_name, $user_id.as_deref(), false, duration);
                CommandUtils::handle_command_error(error, $command_name)
            }
        }
    }};
}

/// 认证命令执行宏
#[macro_export]
macro_rules! execute_authenticated_command {
    ($command_name:expr, $user_id:expr, $operation:expr) => {{
        match CommandUtils::validate_authentication($user_id.clone()) {
            Ok(authenticated_user_id) => {
                execute_command!($command_name, Some(authenticated_user_id), $operation)
            }
            Err(error) => {
                CommandUtils::handle_command_error(error, $command_name)
            }
        }
    }};
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_service_context() {
        let context = CommandUtils::create_service_context(Some("user123".to_string()));
        assert!(context.is_authenticated());
        assert_eq!(context.user_id().unwrap(), "user123");
    }

    #[test]
    fn test_validate_authentication_success() {
        let result = CommandUtils::validate_authentication(Some("user123".to_string()));
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "user123");
    }

    #[test]
    fn test_validate_authentication_failure() {
        let result = CommandUtils::validate_authentication(None);
        assert!(result.is_err());
    }

    #[test]
    fn test_get_all_commands_count() {
        let commands = get_all_commands();
        // 验证命令数量是否符合预期
        assert!(commands.len() > 20); // 至少应该有20个以上的命令
    }
}
