// 请求DTO定义
// 定义所有API请求的数据结构

use super::{DtoValidate, DtoUtils, PaginationDto, DateRangeDto};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 用户注册请求
#[derive(Debug, Clone, Deserialize)]
pub struct RegisterRequestDto {
    pub username: String,
    pub email: Option<String>,
    pub password: String,
    pub full_name: Option<String>,
    pub timezone: Option<String>,
    pub language: Option<String>,
}

impl DtoValidate for RegisterRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        // 验证用户名
        if let Err(e) = DtoUtils::validate_string_length(&self.username, "用户名", Some(3), Some(50)) {
            errors.push(e);
        }

        // 验证密码
        if let Err(e) = DtoUtils::validate_string_length(&self.password, "密码", Some(8), Some(128)) {
            errors.push(e);
        }

        // 验证邮箱
        if let Some(email) = &self.email {
            if let Err(e) = DtoUtils::validate_email(email) {
                errors.push(e);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 用户登录请求
#[derive(Debug, Clone, Deserialize)]
pub struct LoginRequestDto {
    pub username: String,
    pub password: String,
    pub remember_me: Option<bool>,
}

impl DtoValidate for LoginRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if self.username.trim().is_empty() {
            errors.push("用户名不能为空".to_string());
        }

        if self.password.trim().is_empty() {
            errors.push("密码不能为空".to_string());
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 密码更改请求
#[derive(Debug, Clone, Deserialize)]
pub struct ChangePasswordRequestDto {
    pub current_password: String,
    pub new_password: String,
    pub confirm_password: String,
}

impl DtoValidate for ChangePasswordRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if self.current_password.trim().is_empty() {
            errors.push("当前密码不能为空".to_string());
        }

        if let Err(e) = DtoUtils::validate_string_length(&self.new_password, "新密码", Some(8), Some(128)) {
            errors.push(e);
        }

        if self.new_password != self.confirm_password {
            errors.push("新密码和确认密码不匹配".to_string());
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 用户更新请求
#[derive(Debug, Clone, Deserialize)]
pub struct UpdateUserRequestDto {
    pub full_name: Option<String>,
    pub email: Option<String>,
    pub avatar_url: Option<String>,
    pub timezone: Option<String>,
    pub language: Option<String>,
}

impl DtoValidate for UpdateUserRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if let Some(email) = &self.email {
            if let Err(e) = DtoUtils::validate_email(email) {
                errors.push(e);
            }
        }

        if let Some(avatar_url) = &self.avatar_url {
            if let Err(e) = DtoUtils::validate_url(avatar_url) {
                errors.push(e);
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 项目创建请求
#[derive(Debug, Clone, Deserialize)]
pub struct CreateProjectRequestDto {
    pub name: String,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub start_date: Option<chrono::NaiveDate>,
    pub deadline: Option<chrono::NaiveDate>,
    pub estimated_hours: Option<u32>,
    pub area_id: Option<String>,
}

impl DtoValidate for CreateProjectRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if let Err(e) = DtoUtils::validate_string_length(&self.name, "项目名称", Some(1), Some(200)) {
            errors.push(e);
        }

        if let Some(description) = &self.description {
            if let Err(e) = DtoUtils::validate_string_length(description, "项目描述", None, Some(2000)) {
                errors.push(e);
            }
        }

        if let Some(priority) = &self.priority {
            if !["low", "medium", "high", "urgent"].contains(&priority.as_str()) {
                errors.push("优先级必须是 low, medium, high, urgent 之一".to_string());
            }
        }

        if let (Some(start), Some(end)) = (self.start_date, self.deadline) {
            if start > end {
                errors.push("开始日期不能晚于截止日期".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 项目更新请求
#[derive(Debug, Clone, Deserialize)]
pub struct UpdateProjectRequestDto {
    pub name: Option<String>,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub start_date: Option<chrono::NaiveDate>,
    pub deadline: Option<chrono::NaiveDate>,
    pub estimated_hours: Option<u32>,
    pub area_id: Option<String>,
}

impl DtoValidate for UpdateProjectRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if let Some(name) = &self.name {
            if let Err(e) = DtoUtils::validate_string_length(name, "项目名称", Some(1), Some(200)) {
                errors.push(e);
            }
        }

        if let Some(description) = &self.description {
            if let Err(e) = DtoUtils::validate_string_length(description, "项目描述", None, Some(2000)) {
                errors.push(e);
            }
        }

        if let Some(priority) = &self.priority {
            if !["low", "medium", "high", "urgent"].contains(&priority.as_str()) {
                errors.push("优先级必须是 low, medium, high, urgent 之一".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 任务创建请求
#[derive(Debug, Clone, Deserialize)]
pub struct CreateTaskRequestDto {
    pub title: String,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub due_date: Option<chrono::NaiveDate>,
    pub estimated_minutes: Option<u32>,
}

impl DtoValidate for CreateTaskRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if let Err(e) = DtoUtils::validate_string_length(&self.title, "任务标题", Some(1), Some(200)) {
            errors.push(e);
        }

        if let Some(description) = &self.description {
            if let Err(e) = DtoUtils::validate_string_length(description, "任务描述", None, Some(2000)) {
                errors.push(e);
            }
        }

        if let Some(priority) = &self.priority {
            if !["low", "medium", "high", "urgent"].contains(&priority.as_str()) {
                errors.push("优先级必须是 low, medium, high, urgent 之一".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 任务更新请求
#[derive(Debug, Clone, Deserialize)]
pub struct UpdateTaskRequestDto {
    pub title: Option<String>,
    pub description: Option<String>,
    pub priority: Option<String>,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub area_id: Option<String>,
    pub assigned_to: Option<String>,
    pub due_date: Option<chrono::NaiveDate>,
    pub estimated_minutes: Option<u32>,
    pub completion_percentage: Option<u8>,
}

impl DtoValidate for UpdateTaskRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if let Some(title) = &self.title {
            if let Err(e) = DtoUtils::validate_string_length(title, "任务标题", Some(1), Some(200)) {
                errors.push(e);
            }
        }

        if let Some(completion) = self.completion_percentage {
            if completion > 100 {
                errors.push("完成度不能超过100%".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 领域创建请求
#[derive(Debug, Clone, Deserialize)]
pub struct CreateAreaRequestDto {
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>,
    pub icon: Option<String>,
}

impl DtoValidate for CreateAreaRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if let Err(e) = DtoUtils::validate_string_length(&self.name, "领域名称", Some(1), Some(100)) {
            errors.push(e);
        }

        if let Some(description) = &self.description {
            if let Err(e) = DtoUtils::validate_string_length(description, "领域描述", None, Some(500)) {
                errors.push(e);
            }
        }

        if let Some(color) = &self.color {
            if !color.starts_with('#') || color.len() != 7 {
                errors.push("颜色必须是有效的十六进制颜色代码".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 领域更新请求
#[derive(Debug, Clone, Deserialize)]
pub struct UpdateAreaRequestDto {
    pub name: Option<String>,
    pub description: Option<String>,
    pub color: Option<String>,
    pub icon: Option<String>,
}

impl DtoValidate for UpdateAreaRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if let Some(name) = &self.name {
            if let Err(e) = DtoUtils::validate_string_length(name, "领域名称", Some(1), Some(100)) {
                errors.push(e);
            }
        }

        if let Some(color) = &self.color {
            if !color.starts_with('#') || color.len() != 7 {
                errors.push("颜色必须是有效的十六进制颜色代码".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 搜索请求
#[derive(Debug, Clone, Deserialize)]
pub struct SearchRequestDto {
    pub keyword: String,
    #[serde(flatten)]
    pub pagination: PaginationDto,
    pub entity_type: Option<String>,
    pub filters: Option<HashMap<String, serde_json::Value>>,
}

impl DtoValidate for SearchRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if self.keyword.trim().is_empty() {
            errors.push("搜索关键词不能为空".to_string());
        }

        if let Err(e) = self.pagination.validate() {
            errors.push(e);
        }

        if let Some(entity_type) = &self.entity_type {
            if !["project", "task", "area", "user"].contains(&entity_type.as_str()) {
                errors.push("实体类型必须是 project, task, area, user 之一".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 统计请求
#[derive(Debug, Clone, Deserialize)]
pub struct StatsRequestDto {
    #[serde(flatten)]
    pub date_range: DateRangeDto,
    pub group_by: Option<String>,
    pub metrics: Option<Vec<String>>,
}

impl DtoValidate for StatsRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if let Err(e) = self.date_range.validate() {
            errors.push(e);
        }

        if let Some(group_by) = &self.group_by {
            if !["day", "week", "month", "year", "status", "priority"].contains(&group_by.as_str()) {
                errors.push("分组方式必须是 day, week, month, year, status, priority 之一".to_string());
            }
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 批量状态更新请求
#[derive(Debug, Clone, Deserialize)]
pub struct BatchStatusUpdateRequestDto {
    pub ids: Vec<String>,
    pub status: String,
    pub reason: Option<String>,
}

impl DtoValidate for BatchStatusUpdateRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if self.ids.is_empty() {
            errors.push("ID列表不能为空".to_string());
        }

        if self.ids.len() > 100 {
            errors.push("批量操作不能超过100个项目".to_string());
        }

        if self.status.trim().is_empty() {
            errors.push("状态不能为空".to_string());
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

/// 文件上传请求
#[derive(Debug, Clone, Deserialize)]
pub struct FileUploadRequestDto {
    pub file_name: String,
    pub file_size: u64,
    pub content_type: String,
    pub purpose: String,
}

impl DtoValidate for FileUploadRequestDto {
    fn validate(&self) -> Result<(), Vec<String>> {
        let mut errors = Vec::new();

        if self.file_name.trim().is_empty() {
            errors.push("文件名不能为空".to_string());
        }

        if self.file_size == 0 {
            errors.push("文件大小不能为0".to_string());
        }

        if self.file_size > 10 * 1024 * 1024 {
            errors.push("文件大小不能超过10MB".to_string());
        }

        let allowed_types = ["image/jpeg", "image/png", "image/gif", "application/pdf", "text/plain"];
        if !allowed_types.contains(&self.content_type.as_str()) {
            errors.push("不支持的文件类型".to_string());
        }

        if !["avatar", "attachment", "export"].contains(&self.purpose.as_str()) {
            errors.push("文件用途必须是 avatar, attachment, export 之一".to_string());
        }

        if errors.is_empty() {
            Ok(())
        } else {
            Err(errors)
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_register_request_validation() {
        let valid_request = RegisterRequestDto {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "password123".to_string(),
            full_name: Some("Test User".to_string()),
            timezone: None,
            language: None,
        };
        assert!(valid_request.validate().is_ok());

        let invalid_request = RegisterRequestDto {
            username: "ab".to_string(), // 太短
            email: Some("invalid-email".to_string()), // 无效邮箱
            password: "123".to_string(), // 太短
            full_name: None,
            timezone: None,
            language: None,
        };
        assert!(invalid_request.validate().is_err());
    }

    #[test]
    fn test_create_project_request_validation() {
        let valid_request = CreateProjectRequestDto {
            name: "Test Project".to_string(),
            description: Some("A test project".to_string()),
            priority: Some("high".to_string()),
            start_date: Some(chrono::NaiveDate::from_ymd_opt(2024, 1, 1).unwrap()),
            deadline: Some(chrono::NaiveDate::from_ymd_opt(2024, 12, 31).unwrap()),
            estimated_hours: Some(100),
            area_id: None,
        };
        assert!(valid_request.validate().is_ok());

        let invalid_request = CreateProjectRequestDto {
            name: "".to_string(), // 空名称
            description: None,
            priority: Some("invalid".to_string()), // 无效优先级
            start_date: Some(chrono::NaiveDate::from_ymd_opt(2024, 12, 31).unwrap()),
            deadline: Some(chrono::NaiveDate::from_ymd_opt(2024, 1, 1).unwrap()), // 日期顺序错误
            estimated_hours: None,
            area_id: None,
        };
        assert!(invalid_request.validate().is_err());
    }

    #[test]
    fn test_file_upload_request_validation() {
        let valid_request = FileUploadRequestDto {
            file_name: "test.jpg".to_string(),
            file_size: 1024 * 1024, // 1MB
            content_type: "image/jpeg".to_string(),
            purpose: "avatar".to_string(),
        };
        assert!(valid_request.validate().is_ok());

        let invalid_request = FileUploadRequestDto {
            file_name: "".to_string(), // 空文件名
            file_size: 20 * 1024 * 1024, // 超过限制
            content_type: "application/exe".to_string(), // 不支持的类型
            purpose: "invalid".to_string(), // 无效用途
        };
        assert!(invalid_request.validate().is_err());
    }
}
