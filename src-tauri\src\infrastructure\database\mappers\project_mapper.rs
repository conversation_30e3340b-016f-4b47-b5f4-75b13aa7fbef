// 项目对象映射器
// 负责在领域实体和数据库模型之间进行转换

use crate::domain::entities::project::{Project, CreateProjectData, UpdateProjectData};
use crate::infrastructure::database::models::{ProjectModel, CreateProjectModel, UpdateProjectModel};
use crate::shared::errors::{AppError, AppResult};

/// 项目映射器
pub struct ProjectMapper;

impl ProjectMapper {
    /// 将数据库模型转换为领域实体
    pub fn to_domain(model: ProjectModel) -> AppResult<Project> {
        Project::new(
            model.id,
            model.name,
            model.description,
            model.status,
            model.progress,
            model.priority,
            model.start_date,
            model.deadline,
            model.estimated_hours,
            model.actual_hours,
            model.area_id,
            model.created_by,
            model.created_at,
            model.updated_at,
            model.archived_at,
        )
    }

    /// 将领域实体转换为数据库模型
    pub fn to_model(project: &Project) -> ProjectModel {
        ProjectModel {
            id: project.id().clone(),
            name: project.name().clone(),
            description: project.description().clone(),
            status: project.status().to_string(),
            progress: project.progress(),
            priority: project.priority().to_i32(),
            start_date: project.start_date(),
            deadline: project.deadline(),
            estimated_hours: project.estimated_hours(),
            actual_hours: project.actual_hours(),
            area_id: project.area_id().clone(),
            created_by: project.created_by().clone(),
            created_at: project.created_at(),
            updated_at: project.updated_at(),
            archived_at: project.archived_at(),
        }
    }

    /// 将创建数据转换为数据库创建模型
    pub fn create_data_to_model(data: CreateProjectData, id: String) -> CreateProjectModel {
        let mut model = CreateProjectModel::new(
            id,
            data.name,
            data.description,
            data.priority.to_i32(),
            data.area_id,
            data.created_by,
        );

        if let Some(start_date) = data.start_date {
            model = model.with_start_date(start_date);
        }

        if let Some(deadline) = data.deadline {
            model = model.with_deadline(deadline);
        }

        if let Some(estimated_hours) = data.estimated_hours {
            model = model.with_estimated_hours(estimated_hours);
        }

        model
    }

    /// 将更新数据转换为数据库更新模型
    pub fn update_data_to_model(data: UpdateProjectData) -> UpdateProjectModel {
        let mut model = UpdateProjectModel::new();

        if let Some(name) = data.name {
            model = model.with_name(name);
        }

        if let Some(description) = data.description {
            model.description = description;
        }

        if let Some(status) = data.status {
            model = model.with_status(status.to_string());
        }

        if let Some(progress) = data.progress {
            model = model.with_progress(progress);
        }

        if let Some(priority) = data.priority {
            model = model.with_priority(priority.to_i32());
        }

        if let Some(start_date) = data.start_date {
            model.start_date = start_date;
        }

        if let Some(deadline) = data.deadline {
            model.deadline = deadline;
        }

        if let Some(estimated_hours) = data.estimated_hours {
            model.estimated_hours = estimated_hours;
        }

        if let Some(actual_hours) = data.actual_hours {
            model.actual_hours = Some(actual_hours);
        }

        if let Some(area_id) = data.area_id {
            model.area_id = area_id;
        }

        if data.archived {
            model = model.archive();
        }

        model
    }

    /// 批量转换数据库模型为领域实体
    pub fn to_domain_list(models: Vec<ProjectModel>) -> AppResult<Vec<Project>> {
        models
            .into_iter()
            .map(Self::to_domain)
            .collect::<AppResult<Vec<_>>>()
    }

    /// 批量转换领域实体为数据库模型
    pub fn to_model_list(projects: &[Project]) -> Vec<ProjectModel> {
        projects.iter().map(Self::to_model).collect()
    }

    /// 验证项目数据完整性
    pub fn validate_project_data(model: &ProjectModel) -> AppResult<()> {
        // 验证项目名称
        if model.name.trim().is_empty() {
            return Err(AppError::validation("项目名称不能为空"));
        }

        if model.name.len() > 200 {
            return Err(AppError::validation("项目名称不能超过200个字符"));
        }

        // 验证描述长度
        if let Some(description) = &model.description {
            if description.len() > 2000 {
                return Err(AppError::validation("项目描述不能超过2000个字符"));
            }
        }

        // 验证状态
        let valid_statuses = ["not_started", "in_progress", "at_risk", "paused", "completed", "archived"];
        if !valid_statuses.contains(&model.status.as_str()) {
            return Err(AppError::validation("无效的项目状态"));
        }

        // 验证进度
        if model.progress < 0 || model.progress > 100 {
            return Err(AppError::validation("项目进度必须在0-100之间"));
        }

        // 验证优先级
        if model.priority < 1 || model.priority > 4 {
            return Err(AppError::validation("项目优先级必须在1-4之间"));
        }

        // 验证时间逻辑
        if let (Some(start_date), Some(deadline)) = (model.start_date, model.deadline) {
            if start_date > deadline {
                return Err(AppError::validation("开始日期不能晚于截止日期"));
            }
        }

        // 验证工时
        if let Some(estimated_hours) = model.estimated_hours {
            if estimated_hours < 0 {
                return Err(AppError::validation("预估工时不能为负数"));
            }
        }

        if model.actual_hours < 0 {
            return Err(AppError::validation("实际工时不能为负数"));
        }

        // 验证创建者
        if model.created_by.trim().is_empty() {
            return Err(AppError::validation("创建者不能为空"));
        }

        Ok(())
    }

    /// 验证创建项目数据
    pub fn validate_create_data(data: &CreateProjectData) -> AppResult<()> {
        // 验证项目名称
        if data.name.trim().is_empty() {
            return Err(AppError::validation("项目名称不能为空"));
        }

        if data.name.len() > 200 {
            return Err(AppError::validation("项目名称不能超过200个字符"));
        }

        // 验证描述
        if let Some(description) = &data.description {
            if description.len() > 2000 {
                return Err(AppError::validation("项目描述不能超过2000个字符"));
            }
        }

        // 验证时间逻辑
        if let (Some(start_date), Some(deadline)) = (data.start_date, data.deadline) {
            if start_date > deadline {
                return Err(AppError::validation("开始日期不能晚于截止日期"));
            }
        }

        // 验证预估工时
        if let Some(estimated_hours) = data.estimated_hours {
            if estimated_hours < 0 {
                return Err(AppError::validation("预估工时不能为负数"));
            }
        }

        // 验证创建者
        if data.created_by.trim().is_empty() {
            return Err(AppError::validation("创建者不能为空"));
        }

        Ok(())
    }

    /// 验证更新项目数据
    pub fn validate_update_data(data: &UpdateProjectData) -> AppResult<()> {
        // 验证项目名称
        if let Some(name) = &data.name {
            if name.trim().is_empty() {
                return Err(AppError::validation("项目名称不能为空"));
            }
            if name.len() > 200 {
                return Err(AppError::validation("项目名称不能超过200个字符"));
            }
        }

        // 验证描述
        if let Some(Some(description)) = &data.description {
            if description.len() > 2000 {
                return Err(AppError::validation("项目描述不能超过2000个字符"));
            }
        }

        // 验证进度
        if let Some(progress) = data.progress {
            if progress < 0 || progress > 100 {
                return Err(AppError::validation("项目进度必须在0-100之间"));
            }
        }

        // 验证工时
        if let Some(Some(estimated_hours)) = data.estimated_hours {
            if estimated_hours < 0 {
                return Err(AppError::validation("预估工时不能为负数"));
            }
        }

        if let Some(actual_hours) = data.actual_hours {
            if actual_hours < 0 {
                return Err(AppError::validation("实际工时不能为负数"));
            }
        }

        Ok(())
    }

    /// 清理项目数据
    pub fn sanitize_create_data(mut data: CreateProjectData) -> CreateProjectData {
        data.name = data.name.trim().to_string();
        
        if let Some(description) = data.description {
            data.description = Some(description.trim().to_string());
        }

        data.created_by = data.created_by.trim().to_string();

        data
    }

    /// 清理更新数据
    pub fn sanitize_update_data(mut data: UpdateProjectData) -> UpdateProjectData {
        if let Some(name) = data.name {
            data.name = Some(name.trim().to_string());
        }

        if let Some(Some(description)) = data.description {
            data.description = Some(Some(description.trim().to_string()));
        }

        data
    }

    /// 检查项目数据是否发生变化
    pub fn has_changes(original: &Project, update_data: &UpdateProjectData) -> bool {
        if let Some(name) = &update_data.name {
            if original.name() != name {
                return true;
            }
        }

        if let Some(description) = &update_data.description {
            if original.description() != description {
                return true;
            }
        }

        if let Some(status) = &update_data.status {
            if original.status() != status {
                return true;
            }
        }

        if let Some(progress) = update_data.progress {
            if original.progress() != progress {
                return true;
            }
        }

        if let Some(priority) = &update_data.priority {
            if original.priority() != priority {
                return true;
            }
        }

        if let Some(start_date) = update_data.start_date {
            if original.start_date() != start_date {
                return true;
            }
        }

        if let Some(deadline) = update_data.deadline {
            if original.deadline() != deadline {
                return true;
            }
        }

        if let Some(estimated_hours) = update_data.estimated_hours {
            if original.estimated_hours() != estimated_hours {
                return true;
            }
        }

        if let Some(actual_hours) = update_data.actual_hours {
            if original.actual_hours() != actual_hours {
                return true;
            }
        }

        if let Some(area_id) = &update_data.area_id {
            if original.area_id() != area_id {
                return true;
            }
        }

        if update_data.archived && original.archived_at().is_none() {
            return true;
        }

        false
    }

    /// 验证项目状态转换的合法性
    pub fn validate_status_transition(
        current_status: &str,
        new_status: &str,
    ) -> AppResult<()> {
        use std::collections::HashMap;

        // 定义允许的状态转换
        let mut allowed_transitions: HashMap<&str, Vec<&str>> = HashMap::new();
        allowed_transitions.insert("not_started", vec!["in_progress", "paused", "cancelled"]);
        allowed_transitions.insert("in_progress", vec!["at_risk", "paused", "completed", "cancelled"]);
        allowed_transitions.insert("at_risk", vec!["in_progress", "paused", "completed", "cancelled"]);
        allowed_transitions.insert("paused", vec!["in_progress", "at_risk", "cancelled"]);
        allowed_transitions.insert("completed", vec!["archived"]);
        allowed_transitions.insert("cancelled", vec!["not_started", "archived"]);
        allowed_transitions.insert("archived", vec![]); // 归档后不能再转换

        if current_status == new_status {
            return Ok(()); // 相同状态总是允许的
        }

        if let Some(allowed) = allowed_transitions.get(current_status) {
            if allowed.contains(&new_status) {
                Ok(())
            } else {
                Err(AppError::validation(&format!(
                    "不能从状态 '{}' 转换到 '{}'",
                    current_status, new_status
                )))
            }
        } else {
            Err(AppError::validation(&format!(
                "未知的项目状态: '{}'",
                current_status
            )))
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::domain::value_objects::Priority;
    use chrono::Utc;

    #[test]
    fn test_project_mapping() {
        let model = ProjectModel {
            id: "proj123".to_string(),
            name: "Test Project".to_string(),
            description: Some("A test project".to_string()),
            status: "in_progress".to_string(),
            progress: 50,
            priority: 2,
            start_date: None,
            deadline: None,
            estimated_hours: Some(40),
            actual_hours: 20,
            area_id: Some("area123".to_string()),
            created_by: "user123".to_string(),
            created_at: Utc::now(),
            updated_at: Utc::now(),
            archived_at: None,
        };

        // 测试模型到领域实体的转换
        let project = ProjectMapper::to_domain(model.clone()).unwrap();
        assert_eq!(project.id(), &model.id);
        assert_eq!(project.name(), &model.name);
        assert_eq!(project.progress(), model.progress);

        // 测试领域实体到模型的转换
        let converted_model = ProjectMapper::to_model(&project);
        assert_eq!(converted_model.id, model.id);
        assert_eq!(converted_model.name, model.name);
        assert_eq!(converted_model.progress, model.progress);
    }

    #[test]
    fn test_create_data_validation() {
        let valid_data = CreateProjectData {
            name: "Test Project".to_string(),
            description: Some("A test project".to_string()),
            priority: Priority::Medium,
            start_date: None,
            deadline: None,
            estimated_hours: Some(40),
            area_id: Some("area123".to_string()),
            created_by: "user123".to_string(),
        };

        assert!(ProjectMapper::validate_create_data(&valid_data).is_ok());

        // 测试空名称
        let invalid_data = CreateProjectData {
            name: "".to_string(),
            ..valid_data.clone()
        };
        assert!(ProjectMapper::validate_create_data(&invalid_data).is_err());

        // 测试负数工时
        let invalid_data = CreateProjectData {
            estimated_hours: Some(-10),
            ..valid_data.clone()
        };
        assert!(ProjectMapper::validate_create_data(&invalid_data).is_err());
    }

    #[test]
    fn test_status_transition_validation() {
        // 测试有效转换
        assert!(ProjectMapper::validate_status_transition("not_started", "in_progress").is_ok());
        assert!(ProjectMapper::validate_status_transition("in_progress", "completed").is_ok());
        assert!(ProjectMapper::validate_status_transition("completed", "archived").is_ok());

        // 测试无效转换
        assert!(ProjectMapper::validate_status_transition("completed", "in_progress").is_err());
        assert!(ProjectMapper::validate_status_transition("archived", "in_progress").is_err());

        // 测试相同状态
        assert!(ProjectMapper::validate_status_transition("in_progress", "in_progress").is_ok());
    }

    #[test]
    fn test_data_sanitization() {
        let data = CreateProjectData {
            name: "  Test Project  ".to_string(),
            description: Some("  A test project  ".to_string()),
            priority: Priority::Medium,
            start_date: None,
            deadline: None,
            estimated_hours: None,
            area_id: None,
            created_by: "  user123  ".to_string(),
        };

        let sanitized = ProjectMapper::sanitize_create_data(data);
        assert_eq!(sanitized.name, "Test Project");
        assert_eq!(sanitized.description, Some("A test project".to_string()));
        assert_eq!(sanitized.created_by, "user123");
    }
}
