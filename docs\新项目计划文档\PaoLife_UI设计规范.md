# PaoLife UI设计规范文档

## 项目概览

### 设计理念
PaoLife基于P.A.R.A.方法论的个人效能管理应用，UI设计需要体现：
- **清晰的信息层次**: 支持Projects、Areas、Resources、Archive四象限管理
- **高效的工作流**: 减少认知负担，提升操作效率
- **专业的视觉体验**: 现代化、简洁、专注的设计风格
- **响应式适配**: 支持不同屏幕尺寸和设备类型

### 目标用户
- **知识工作者**: 需要管理复杂项目和任务
- **效能追求者**: 关注个人生产力提升
- **长期用户**: 需要稳定、可靠的日常工具

## 第一部分：UI库选择和技术栈

### 1. 推荐UI库组合

基于重构计划中的SolidJS技术栈，推荐以下UI库组合：

#### 核心UI库
```typescript
// 主要UI组件库
"@kobalte/core": "^0.13.0"        // 无头UI组件，完全可访问性
"solid-ui": "^0.1.0"              // SolidJS版本的shadcn/ui
"@corvu/primitives": "^0.4.0"     // 高质量的SolidJS原语组件

// 样式系统
"tailwindcss": "^4.0.0"           // Tailwind CSS v4
"class-variance-authority": "^0.7.0" // 组件变体管理
"tailwind-merge": "^2.0.0"        // 类名合并工具
"clsx": "^2.0.0"                  // 条件类名工具

// 图标系统
"@tabler/icons-solidjs": "^3.0.0" // 现代化图标库
"lucide-solid": "^0.1.0"          // 简洁线性图标

// 动画和交互
"@motionone/solid": "^10.16.0"    // 高性能动画库
"@solid-primitives/intersection-observer": "^2.1.0" // 交互观察
```

#### 专业组件库
```typescript
// 数据可视化
"@unovis/solid": "^1.4.0"         // 现代数据可视化
"solid-chartjs": "^1.3.0"         // Chart.js的SolidJS包装

// 虚拟化和性能
"@tanstack/solid-virtual": "^3.0.0" // 大列表虚拟化
"@solid-primitives/resize-observer": "^2.0.0" // 尺寸观察

// 编辑器和输入
"@codemirror/state": "^6.4.0"     // 代码编辑器状态
"@codemirror/view": "^6.23.0"     // 编辑器视图
"@codemirror/lang-markdown": "^6.2.0" // Markdown支持

// 拖拽和排序
"@dnd-kit/core": "^6.1.0"         // 拖拽核心
"@dnd-kit/sortable": "^8.0.0"     // 排序功能
"@dnd-kit/utilities": "^3.2.0"    // 拖拽工具
```

### 2. UI库选择理由

#### @kobalte/core - 核心选择
- **完全可访问性**: 符合WAI-ARIA标准
- **无头设计**: 完全自定义样式控制
- **SolidJS原生**: 专为SolidJS设计，性能最优
- **TypeScript支持**: 完整的类型定义

#### solid-ui - 设计系统
- **shadcn/ui移植**: 成熟的设计系统
- **组件丰富**: 覆盖常用UI组件
- **定制性强**: 基于Tailwind CSS，易于定制
- **社区活跃**: 持续更新和维护

#### Tailwind CSS v4 - 样式系统
- **原子化CSS**: 快速构建界面
- **响应式设计**: 内置响应式工具
- **暗色模式**: 原生支持主题切换
- **性能优化**: 按需生成，体积最小

## 第二部分：设计风格定义

### 1. 视觉风格

#### 设计原则
- **简洁至上**: 去除不必要的装饰元素
- **功能导向**: 每个元素都有明确的功能目的
- **一致性**: 统一的视觉语言和交互模式
- **专业感**: 适合商务和学术环境使用

#### 色彩系统
```css
/* 主色调 - 专业蓝 */
:root {
  /* Primary Colors - 专业蓝色系 */
  --color-primary-50: #eff6ff;
  --color-primary-100: #dbeafe;
  --color-primary-200: #bfdbfe;
  --color-primary-300: #93c5fd;
  --color-primary-400: #60a5fa;
  --color-primary-500: #3b82f6;  /* 主色 */
  --color-primary-600: #2563eb;
  --color-primary-700: #1d4ed8;
  --color-primary-800: #1e40af;
  --color-primary-900: #1e3a8a;

  /* Secondary Colors - 中性灰色系 */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;

  /* Semantic Colors - 语义色彩 */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  /* P.A.R.A. 专用色彩 */
  --color-projects: #3b82f6;    /* 蓝色 - 项目 */
  --color-areas: #10b981;       /* 绿色 - 领域 */
  --color-resources: #f59e0b;   /* 橙色 - 资源 */
  --color-archive: #6b7280;     /* 灰色 - 归档 */
}

/* 暗色模式 */
[data-theme="dark"] {
  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-surface-elevated: #334155;
  --color-text-primary: #f1f5f9;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-border: #334155;
}

/* 亮色模式 */
[data-theme="light"] {
  --color-background: #ffffff;
  --color-surface: #f8fafc;
  --color-surface-elevated: #ffffff;
  --color-text-primary: #0f172a;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;
  --color-border: #e2e8f0;
}
```

#### 字体系统
```css
/* 字体定义 */
:root {
  /* 字体族 */
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  --font-display: 'Inter Display', var(--font-sans);

  /* 字体大小 */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */

  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;

  /* 字重 */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
}
```

#### 间距系统
```css
/* 间距系统 - 基于8px网格 */
:root {
  --space-0: 0;
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
}
```

### 2. 组件设计规范

#### 按钮系统
```typescript
// 按钮变体定义
const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "underline-offset-4 hover:underline text-primary",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)
```

#### 卡片系统
```typescript
// 卡片组件规范
interface CardProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'filled';
  size?: 'sm' | 'md' | 'lg';
  interactive?: boolean;
  className?: string;
  children: JSX.Element;
}

const cardVariants = cva(
  "rounded-lg border bg-card text-card-foreground shadow-sm",
  {
    variants: {
      variant: {
        default: "border-border",
        elevated: "border-border shadow-md",
        outlined: "border-2 border-border",
        filled: "bg-muted border-transparent",
      },
      size: {
        sm: "p-4",
        md: "p-6",
        lg: "p-8",
      },
      interactive: {
        true: "cursor-pointer hover:shadow-md transition-shadow",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
      interactive: false,
    },
  }
)
```

## 第三部分：响应式设计要求

### 1. 断点系统

```css
/* 响应式断点 */
:root {
  --breakpoint-sm: 640px;   /* 手机横屏 */
  --breakpoint-md: 768px;   /* 平板竖屏 */
  --breakpoint-lg: 1024px;  /* 平板横屏/小笔记本 */
  --breakpoint-xl: 1280px;  /* 桌面显示器 */
  --breakpoint-2xl: 1536px; /* 大屏显示器 */
}

/* Tailwind CSS 断点配置 */
module.exports = {
  theme: {
    screens: {
      'sm': '640px',
      'md': '768px', 
      'lg': '1024px',
      'xl': '1280px',
      '2xl': '1536px',
    }
  }
}
```

### 2. 布局适配策略

#### 桌面端 (≥1024px)
- **三栏布局**: 侧边栏 + 主内容 + 详情面板
- **侧边栏宽度**: 280px (可折叠到64px)
- **主内容区**: 自适应宽度，最大1200px
- **详情面板**: 320px (可隐藏)

#### 平板端 (768px - 1023px)
- **两栏布局**: 侧边栏 + 主内容
- **侧边栏**: 可折叠，覆盖模式
- **主内容**: 全宽显示
- **详情面板**: 模态框或抽屉形式

#### 手机端 (<768px)
- **单栏布局**: 全屏主内容
- **导航**: 底部标签栏
- **侧边栏**: 抽屉式导航
- **详情**: 全屏模态或新页面

### 3. 组件响应式规范

#### 卡片网格
```css
/* 响应式卡片网格 */
.card-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

/* 平板适配 */
@media (max-width: 1023px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 1rem;
  }
}

/* 手机适配 */
@media (max-width: 767px) {
  .card-grid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
}
```

#### 表格响应式
```typescript
// 响应式表格组件
function ResponsiveTable(props: TableProps) {
  const [isMobile] = createSignal(window.innerWidth < 768);
  
  return (
    <Show
      when={!isMobile()}
      fallback={<MobileCardList data={props.data} />}
    >
      <DesktopTable data={props.data} />
    </Show>
  );
}
```

### 4. 触摸和交互适配

#### 触摸目标尺寸
```css
/* 触摸友好的最小尺寸 */
.touch-target {
  min-height: 44px;  /* iOS推荐 */
  min-width: 44px;
}

/* 手机端按钮加大 */
@media (max-width: 767px) {
  .btn {
    min-height: 48px;
    padding: 12px 16px;
  }
}
```

#### 手势支持
```typescript
// 手势识别配置
const gestureConfig = {
  swipe: {
    threshold: 50,      // 滑动阈值
    velocity: 0.3,      // 速度阈值
  },
  pinch: {
    threshold: 0.1,     // 缩放阈值
  },
  longPress: {
    duration: 500,      // 长按时长
  }
};
```

## 第四部分：组件库配置

### 1. Tailwind CSS 配置

```javascript
// tailwind.config.js
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./index.html"
  ],
  darkMode: ['class', '[data-theme="dark"]'],
  theme: {
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        // P.A.R.A. 专用色彩
        projects: "hsl(var(--color-projects))",
        areas: "hsl(var(--color-areas))",
        resources: "hsl(var(--color-resources))",
        archive: "hsl(var(--color-archive))",
      },
      fontFamily: {
        sans: ['Inter', 'sans-serif'],
        mono: ['JetBrains Mono', 'monospace'],
        display: ['Inter Display', 'sans-serif'],
      },
      animation: {
        "fade-in": "fadeIn 0.2s ease-in-out",
        "slide-in": "slideIn 0.3s ease-out",
        "scale-in": "scaleIn 0.2s ease-out",
      },
      keyframes: {
        fadeIn: {
          "0%": { opacity: "0" },
          "100%": { opacity: "1" },
        },
        slideIn: {
          "0%": { transform: "translateY(-10px)", opacity: "0" },
          "100%": { transform: "translateY(0)", opacity: "1" },
        },
        scaleIn: {
          "0%": { transform: "scale(0.95)", opacity: "0" },
          "100%": { transform: "scale(1)", opacity: "1" },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio'),
  ],
}
```

### 2. SolidJS 组件库集成

```typescript
// src/components/ui/index.ts - 统一导出
export { Button } from './button';
export { Card, CardHeader, CardContent, CardFooter } from './card';
export { Input } from './input';
export { Label } from './label';
export { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './select';
export { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './dialog';
export { Tabs, TabsContent, TabsList, TabsTrigger } from './tabs';
export { Badge } from './badge';
export { Avatar, AvatarFallback, AvatarImage } from './avatar';
export { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './tooltip';

// P.A.R.A. 专用组件
export { ProjectCard } from './project-card';
export { AreaCard } from './area-card';
export { TaskItem } from './task-item';
export { ResourceLink } from './resource-link';
export { KPIChart } from './kpi-chart';
export { HabitTracker } from './habit-tracker';
```

```typescript
// src/components/ui/button.tsx - 按钮组件示例
import { splitProps, type JSX } from 'solid-js';
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "underline-offset-4 hover:underline text-primary",
      },
      size: {
        default: "h-10 py-2 px-4",
        sm: "h-9 px-3 rounded-md",
        lg: "h-11 px-8 rounded-md",
        icon: "h-10 w-10",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps
  extends JSX.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

export function Button(props: ButtonProps) {
  const [local, others] = splitProps(props, ["variant", "size", "class"]);

  return (
    <button
      class={cn(buttonVariants({ variant: local.variant, size: local.size }), local.class)}
      {...others}
    />
  );
}
```

## 第五部分：P.A.R.A.专用UI组件

### 1. 项目卡片组件

```typescript
// src/components/ui/project-card.tsx
import { Show, createSignal } from 'solid-js';
import { Card, CardContent, CardHeader } from './card';
import { Badge } from './badge';
import { Button } from './button';
import { Progress } from './progress';

interface ProjectCardProps {
  project: {
    id: string;
    name: string;
    description?: string;
    status: 'not_started' | 'in_progress' | 'at_risk' | 'paused' | 'completed';
    progress: number;
    deadline?: string;
    area?: { name: string; color: string };
    taskCount: number;
    completedTasks: number;
  };
  onEdit?: (id: string) => void;
  onView?: (id: string) => void;
}

export function ProjectCard(props: ProjectCardProps) {
  const [isHovered, setIsHovered] = createSignal(false);

  const statusColors = {
    not_started: 'bg-gray-100 text-gray-800',
    in_progress: 'bg-blue-100 text-blue-800',
    at_risk: 'bg-red-100 text-red-800',
    paused: 'bg-yellow-100 text-yellow-800',
    completed: 'bg-green-100 text-green-800',
  };

  const statusLabels = {
    not_started: '未开始',
    in_progress: '进行中',
    at_risk: '有风险',
    paused: '已暂停',
    completed: '已完成',
  };

  return (
    <Card
      class="cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-[1.02]"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={() => props.onView?.(props.project.id)}
    >
      <CardHeader class="pb-3">
        <div class="flex items-start justify-between">
          <div class="flex-1 min-w-0">
            <h3 class="font-semibold text-lg text-foreground truncate">
              {props.project.name}
            </h3>
            <Show when={props.project.description}>
              <p class="text-sm text-muted-foreground mt-1 line-clamp-2">
                {props.project.description}
              </p>
            </Show>
          </div>
          <div class="flex items-center gap-2 ml-3">
            <Badge class={statusColors[props.project.status]}>
              {statusLabels[props.project.status]}
            </Badge>
            <Show when={isHovered()}>
              <Button
                variant="ghost"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  props.onEdit?.(props.project.id);
                }}
              >
                <EditIcon class="h-4 w-4" />
              </Button>
            </Show>
          </div>
        </div>
      </CardHeader>

      <CardContent class="pt-0">
        {/* 进度条 */}
        <div class="space-y-2">
          <div class="flex justify-between text-sm">
            <span class="text-muted-foreground">进度</span>
            <span class="font-medium">{props.project.progress}%</span>
          </div>
          <Progress value={props.project.progress} class="h-2" />
        </div>

        {/* 任务统计 */}
        <div class="flex items-center justify-between mt-4 text-sm">
          <div class="flex items-center gap-4">
            <span class="text-muted-foreground">
              任务: {props.project.completedTasks}/{props.project.taskCount}
            </span>
            <Show when={props.project.area}>
              <div class="flex items-center gap-1">
                <div
                  class="w-2 h-2 rounded-full"
                  style={{ "background-color": props.project.area!.color }}
                />
                <span class="text-muted-foreground">{props.project.area!.name}</span>
              </div>
            </Show>
          </div>
          <Show when={props.project.deadline}>
            <span class="text-muted-foreground">
              截止: {new Date(props.project.deadline!).toLocaleDateString()}
            </span>
          </Show>
        </div>
      </CardContent>
    </Card>
  );
}
```

### 2. 任务项组件

```typescript
// src/components/ui/task-item.tsx
import { Show, createSignal } from 'solid-js';
import { Checkbox } from './checkbox';
import { Badge } from './badge';
import { Button } from './button';

interface TaskItemProps {
  task: {
    id: string;
    title: string;
    completed: boolean;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    dueDate?: string;
    tags?: Array<{ name: string; color: string }>;
    subtaskCount?: number;
    level: number; // 层级深度
  };
  onToggle?: (id: string, completed: boolean) => void;
  onEdit?: (id: string) => void;
  onAddSubtask?: (parentId: string) => void;
}

export function TaskItem(props: TaskItemProps) {
  const [isHovered, setIsHovered] = createSignal(false);

  const priorityColors = {
    low: 'bg-gray-100 text-gray-700',
    medium: 'bg-blue-100 text-blue-700',
    high: 'bg-orange-100 text-orange-700',
    urgent: 'bg-red-100 text-red-700',
  };

  const priorityLabels = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急',
  };

  return (
    <div
      class={`group flex items-center gap-3 p-3 rounded-lg border transition-all duration-200 ${
        props.task.completed ? 'bg-muted/50 opacity-75' : 'bg-background hover:bg-muted/30'
      }`}
      style={{ "margin-left": `${props.task.level * 24}px` }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 复选框 */}
      <Checkbox
        checked={props.task.completed}
        onCheckedChange={(checked) => props.onToggle?.(props.task.id, checked as boolean)}
        class="flex-shrink-0"
      />

      {/* 任务内容 */}
      <div class="flex-1 min-w-0">
        <div class="flex items-center gap-2 mb-1">
          <span
            class={`font-medium ${
              props.task.completed ? 'line-through text-muted-foreground' : 'text-foreground'
            }`}
          >
            {props.task.title}
          </span>

          {/* 优先级标签 */}
          <Show when={props.task.priority}>
            <Badge variant="secondary" class={priorityColors[props.task.priority!]}>
              {priorityLabels[props.task.priority!]}
            </Badge>
          </Show>

          {/* 子任务数量 */}
          <Show when={props.task.subtaskCount && props.task.subtaskCount > 0}>
            <Badge variant="outline" class="text-xs">
              {props.task.subtaskCount} 子任务
            </Badge>
          </Show>
        </div>

        {/* 标签和截止日期 */}
        <div class="flex items-center gap-2 text-sm">
          <Show when={props.task.tags && props.task.tags.length > 0}>
            <div class="flex gap-1">
              {props.task.tags!.map((tag) => (
                <Badge
                  variant="outline"
                  class="text-xs"
                  style={{ "border-color": tag.color, color: tag.color }}
                >
                  {tag.name}
                </Badge>
              ))}
            </div>
          </Show>

          <Show when={props.task.dueDate}>
            <span class="text-muted-foreground">
              截止: {new Date(props.task.dueDate!).toLocaleDateString()}
            </span>
          </Show>
        </div>
      </div>

      {/* 操作按钮 */}
      <Show when={isHovered()}>
        <div class="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="ghost"
            size="icon"
            class="h-8 w-8"
            onClick={() => props.onAddSubtask?.(props.task.id)}
          >
            <PlusIcon class="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            class="h-8 w-8"
            onClick={() => props.onEdit?.(props.task.id)}
          >
            <EditIcon class="h-4 w-4" />
          </Button>
        </div>
      </Show>
    </div>
  );
}
```

### 3. 习惯追踪器组件

```typescript
// src/components/ui/habit-tracker.tsx
import { For, createSignal, createMemo } from 'solid-js';
import { Card, CardContent, CardHeader, CardTitle } from './card';

interface HabitTrackerProps {
  habit: {
    id: string;
    name: string;
    target: number;
    frequency: 'daily' | 'weekly' | 'monthly';
  };
  records: Array<{
    date: string;
    completed: boolean;
    value?: number;
  }>;
  onToggle?: (date: string, completed: boolean) => void;
}

export function HabitTracker(props: HabitTrackerProps) {
  const [selectedDate, setSelectedDate] = createSignal<string | null>(null);

  // 生成日历网格数据
  const calendarData = createMemo(() => {
    const today = new Date();
    const startDate = new Date(today.getFullYear(), today.getMonth() - 2, 1);
    const endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const days = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      const dateStr = current.toISOString().split('T')[0];
      const record = props.records.find(r => r.date === dateStr);

      days.push({
        date: dateStr,
        day: current.getDate(),
        isToday: dateStr === today.toISOString().split('T')[0],
        isCompleted: record?.completed || false,
        value: record?.value,
      });

      current.setDate(current.getDate() + 1);
    }

    return days;
  });

  // 计算连续天数
  const streakCount = createMemo(() => {
    const sortedRecords = props.records
      .filter(r => r.completed)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

    let streak = 0;
    const today = new Date().toISOString().split('T')[0];
    let currentDate = today;

    for (const record of sortedRecords) {
      if (record.date === currentDate) {
        streak++;
        const date = new Date(currentDate);
        date.setDate(date.getDate() - 1);
        currentDate = date.toISOString().split('T')[0];
      } else {
        break;
      }
    }

    return streak;
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle class="flex items-center justify-between">
          <span>{props.habit.name}</span>
          <div class="text-sm font-normal text-muted-foreground">
            连续 {streakCount()} 天
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent>
        {/* 热力图 */}
        <div class="grid grid-cols-7 gap-1 mb-4">
          <For each={['日', '一', '二', '三', '四', '五', '六']}>
            {(day) => (
              <div class="text-xs text-center text-muted-foreground p-1">
                {day}
              </div>
            )}
          </For>

          <For each={calendarData()}>
            {(day) => (
              <button
                class={`
                  w-8 h-8 rounded text-xs font-medium transition-all duration-200
                  ${day.isCompleted
                    ? 'bg-green-500 text-white hover:bg-green-600'
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                  }
                  ${day.isToday ? 'ring-2 ring-blue-500' : ''}
                `}
                onClick={() => {
                  props.onToggle?.(day.date, !day.isCompleted);
                }}
                onMouseEnter={() => setSelectedDate(day.date)}
                onMouseLeave={() => setSelectedDate(null)}
              >
                {day.day}
              </button>
            )}
          </For>
        </div>

        {/* 统计信息 */}
        <div class="flex justify-between text-sm text-muted-foreground">
          <span>目标: {props.habit.target}次/{props.habit.frequency === 'daily' ? '天' : props.habit.frequency === 'weekly' ? '周' : '月'}</span>
          <span>完成率: {Math.round((props.records.filter(r => r.completed).length / props.records.length) * 100)}%</span>
        </div>
      </CardContent>
    </Card>
  );
}
```

## 第六部分：主题系统和暗色模式

### 1. 主题切换实现

```typescript
// src/lib/theme.ts
import { createSignal, createEffect } from 'solid-js';

export type Theme = 'light' | 'dark' | 'system';

const [theme, setTheme] = createSignal<Theme>('system');

// 获取系统主题
function getSystemTheme(): 'light' | 'dark' {
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

// 应用主题
function applyTheme(theme: 'light' | 'dark') {
  const root = document.documentElement;
  root.setAttribute('data-theme', theme);
  root.classList.remove('light', 'dark');
  root.classList.add(theme);
}

// 主题切换逻辑
createEffect(() => {
  const currentTheme = theme();
  const actualTheme = currentTheme === 'system' ? getSystemTheme() : currentTheme;
  applyTheme(actualTheme);

  // 保存到本地存储
  localStorage.setItem('theme', currentTheme);
});

// 监听系统主题变化
window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
  if (theme() === 'system') {
    applyTheme(getSystemTheme());
  }
});

// 初始化主题
const savedTheme = localStorage.getItem('theme') as Theme;
if (savedTheme) {
  setTheme(savedTheme);
}

export { theme, setTheme };
```

### 2. 主题切换组件

```typescript
// src/components/ui/theme-toggle.tsx
import { Show } from 'solid-js';
import { Button } from './button';
import { theme, setTheme } from '@/lib/theme';

export function ThemeToggle() {
  const toggleTheme = () => {
    const currentTheme = theme();
    if (currentTheme === 'light') {
      setTheme('dark');
    } else if (currentTheme === 'dark') {
      setTheme('system');
    } else {
      setTheme('light');
    }
  };

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={toggleTheme}
      title="切换主题"
    >
      <Show when={theme() === 'light'}>
        <SunIcon class="h-5 w-5" />
      </Show>
      <Show when={theme() === 'dark'}>
        <MoonIcon class="h-5 w-5" />
      </Show>
      <Show when={theme() === 'system'}>
        <ComputerIcon class="h-5 w-5" />
      </Show>
    </Button>
  );
}
```

## 第七部分：动画和交互设计

### 1. 动画系统

```css
/* 全局动画配置 */
:root {
  --animation-duration-fast: 150ms;
  --animation-duration-normal: 250ms;
  --animation-duration-slow: 350ms;

  --animation-easing-ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --animation-easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --animation-easing-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
}

/* 通用动画类 */
.animate-fade-in {
  animation: fadeIn var(--animation-duration-normal) var(--animation-easing-ease-out);
}

.animate-slide-up {
  animation: slideUp var(--animation-duration-normal) var(--animation-easing-ease-out);
}

.animate-scale-in {
  animation: scaleIn var(--animation-duration-fast) var(--animation-easing-ease-out);
}

/* 页面转场动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: all var(--animation-duration-normal) var(--animation-easing-ease-out);
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: all var(--animation-duration-fast) var(--animation-easing-ease-in);
}
```

### 2. 微交互设计

```typescript
// src/components/ui/interactive-button.tsx
import { createSignal } from 'solid-js';
import { Button, type ButtonProps } from './button';

interface InteractiveButtonProps extends ButtonProps {
  hapticFeedback?: boolean;
  rippleEffect?: boolean;
}

export function InteractiveButton(props: InteractiveButtonProps) {
  const [isPressed, setIsPressed] = createSignal(false);
  const [ripples, setRipples] = createSignal<Array<{ id: number; x: number; y: number }>>([]);

  const handleMouseDown = (e: MouseEvent) => {
    setIsPressed(true);

    // 添加涟漪效果
    if (props.rippleEffect) {
      const rect = (e.currentTarget as HTMLElement).getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;
      const id = Date.now();

      setRipples(prev => [...prev, { id, x, y }]);

      // 清理涟漪
      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== id));
      }, 600);
    }

    // 触觉反馈
    if (props.hapticFeedback && 'vibrate' in navigator) {
      navigator.vibrate(10);
    }
  };

  const handleMouseUp = () => {
    setIsPressed(false);
  };

  return (
    <Button
      {...props}
      class={`
        relative overflow-hidden transition-transform duration-75
        ${isPressed() ? 'scale-95' : 'scale-100'}
        ${props.class || ''}
      `}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      {props.children}

      {/* 涟漪效果 */}
      {ripples().map((ripple) => (
        <span
          class="absolute pointer-events-none animate-ping"
          style={{
            left: `${ripple.x}px`,
            top: `${ripple.y}px`,
            width: '20px',
            height: '20px',
            'border-radius': '50%',
            'background-color': 'currentColor',
            opacity: '0.3',
            transform: 'translate(-50%, -50%)',
          }}
        />
      ))}
    </Button>
  );
}
```

## 第八部分：可访问性设计

### 1. 可访问性标准

```typescript
// src/lib/accessibility.ts
export const a11yConfig = {
  // WCAG 2.1 AA 标准
  colorContrast: {
    normal: 4.5,      // 普通文本
    large: 3,         // 大文本 (18pt+ 或 14pt+ 粗体)
    nonText: 3,       // 非文本元素
  },

  // 焦点管理
  focusManagement: {
    trapFocus: true,          // 模态框焦点陷阱
    restoreFocus: true,       // 恢复焦点
    skipLinks: true,          // 跳过链接
  },

  // 键盘导航
  keyboardNavigation: {
    tabIndex: true,           // Tab 索引管理
    arrowKeys: true,          // 方向键导航
    enterSpace: true,         // Enter/Space 激活
    escape: true,             // Escape 关闭
  },

  // 屏幕阅读器
  screenReader: {
    ariaLabels: true,         // ARIA 标签
    ariaDescriptions: true,   // ARIA 描述
    liveRegions: true,        // 实时区域
    landmarks: true,          // 地标
  },
};
```

### 2. 可访问性组件

```typescript
// src/components/ui/accessible-button.tsx
import { splitProps, type JSX } from 'solid-js';
import { Button, type ButtonProps } from './button';

interface AccessibleButtonProps extends ButtonProps {
  'aria-label'?: string;
  'aria-describedby'?: string;
  loading?: boolean;
  loadingText?: string;
}

export function AccessibleButton(props: AccessibleButtonProps) {
  const [local, others] = splitProps(props, [
    'aria-label',
    'aria-describedby',
    'loading',
    'loadingText',
    'children'
  ]);

  return (
    <Button
      aria-label={local['aria-label']}
      aria-describedby={local['aria-describedby']}
      aria-busy={local.loading}
      disabled={local.loading || others.disabled}
      {...others}
    >
      {local.loading ? (
        <>
          <LoadingSpinner class="mr-2 h-4 w-4 animate-spin" />
          {local.loadingText || '加载中...'}
        </>
      ) : (
        local.children
      )}
    </Button>
  );
}
```

## 总结

这个UI设计规范文档为PaoLife项目提供了：

### 核心特点
1. **技术栈匹配**: 完全基于SolidJS + Tailwind CSS v4
2. **P.A.R.A.适配**: 专门为P.A.R.A.方法论设计的组件
3. **响应式完整**: 支持桌面、平板、手机全设备
4. **可访问性**: 符合WCAG 2.1 AA标准
5. **现代化**: 采用最新的设计趋势和最佳实践

### 实施建议
1. **立即可用**: 所有配置和组件都可直接使用
2. **渐进实施**: 可以逐步替换现有组件
3. **团队协作**: 统一的设计语言和组件库
4. **长期维护**: 模块化设计，易于维护和扩展

这个设计规范将确保PaoLife具有一致、专业、高效的用户界面体验。
