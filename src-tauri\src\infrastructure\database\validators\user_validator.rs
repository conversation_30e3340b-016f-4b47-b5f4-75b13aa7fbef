// 用户数据验证器
// 实现用户相关的数据验证和业务规则检查

use crate::domain::entities::user::{CreateUserData, UpdateUserData};
use crate::infrastructure::database::validators::{Validator, ValidationUtils, ValidationContext};
use crate::shared::errors::{AppError, AppResult};

/// 用户创建数据验证器
pub struct CreateUserValidator;

impl Validator<CreateUserData> for CreateUserValidator {
    fn validate(&self, data: &CreateUserData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 验证用户名
        ctx.validate(
            || ValidationUtils::validate_required(&data.username, "用户名"),
            "username_required"
        );
        
        ctx.validate(
            || ValidationUtils::validate_length(&data.username, "用户名", 3, 50),
            "username_length"
        );
        
        ctx.validate(
            || ValidationUtils::validate_username(&data.username),
            "username_format"
        );

        // 验证邮箱
        if let Some(email) = &data.email {
            if !email.trim().is_empty() {
                ctx.validate(
                    || ValidationUtils::validate_email(email),
                    "email_format"
                );
            }
        }

        // 验证密码
        ctx.validate(
            || ValidationUtils::validate_required(&data.password, "密码"),
            "password_required"
        );
        
        ctx.validate(
            || ValidationUtils::validate_password_strength(&data.password),
            "password_strength"
        );

        // 验证全名
        if let Some(full_name) = &data.full_name {
            ctx.validate(
                || ValidationUtils::validate_length(full_name, "全名", 1, 100),
                "full_name_length"
            );
        }

        // 验证时区
        if let Some(timezone) = &data.timezone {
            ctx.validate(
                || ValidationUtils::validate_required(timezone, "时区"),
                "timezone_required"
            );
        }

        // 验证语言
        if let Some(language) = &data.language {
            ctx.validate(
                || ValidationUtils::validate_required(language, "语言"),
                "language_required"
            );
            
            ctx.validate(
                || ValidationUtils::validate_enum(
                    language,
                    "语言",
                    &["en", "zh", "zh-CN", "zh-TW", "ja", "ko", "fr", "de", "es"]
                ),
                "language_enum"
            );
        }

        // 验证创建者
        ctx.validate(
            || ValidationUtils::validate_required(&data.created_by, "创建者"),
            "created_by_required"
        );

        ctx.finish()
    }

    fn sanitize(&self, mut data: CreateUserData) -> CreateUserData {
        // 清理用户名
        data.username = ValidationUtils::sanitize_string(data.username);
        
        // 清理和标准化邮箱
        if let Some(email) = data.email {
            data.email = Some(ValidationUtils::normalize_email(email));
        }

        // 清理全名
        data.full_name = ValidationUtils::sanitize_optional_string(data.full_name);

        // 设置默认值
        if data.timezone.is_none() {
            data.timezone = Some("UTC".to_string());
        }

        if data.language.is_none() {
            data.language = Some("en".to_string());
        }

        // 清理创建者
        data.created_by = ValidationUtils::sanitize_string(data.created_by);

        data
    }
}

/// 用户更新数据验证器
pub struct UpdateUserValidator;

impl Validator<UpdateUserData> for UpdateUserValidator {
    fn validate(&self, data: &UpdateUserData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 验证邮箱
        if let Some(Some(email)) = &data.email {
            if !email.trim().is_empty() {
                ctx.validate(
                    || ValidationUtils::validate_email(email),
                    "email_format"
                );
            }
        }

        // 验证密码哈希（如果提供）
        if let Some(password_hash) = &data.password_hash {
            ctx.validate(
                || ValidationUtils::validate_required(password_hash, "密码哈希"),
                "password_hash_required"
            );
        }

        // 验证全名
        if let Some(Some(full_name)) = &data.full_name {
            ctx.validate(
                || ValidationUtils::validate_length(full_name, "全名", 1, 100),
                "full_name_length"
            );
        }

        // 验证头像URL
        if let Some(Some(avatar_url)) = &data.avatar_url {
            if !avatar_url.trim().is_empty() {
                ctx.validate(
                    || ValidationUtils::validate_url(avatar_url),
                    "avatar_url_format"
                );
            }
        }

        // 验证时区
        if let Some(timezone) = &data.timezone {
            ctx.validate(
                || ValidationUtils::validate_required(timezone, "时区"),
                "timezone_required"
            );
        }

        // 验证语言
        if let Some(language) = &data.language {
            ctx.validate(
                || ValidationUtils::validate_required(language, "语言"),
                "language_required"
            );
            
            ctx.validate(
                || ValidationUtils::validate_enum(
                    language,
                    "语言",
                    &["en", "zh", "zh-CN", "zh-TW", "ja", "ko", "fr", "de", "es"]
                ),
                "language_enum"
            );
        }

        ctx.finish()
    }

    fn sanitize(&self, mut data: UpdateUserData) -> UpdateUserData {
        // 清理和标准化邮箱
        if let Some(Some(email)) = data.email {
            data.email = Some(Some(ValidationUtils::normalize_email(email)));
        }

        // 清理全名
        if let Some(full_name) = data.full_name {
            data.full_name = Some(ValidationUtils::sanitize_optional_string(full_name));
        }

        // 清理头像URL
        if let Some(avatar_url) = data.avatar_url {
            data.avatar_url = Some(ValidationUtils::sanitize_optional_string(avatar_url));
        }

        // 清理时区
        if let Some(timezone) = data.timezone {
            data.timezone = Some(ValidationUtils::sanitize_string(timezone));
        }

        // 清理语言
        if let Some(language) = data.language {
            data.language = Some(ValidationUtils::sanitize_string(language));
        }

        data
    }
}

/// 用户业务规则验证器
pub struct UserBusinessRuleValidator;

impl UserBusinessRuleValidator {
    /// 验证用户名唯一性
    pub async fn validate_username_uniqueness<F, Fut>(
        username: &str,
        check_fn: F,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        ValidationUtils::validate_uniqueness(check_fn, "用户名", username).await
    }

    /// 验证邮箱唯一性
    pub async fn validate_email_uniqueness<F, Fut>(
        email: &str,
        check_fn: F,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        ValidationUtils::validate_uniqueness(check_fn, "邮箱", email).await
    }

    /// 验证用户状态转换
    pub fn validate_status_transition(
        current_active: bool,
        new_active: bool,
        user_role: &str,
    ) -> AppResult<()> {
        // 管理员用户不能被停用
        if user_role == "admin" && !new_active {
            return Err(AppError::validation("管理员用户不能被停用"));
        }

        // 如果用户当前是停用状态，激活需要特殊权限
        if !current_active && new_active {
            // 这里可以添加额外的权限检查
            ValidationUtils::validate_business_rule(
                || true, // 假设有权限检查逻辑
                "没有权限激活用户"
            )?;
        }

        Ok(())
    }

    /// 验证密码更新规则
    pub fn validate_password_update(
        user_id: &str,
        current_user_id: &str,
        is_admin: bool,
    ) -> AppResult<()> {
        // 只有用户本人或管理员可以更新密码
        if user_id != current_user_id && !is_admin {
            return Err(AppError::validation("只有用户本人或管理员可以更新密码"));
        }

        Ok(())
    }

    /// 验证用户删除规则
    pub fn validate_user_deletion(
        user_id: &str,
        current_user_id: &str,
        is_admin: bool,
        user_role: &str,
    ) -> AppResult<()> {
        // 用户不能删除自己
        if user_id == current_user_id {
            return Err(AppError::validation("用户不能删除自己"));
        }

        // 只有管理员可以删除用户
        if !is_admin {
            return Err(AppError::validation("只有管理员可以删除用户"));
        }

        // 不能删除其他管理员
        if user_role == "admin" {
            return Err(AppError::validation("不能删除管理员用户"));
        }

        Ok(())
    }

    /// 验证用户数据完整性
    pub fn validate_data_integrity(data: &CreateUserData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 如果提供了邮箱，必须是有效的
        if let Some(email) = &data.email {
            if !email.trim().is_empty() {
                ctx.validate(
                    || ValidationUtils::validate_email(email),
                    "email_integrity"
                );
            }
        }

        // 用户名不能包含敏感词
        let sensitive_words = ["admin", "root", "system", "test"];
        for word in sensitive_words {
            if data.username.to_lowercase().contains(word) {
                ctx.validate(
                    || Err(AppError::validation(&format!("用户名不能包含敏感词: {}", word))),
                    "username_sensitive"
                );
                break;
            }
        }

        // 密码不能与用户名相同
        if data.password.to_lowercase() == data.username.to_lowercase() {
            ctx.validate(
                || Err(AppError::validation("密码不能与用户名相同")),
                "password_username_same"
            );
        }

        ctx.finish()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_user_validation() {
        let validator = CreateUserValidator;
        
        // 有效数据
        let valid_data = CreateUserData {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "password123".to_string(),
            full_name: Some("Test User".to_string()),
            timezone: Some("UTC".to_string()),
            language: Some("en".to_string()),
            created_by: "system".to_string(),
        };
        
        assert!(validator.validate(&valid_data).is_ok());

        // 无效用户名
        let invalid_data = CreateUserData {
            username: "ab".to_string(), // 太短
            ..valid_data.clone()
        };
        assert!(validator.validate(&invalid_data).is_err());

        // 无效邮箱
        let invalid_data = CreateUserData {
            email: Some("invalid-email".to_string()),
            ..valid_data.clone()
        };
        assert!(validator.validate(&invalid_data).is_err());
    }

    #[test]
    fn test_data_sanitization() {
        let validator = CreateUserValidator;
        
        let data = CreateUserData {
            username: "  testuser  ".to_string(),
            email: Some("  <EMAIL>  ".to_string()),
            password: "password123".to_string(),
            full_name: Some("  Test User  ".to_string()),
            timezone: None,
            language: None,
            created_by: "  system  ".to_string(),
        };

        let sanitized = validator.sanitize(data);
        
        assert_eq!(sanitized.username, "testuser");
        assert_eq!(sanitized.email, Some("<EMAIL>".to_string()));
        assert_eq!(sanitized.full_name, Some("Test User".to_string()));
        assert_eq!(sanitized.timezone, Some("UTC".to_string()));
        assert_eq!(sanitized.language, Some("en".to_string()));
        assert_eq!(sanitized.created_by, "system");
    }

    #[test]
    fn test_business_rules() {
        // 测试状态转换
        assert!(UserBusinessRuleValidator::validate_status_transition(
            true, false, "user"
        ).is_ok());
        
        assert!(UserBusinessRuleValidator::validate_status_transition(
            true, false, "admin"
        ).is_err());

        // 测试密码更新权限
        assert!(UserBusinessRuleValidator::validate_password_update(
            "user1", "user1", false
        ).is_ok());
        
        assert!(UserBusinessRuleValidator::validate_password_update(
            "user1", "user2", false
        ).is_err());
        
        assert!(UserBusinessRuleValidator::validate_password_update(
            "user1", "user2", true
        ).is_ok());
    }
}
