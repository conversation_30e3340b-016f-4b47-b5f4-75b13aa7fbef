// 头部组件
// 应用顶部导航栏，包含标题、搜索、通知和用户菜单

import { Component, createSignal, Show, onMount, onCleanup } from 'solid-js';
import { useAuth } from '../../contexts/AuthContext';
import { useNavigate } from '@solidjs/router';
import { Button, IconButton } from '../ui/Button';
import { Input } from '../ui/Input';
import toast from 'solid-toast';

interface HeaderProps {
  title?: string;
  onMenuClick?: () => void;
  showMenuButton?: boolean;
  sidebarCollapsed?: boolean;
  onToggleSidebar?: () => void;
  showSidebarToggle?: boolean;
}

export const Header: Component<HeaderProps> = (props) => {
  const { user, logout } = useAuth();
  const navigate = useNavigate();
  
  const [searchQuery, setSearchQuery] = createSignal('');
  const [showUserMenu, setShowUserMenu] = createSignal(false);
  const [showNotifications, setShowNotifications] = createSignal(false);
  const [notifications] = createSignal([
    {
      id: '1',
      title: '项目截止提醒',
      message: '项目"网站重构"将在3天后到期',
      time: '2分钟前',
      read: false,
      type: 'warning'
    },
    {
      id: '2',
      title: '任务完成',
      message: '任务"用户界面设计"已完成',
      time: '1小时前',
      read: false,
      type: 'success'
    },
    {
      id: '3',
      title: '新消息',
      message: '团队成员张三给您发送了消息',
      time: '2小时前',
      read: true,
      type: 'info'
    }
  ]);

  // 点击外部关闭菜单
  let userMenuRef: HTMLDivElement | undefined;
  let notificationRef: HTMLDivElement | undefined;

  const handleClickOutside = (event: MouseEvent) => {
    if (userMenuRef && !userMenuRef.contains(event.target as Node)) {
      setShowUserMenu(false);
    }
    if (notificationRef && !notificationRef.contains(event.target as Node)) {
      setShowNotifications(false);
    }
  };

  onMount(() => {
    document.addEventListener('click', handleClickOutside);
  });

  onCleanup(() => {
    document.removeEventListener('click', handleClickOutside);
  });

  // 处理搜索
  const handleSearch = (e: Event) => {
    e.preventDefault();
    if (searchQuery().trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery().trim())}`);
    }
  };

  // 处理登出
  const handleLogout = async () => {
    try {
      await logout();
      navigate('/auth/login', { replace: true });
    } catch (error) {
      toast.error('登出失败');
    }
  };

  const unreadCount = () => notifications().filter(n => !n.read).length;

  return (
    <div class="relative z-10 flex-shrink-0 flex h-16 bg-white shadow">
      {/* 移动端菜单按钮 */}
      <Show when={props.showMenuButton}>
        <button
          type="button"
          class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500 md:hidden"
          onClick={props.onMenuClick}
        >
          <span class="sr-only">打开侧边栏</span>
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h7" />
          </svg>
        </button>
      </Show>

      {/* 侧边栏切换按钮 */}
      <Show when={props.showSidebarToggle}>
        <button
          type="button"
          class="px-4 border-r border-gray-200 text-gray-500 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500"
          onClick={props.onToggleSidebar}
        >
          <span class="sr-only">切换侧边栏</span>
          <Show
            when={props.sidebarCollapsed}
            fallback={
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
              </svg>
            }
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
            </svg>
          </Show>
        </button>
      </Show>

      <div class="flex-1 px-4 flex justify-between">
        {/* 左侧：标题和搜索 */}
        <div class="flex-1 flex items-center">
          <Show when={props.title}>
            <h1 class="text-2xl font-semibold text-gray-900 mr-8">
              {props.title}
            </h1>
          </Show>
          
          {/* 搜索框 */}
          <div class="w-full max-w-lg lg:max-w-xs">
            <form onSubmit={handleSearch}>
              <Input
                type="search"
                placeholder="搜索项目、任务..."
                value={searchQuery()}
                onInput={(e) => setSearchQuery(e.currentTarget.value)}
                leftIcon={() => (
                  <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                )}
                class="pr-10"
              />
            </form>
          </div>
        </div>

        {/* 右侧：通知和用户菜单 */}
        <div class="ml-4 flex items-center md:ml-6 space-x-4">
          {/* 快速操作按钮 */}
          <IconButton
            icon={() => (
              <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
            )}
            variant="ghost"
            size="sm"
            aria-label="快速添加"
            onClick={() => navigate('/quick-add')}
          />

          {/* 通知按钮 */}
          <div class="relative" ref={notificationRef}>
            <button
              type="button"
              class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 relative"
              onClick={() => setShowNotifications(!showNotifications())}
            >
              <span class="sr-only">查看通知</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
              </svg>
              <Show when={unreadCount() > 0}>
                <span class="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                  {unreadCount()}
                </span>
              </Show>
            </button>

            {/* 通知下拉菜单 */}
            <Show when={showNotifications()}>
              <div class="origin-top-right absolute right-0 mt-2 w-80 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                <div class="py-1">
                  <div class="px-4 py-2 border-b border-gray-200">
                    <h3 class="text-sm font-medium text-gray-900">通知</h3>
                  </div>
                  
                  <div class="max-h-64 overflow-y-auto">
                    {notifications().map((notification) => (
                      <div class={`px-4 py-3 hover:bg-gray-50 ${!notification.read ? 'bg-blue-50' : ''}`}>
                        <div class="flex items-start">
                          <div class={`flex-shrink-0 w-2 h-2 rounded-full mt-2 ${
                            notification.type === 'warning' ? 'bg-yellow-400' :
                            notification.type === 'success' ? 'bg-green-400' :
                            'bg-blue-400'
                          }`} />
                          <div class="ml-3 flex-1">
                            <p class="text-sm font-medium text-gray-900">{notification.title}</p>
                            <p class="text-sm text-gray-500">{notification.message}</p>
                            <p class="text-xs text-gray-400 mt-1">{notification.time}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  
                  <div class="px-4 py-2 border-t border-gray-200">
                    <button
                      type="button"
                      class="text-sm text-blue-600 hover:text-blue-500"
                      onClick={() => navigate('/notifications')}
                    >
                      查看所有通知
                    </button>
                  </div>
                </div>
              </div>
            </Show>
          </div>

          {/* 用户菜单 */}
          <div class="relative" ref={userMenuRef}>
            <button
              type="button"
              class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              onClick={() => setShowUserMenu(!showUserMenu())}
            >
              <span class="sr-only">打开用户菜单</span>
              <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                <span class="text-sm font-medium text-white">
                  {user()?.username?.charAt(0).toUpperCase() || 'U'}
                </span>
              </div>
            </button>

            {/* 用户下拉菜单 */}
            <Show when={showUserMenu()}>
              <div class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50">
                <div class="py-1">
                  <div class="px-4 py-2 border-b border-gray-200">
                    <p class="text-sm font-medium text-gray-900">{user()?.full_name || user()?.username}</p>
                    <p class="text-sm text-gray-500">{user()?.email}</p>
                  </div>
                  
                  <button
                    type="button"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      navigate('/profile');
                      setShowUserMenu(false);
                    }}
                  >
                    个人资料
                  </button>
                  
                  <button
                    type="button"
                    class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    onClick={() => {
                      navigate('/settings');
                      setShowUserMenu(false);
                    }}
                  >
                    设置
                  </button>
                  
                  <div class="border-t border-gray-200">
                    <button
                      type="button"
                      class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      onClick={() => {
                        handleLogout();
                        setShowUserMenu(false);
                      }}
                    >
                      退出登录
                    </button>
                  </div>
                </div>
              </div>
            </Show>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
