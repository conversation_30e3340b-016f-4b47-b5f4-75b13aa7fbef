// 对象映射器模块
// 负责在领域实体和数据库模型之间进行转换

use crate::shared::errors::{AppError, AppResult};

// 映射器模块
pub mod user_mapper;
pub mod project_mapper;
pub mod task_mapper;
pub mod area_mapper;

// 重新导出所有映射器
pub use user_mapper::*;
pub use project_mapper::*;
pub use task_mapper::*;
pub use area_mapper::*;

/// 通用映射器trait
pub trait Mapper<Domain, Model> {
    /// 从领域实体转换为数据库模型
    fn to_model(domain: &Domain) -> Model;

    /// 从数据库模型转换为领域实体
    fn to_domain(model: &Model) -> AppResult<Domain>;

    /// 批量转换为领域实体
    fn to_domain_list(models: Vec<Model>) -> AppResult<Vec<Domain>> {
        models.iter()
            .map(Self::to_domain)
            .collect()
    }
}





/// 映射器工具函数
pub struct MapperUtils;

impl MapperUtils {
    /// 安全地转换字符串为枚举
    pub fn string_to_enum<T, F>(value: &str, converter: F) -> AppResult<T>
    where
        F: FnOnce(&str) -> Option<T>,
    {
        converter(value).ok_or_else(|| {
            AppError::database(format!("Invalid enum value: {}", value))
        })
    }

    /// 安全地转换数字
    pub fn safe_cast_u32(value: i32) -> AppResult<u32> {
        if value < 0 {
            Err(AppError::database(format!("Cannot convert negative value to u32: {}", value)))
        } else {
            Ok(value as u32)
        }
    }

    /// 安全地转换数字
    pub fn safe_cast_u8(value: i32) -> AppResult<u8> {
        if value < 0 || value > 255 {
            Err(AppError::database(format!("Value out of range for u8: {}", value)))
        } else {
            Ok(value as u8)
        }
    }

    /// 验证必需字段
    pub fn validate_required_field<T>(field: &Option<T>, field_name: &str) -> AppResult<&T> {
        field.as_ref().ok_or_else(|| {
            AppError::database(format!("Required field '{}' is missing", field_name))
        })
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_safe_cast_u32() {
        assert_eq!(MapperUtils::safe_cast_u32(100).unwrap(), 100u32);
        assert!(MapperUtils::safe_cast_u32(-1).is_err());
    }

    #[test]
    fn test_safe_cast_u8() {
        assert_eq!(MapperUtils::safe_cast_u8(100).unwrap(), 100u8);
        assert!(MapperUtils::safe_cast_u8(-1).is_err());
        assert!(MapperUtils::safe_cast_u8(256).is_err());
    }

    #[test]
    fn test_validate_required_field() {
        let some_value = Some("test");
        let none_value: Option<&str> = None;

        assert_eq!(MapperUtils::validate_required_field(&some_value, "test_field").unwrap(), &"test");
        assert!(MapperUtils::validate_required_field(&none_value, "test_field").is_err());
    }
}
