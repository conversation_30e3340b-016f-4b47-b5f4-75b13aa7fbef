// 忘记密码页面
// 用户密码重置界面

import { Component, createSignal, Show } from 'solid-js';
import { A, useNavigate } from '@solidjs/router';
import { Button } from '../../components/ui/Button';
import { Input } from '../../components/ui/Input';
import toast from 'solid-toast';

export const ForgotPasswordPage: Component = () => {
  const navigate = useNavigate();
  
  const [email, setEmail] = createSignal('');
  const [isSubmitting, setIsSubmitting] = createSignal(false);
  const [isEmailSent, setIsEmailSent] = createSignal(false);
  const [errors, setErrors] = createSignal<Record<string, string>>({});

  // 表单验证
  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!email().trim()) {
      newErrors.email = '请输入邮箱地址';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email())) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理表单提交
  const handleSubmit = async (e: Event) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      // 这里应该调用重置密码API
      // const response = await api.resetPassword(email());
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      setIsEmailSent(true);
      toast.success('密码重置邮件已发送');
    } catch (error) {
      console.error('Password reset error:', error);
      toast.error('发送重置邮件失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  // 重新发送邮件
  const handleResendEmail = async () => {
    setIsSubmitting(true);
    
    try {
      // 重新发送邮件逻辑
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('重置邮件已重新发送');
    } catch (error) {
      toast.error('重新发送失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div class="max-w-md w-full space-y-8">
        {/* 头部 */}
        <div>
          <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-yellow-100">
            <svg class="h-8 w-8 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          
          <Show
            when={!isEmailSent()}
            fallback={
              <>
                <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
                  检查您的邮箱
                </h2>
                <p class="mt-2 text-center text-sm text-gray-600">
                  我们已向 <span class="font-medium text-gray-900">{email()}</span> 发送了密码重置链接
                </p>
              </>
            }
          >
            <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
              重置密码
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
              输入您的邮箱地址，我们将发送重置链接给您
            </p>
          </Show>
        </div>

        <Show
          when={!isEmailSent()}
          fallback={
            <div class="mt-8 space-y-6">
              {/* 邮件发送成功状态 */}
              <div class="rounded-md bg-green-50 p-4">
                <div class="flex">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">
                      邮件已发送
                    </h3>
                    <div class="mt-2 text-sm text-green-700">
                      <p>请检查您的邮箱并点击重置链接。如果没有收到邮件，请检查垃圾邮件文件夹。</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 操作按钮 */}
              <div class="space-y-3">
                <Button
                  variant="outline"
                  class="w-full"
                  onClick={handleResendEmail}
                  loading={isSubmitting()}
                  disabled={isSubmitting()}
                >
                  重新发送邮件
                </Button>

                <Button
                  variant="ghost"
                  class="w-full"
                  onClick={() => navigate('/auth/login')}
                >
                  返回登录
                </Button>
              </div>

              {/* 帮助信息 */}
              <div class="text-center">
                <p class="text-xs text-gray-500">
                  没有收到邮件？请检查垃圾邮件文件夹或{' '}
                  <button
                    type="button"
                    class="text-blue-600 hover:text-blue-500"
                    onClick={() => {
                      // 联系支持逻辑
                      toast.info('请联系客服获取帮助');
                    }}
                  >
                    联系客服
                  </button>
                </p>
              </div>
            </div>
          }
        >
          {/* 重置密码表单 */}
          <form class="mt-8 space-y-6" onSubmit={handleSubmit}>
            <div>
              <Input
                label="邮箱地址"
                type="email"
                value={email()}
                onInput={(e) => {
                  setEmail(e.currentTarget.value);
                  if (errors().email) {
                    setErrors(prev => ({ ...prev, email: '' }));
                  }
                }}
                error={errors().email}
                placeholder="请输入您的邮箱地址"
                required
                autocomplete="email"
                leftIcon={() => (
                  <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                )}
              />
            </div>

            <div>
              <Button
                type="submit"
                class="w-full"
                loading={isSubmitting()}
                disabled={isSubmitting()}
              >
                {isSubmitting() ? '发送中...' : '发送重置链接'}
              </Button>
            </div>
          </form>
        </Show>

        {/* 返回登录链接 */}
        <Show when={!isEmailSent()}>
          <div class="text-center">
            <p class="text-sm text-gray-600">
              记起密码了？{' '}
              <A
                href="/auth/login"
                class="font-medium text-blue-600 hover:text-blue-500"
              >
                返回登录
              </A>
            </p>
          </div>
        </Show>

        {/* 安全提示 */}
        <div class="mt-6 p-4 bg-blue-50 rounded-md">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-blue-800">
                安全提示
              </h3>
              <div class="mt-2 text-sm text-blue-700">
                <ul class="list-disc list-inside space-y-1">
                  <li>重置链接将在24小时后过期</li>
                  <li>每个链接只能使用一次</li>
                  <li>如果您没有请求重置密码，请忽略此邮件</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
