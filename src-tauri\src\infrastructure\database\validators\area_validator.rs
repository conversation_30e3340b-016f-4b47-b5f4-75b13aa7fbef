// 领域数据验证器
// 实现领域相关的数据验证和业务规则检查

use crate::domain::entities::area::{CreateAreaData, UpdateAreaData};
use crate::infrastructure::database::validators::{Validator, ValidationUtils, ValidationContext};
use crate::shared::errors::{AppError, AppResult};

/// 领域创建数据验证器
pub struct CreateAreaValidator;

impl Validator<CreateAreaData> for CreateAreaValidator {
    fn validate(&self, data: &CreateAreaData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 验证领域名称
        ctx.validate(
            || ValidationUtils::validate_required(&data.name, "领域名称"),
            "name_required"
        );
        
        ctx.validate(
            || ValidationUtils::validate_length(&data.name, "领域名称", 1, 100),
            "name_length"
        );

        // 验证描述
        if let Some(description) = &data.description {
            ctx.validate(
                || ValidationUtils::validate_length(description, "领域描述", 0, 1000),
                "description_length"
            );
        }

        // 验证标准
        if let Some(standard) = &data.standard {
            ctx.validate(
                || ValidationUtils::validate_length(standard, "领域标准", 0, 2000),
                "standard_length"
            );
        }

        // 验证图标名称
        if let Some(icon_name) = &data.icon_name {
            ctx.validate(
                || ValidationUtils::validate_length(icon_name, "图标名称", 1, 50),
                "icon_name_length"
            );
            
            // 验证图标名称格式（只允许字母、数字、下划线、连字符）
            ctx.validate(
                || Self::validate_icon_name(icon_name),
                "icon_name_format"
            );
        }

        // 验证颜色
        if let Some(color_hex) = &data.color_hex {
            ctx.validate(
                || ValidationUtils::validate_hex_color(color_hex),
                "color_hex_format"
            );
        }

        // 验证排序顺序
        ctx.validate(
            || ValidationUtils::validate_range(
                data.sort_order,
                "排序顺序",
                0,
                9999
            ),
            "sort_order_range"
        );

        // 验证创建者
        ctx.validate(
            || ValidationUtils::validate_required(&data.created_by, "创建者"),
            "created_by_required"
        );

        ctx.finish()
    }

    fn sanitize(&self, mut data: CreateAreaData) -> CreateAreaData {
        // 清理领域名称
        data.name = ValidationUtils::sanitize_string(data.name);
        
        // 清理描述
        data.description = ValidationUtils::sanitize_optional_string(data.description);

        // 清理标准
        data.standard = ValidationUtils::sanitize_optional_string(data.standard);

        // 清理图标名称
        if let Some(icon_name) = data.icon_name {
            data.icon_name = Some(ValidationUtils::sanitize_string(icon_name));
        }

        // 清理颜色（转为大写）
        if let Some(color_hex) = data.color_hex {
            data.color_hex = Some(color_hex.to_uppercase());
        }

        // 清理创建者
        data.created_by = ValidationUtils::sanitize_string(data.created_by);

        data
    }
}

impl CreateAreaValidator {
    fn validate_icon_name(icon_name: &str) -> AppResult<()> {
        let valid_chars = icon_name.chars().all(|c| {
            c.is_alphanumeric() || c == '_' || c == '-'
        });

        if !valid_chars {
            return Err(AppError::validation(
                "图标名称只能包含字母、数字、下划线和连字符"
            ));
        }

        Ok(())
    }
}

/// 领域更新数据验证器
pub struct UpdateAreaValidator;

impl Validator<UpdateAreaData> for UpdateAreaValidator {
    fn validate(&self, data: &UpdateAreaData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 验证领域名称
        if let Some(name) = &data.name {
            ctx.validate(
                || ValidationUtils::validate_required(name, "领域名称"),
                "name_required"
            );
            
            ctx.validate(
                || ValidationUtils::validate_length(name, "领域名称", 1, 100),
                "name_length"
            );
        }

        // 验证描述
        if let Some(Some(description)) = &data.description {
            ctx.validate(
                || ValidationUtils::validate_length(description, "领域描述", 0, 1000),
                "description_length"
            );
        }

        // 验证标准
        if let Some(Some(standard)) = &data.standard {
            ctx.validate(
                || ValidationUtils::validate_length(standard, "领域标准", 0, 2000),
                "standard_length"
            );
        }

        // 验证图标名称
        if let Some(Some(icon_name)) = &data.icon_name {
            ctx.validate(
                || ValidationUtils::validate_length(icon_name, "图标名称", 1, 50),
                "icon_name_length"
            );
            
            ctx.validate(
                || CreateAreaValidator::validate_icon_name(icon_name),
                "icon_name_format"
            );
        }

        // 验证颜色
        if let Some(Some(color_hex)) = &data.color_hex {
            ctx.validate(
                || ValidationUtils::validate_hex_color(color_hex),
                "color_hex_format"
            );
        }

        // 验证排序顺序
        if let Some(sort_order) = data.sort_order {
            ctx.validate(
                || ValidationUtils::validate_range(
                    sort_order,
                    "排序顺序",
                    0,
                    9999
                ),
                "sort_order_range"
            );
        }

        ctx.finish()
    }

    fn sanitize(&self, mut data: UpdateAreaData) -> UpdateAreaData {
        // 清理领域名称
        if let Some(name) = data.name {
            data.name = Some(ValidationUtils::sanitize_string(name));
        }
        
        // 清理描述
        if let Some(description) = data.description {
            data.description = Some(ValidationUtils::sanitize_optional_string(description));
        }

        // 清理标准
        if let Some(standard) = data.standard {
            data.standard = Some(ValidationUtils::sanitize_optional_string(standard));
        }

        // 清理图标名称
        if let Some(Some(icon_name)) = data.icon_name {
            data.icon_name = Some(Some(ValidationUtils::sanitize_string(icon_name)));
        }

        // 清理颜色（转为大写）
        if let Some(Some(color_hex)) = data.color_hex {
            data.color_hex = Some(Some(color_hex.to_uppercase()));
        }

        data
    }
}

/// 领域业务规则验证器
pub struct AreaBusinessRuleValidator;

impl AreaBusinessRuleValidator {
    /// 验证领域名称唯一性
    pub async fn validate_name_uniqueness<F, Fut>(
        name: &str,
        user_id: &str,
        exclude_id: Option<&str>,
        check_fn: F,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        let exists = check_fn().await?;
        if exists {
            return Err(AppError::validation(&format!(
                "领域名称 '{}' 已存在",
                name
            )));
        }
        Ok(())
    }

    /// 验证领域删除权限
    pub fn validate_deletion_permission(
        area_creator: &str,
        current_user: &str,
        is_admin: bool,
        has_projects: bool,
        has_tasks: bool,
    ) -> AppResult<()> {
        // 只有领域创建者或管理员可以删除领域
        if area_creator != current_user && !is_admin {
            return Err(AppError::validation("只有领域创建者或管理员可以删除领域"));
        }

        // 有关联项目的领域不能删除
        if has_projects {
            return Err(AppError::validation("有关联项目的领域不能删除"));
        }

        // 有关联任务的领域不能删除
        if has_tasks {
            return Err(AppError::validation("有关联任务的领域不能删除"));
        }

        Ok(())
    }

    /// 验证领域归档条件
    pub fn validate_archive_requirements(
        has_active_projects: bool,
        has_active_tasks: bool,
    ) -> AppResult<()> {
        // 有活跃项目的领域不能归档
        if has_active_projects {
            return Err(AppError::validation("有活跃项目的领域不能归档"));
        }

        // 有活跃任务的领域不能归档
        if has_active_tasks {
            return Err(AppError::validation("有活跃任务的领域不能归档"));
        }

        Ok(())
    }

    /// 验证领域状态转换
    pub fn validate_status_transition(
        current_active: bool,
        new_active: bool,
        has_dependencies: bool,
    ) -> AppResult<()> {
        // 如果要停用领域，检查是否有依赖
        if current_active && !new_active && has_dependencies {
            return Err(AppError::validation(
                "有关联项目或任务的领域不能停用"
            ));
        }

        Ok(())
    }

    /// 验证排序顺序冲突
    pub async fn validate_sort_order_conflict<F, Fut>(
        sort_order: i32,
        user_id: &str,
        exclude_id: Option<&str>,
        check_fn: F,
    ) -> AppResult<()>
    where
        F: FnOnce() -> Fut,
        Fut: std::future::Future<Output = AppResult<bool>>,
    {
        let exists = check_fn().await?;
        if exists {
            return Err(AppError::validation(&format!(
                "排序顺序 {} 已被其他领域使用",
                sort_order
            )));
        }
        Ok(())
    }

    /// 验证领域层级限制
    pub fn validate_area_limits(
        current_count: usize,
        is_creating: bool,
    ) -> AppResult<()> {
        const MAX_AREAS_PER_USER: usize = 50;

        if is_creating && current_count >= MAX_AREAS_PER_USER {
            return Err(AppError::validation(&format!(
                "每个用户最多只能创建 {} 个领域",
                MAX_AREAS_PER_USER
            )));
        }

        Ok(())
    }

    /// 验证领域数据完整性
    pub fn validate_data_integrity(data: &CreateAreaData) -> AppResult<()> {
        let mut ctx = ValidationUtils::create_validation_context();

        // 领域名称不能包含特殊字符
        let invalid_chars = ['<', '>', ':', '"', '|', '?', '*', '/', '\\'];
        for ch in invalid_chars {
            if data.name.contains(ch) {
                ctx.validate(
                    || Err(AppError::validation(&format!(
                        "领域名称不能包含特殊字符: {}",
                        ch
                    ))),
                    "name_special_chars"
                );
                break;
            }
        }

        // 如果设置了图标，建议也设置颜色
        if data.icon_name.is_some() && data.color_hex.is_none() {
            ctx.validate(
                || Err(AppError::validation("设置了图标的领域建议也设置颜色")),
                "icon_color_consistency"
            );
        }

        // 验证预定义的图标名称
        if let Some(icon_name) = &data.icon_name {
            let valid_icons = [
                "folder", "briefcase", "heart", "home", "book", "star",
                "target", "trophy", "lightbulb", "rocket", "shield", "globe"
            ];
            
            if !valid_icons.contains(&icon_name.as_str()) {
                ctx.validate(
                    || Err(AppError::validation(&format!(
                        "图标名称 '{}' 不在预定义列表中",
                        icon_name
                    ))),
                    "icon_predefined"
                );
            }
        }

        // 验证颜色对比度（简单检查）
        if let Some(color_hex) = &data.color_hex {
            if color_hex == "#FFFFFF" || color_hex == "#000000" {
                ctx.validate(
                    || Err(AppError::validation("不建议使用纯白色或纯黑色作为领域颜色")),
                    "color_contrast"
                );
            }
        }

        ctx.finish()
    }

    /// 验证领域标准格式
    pub fn validate_standard_format(standard: &str) -> AppResult<()> {
        // 标准应该是明确的、可衡量的描述
        if standard.len() < 10 {
            return Err(AppError::validation(
                "领域标准应该是明确的描述，至少10个字符"
            ));
        }

        // 检查是否包含问号（标准应该是陈述句，不是疑问句）
        if standard.contains('?') {
            return Err(AppError::validation(
                "领域标准应该是明确的陈述，不应该包含疑问句"
            ));
        }

        Ok(())
    }

    /// 验证领域激活条件
    pub fn validate_activation_requirements(
        has_name: bool,
        has_description: bool,
        has_standard: bool,
    ) -> AppResult<()> {
        if !has_name {
            return Err(AppError::validation("激活领域需要设置名称"));
        }

        // 建议设置描述和标准
        if !has_description {
            return Err(AppError::validation("建议为领域设置描述"));
        }

        if !has_standard {
            return Err(AppError::validation("建议为领域设置标准"));
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_create_area_validation() {
        let validator = CreateAreaValidator;
        
        // 有效数据
        let valid_data = CreateAreaData {
            name: "Work".to_string(),
            description: Some("Work related activities".to_string()),
            standard: Some("Maintain work-life balance".to_string()),
            icon_name: Some("briefcase".to_string()),
            color_hex: Some("#FF6B6B".to_string()),
            sort_order: 1,
            created_by: "user123".to_string(),
        };
        
        assert!(validator.validate(&valid_data).is_ok());

        // 无效名称
        let invalid_data = CreateAreaData {
            name: "".to_string(),
            ..valid_data.clone()
        };
        assert!(validator.validate(&invalid_data).is_err());

        // 无效颜色
        let invalid_data = CreateAreaData {
            color_hex: Some("invalid-color".to_string()),
            ..valid_data.clone()
        };
        assert!(validator.validate(&invalid_data).is_err());
    }

    #[test]
    fn test_icon_name_validation() {
        assert!(CreateAreaValidator::validate_icon_name("briefcase").is_ok());
        assert!(CreateAreaValidator::validate_icon_name("work_icon").is_ok());
        assert!(CreateAreaValidator::validate_icon_name("icon-name").is_ok());
        assert!(CreateAreaValidator::validate_icon_name("invalid@icon").is_err());
        assert!(CreateAreaValidator::validate_icon_name("icon with space").is_err());
    }

    #[test]
    fn test_data_sanitization() {
        let validator = CreateAreaValidator;
        
        let data = CreateAreaData {
            name: "  Work Area  ".to_string(),
            description: Some("  Work related activities  ".to_string()),
            standard: Some("  Maintain balance  ".to_string()),
            icon_name: Some("  briefcase  ".to_string()),
            color_hex: Some("#ff6b6b".to_string()),
            sort_order: 1,
            created_by: "  user123  ".to_string(),
        };

        let sanitized = validator.sanitize(data);
        
        assert_eq!(sanitized.name, "Work Area");
        assert_eq!(sanitized.description, Some("Work related activities".to_string()));
        assert_eq!(sanitized.standard, Some("Maintain balance".to_string()));
        assert_eq!(sanitized.icon_name, Some("briefcase".to_string()));
        assert_eq!(sanitized.color_hex, Some("#FF6B6B".to_string()));
        assert_eq!(sanitized.created_by, "user123");
    }

    #[test]
    fn test_business_rules() {
        // 测试删除权限
        assert!(AreaBusinessRuleValidator::validate_deletion_permission(
            "user1", "user1", false, false, false
        ).is_ok());
        
        assert!(AreaBusinessRuleValidator::validate_deletion_permission(
            "user1", "user2", false, false, false
        ).is_err());
        
        assert!(AreaBusinessRuleValidator::validate_deletion_permission(
            "user1", "user1", false, true, false
        ).is_err());

        // 测试归档条件
        assert!(AreaBusinessRuleValidator::validate_archive_requirements(
            false, false
        ).is_ok());
        
        assert!(AreaBusinessRuleValidator::validate_archive_requirements(
            true, false
        ).is_err());
    }

    #[test]
    fn test_standard_format_validation() {
        assert!(AreaBusinessRuleValidator::validate_standard_format(
            "Maintain a healthy work-life balance"
        ).is_ok());
        
        assert!(AreaBusinessRuleValidator::validate_standard_format(
            "Short"
        ).is_err());
        
        assert!(AreaBusinessRuleValidator::validate_standard_format(
            "What should I do?"
        ).is_err());
    }
}
