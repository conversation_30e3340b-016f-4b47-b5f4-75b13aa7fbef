// WebSocket处理器
// 实现实时通信功能，如通知、状态更新等

use crate::shared::errors::{AppError, AppResult};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{broadcast, RwLock};
use uuid::Uuid;

/// WebSocket消息类型
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum WebSocketMessage {
    /// 连接确认
    Connected { session_id: String },
    /// 心跳
    Ping,
    Pong,
    /// 用户认证
    Authenticate { token: String },
    /// 认证结果
    AuthResult { success: bool, user_id: Option<String> },
    /// 项目更新通知
    ProjectUpdated { project_id: String, changes: serde_json::Value },
    /// 任务更新通知
    TaskUpdated { task_id: String, changes: serde_json::Value },
    /// 系统通知
    SystemNotification { title: String, message: String, level: NotificationLevel },
    /// 实时统计更新
    StatsUpdate { stats: serde_json::Value },
    /// 错误消息
    Error { message: String },
}

/// 通知级别
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum NotificationLevel {
    Info,
    Warning,
    Error,
    Success,
}

/// WebSocket会话
#[derive(Debug, Clone)]
pub struct WebSocketSession {
    pub id: String,
    pub user_id: Option<String>,
    pub connected_at: chrono::DateTime<chrono::Utc>,
    pub last_ping: chrono::DateTime<chrono::Utc>,
}

impl WebSocketSession {
    pub fn new() -> Self {
        let now = chrono::Utc::now();
        Self {
            id: Uuid::new_v4().to_string(),
            user_id: None,
            connected_at: now,
            last_ping: now,
        }
    }

    pub fn authenticate(&mut self, user_id: String) {
        self.user_id = Some(user_id);
    }

    pub fn update_ping(&mut self) {
        self.last_ping = chrono::Utc::now();
    }

    pub fn is_authenticated(&self) -> bool {
        self.user_id.is_some()
    }

    pub fn is_expired(&self, timeout_seconds: i64) -> bool {
        let now = chrono::Utc::now();
        (now - self.last_ping).num_seconds() > timeout_seconds
    }
}

/// WebSocket管理器
pub struct WebSocketManager {
    sessions: Arc<RwLock<HashMap<String, WebSocketSession>>>,
    message_sender: broadcast::Sender<(String, WebSocketMessage)>,
    _message_receiver: broadcast::Receiver<(String, WebSocketMessage)>,
}

impl WebSocketManager {
    /// 创建新的WebSocket管理器
    pub fn new() -> Self {
        let (sender, receiver) = broadcast::channel(1000);
        
        Self {
            sessions: Arc::new(RwLock::new(HashMap::new())),
            message_sender: sender,
            _message_receiver: receiver,
        }
    }

    /// 创建新会话
    pub async fn create_session(&self) -> WebSocketSession {
        let session = WebSocketSession::new();
        let session_id = session.id.clone();
        
        {
            let mut sessions = self.sessions.write().await;
            sessions.insert(session_id.clone(), session.clone());
        }

        tracing::info!(session_id = %session_id, "WebSocket session created");
        session
    }

    /// 认证会话
    pub async fn authenticate_session(&self, session_id: &str, user_id: String) -> AppResult<()> {
        let mut sessions = self.sessions.write().await;
        
        if let Some(session) = sessions.get_mut(session_id) {
            session.authenticate(user_id.clone());
            tracing::info!(
                session_id = %session_id,
                user_id = %user_id,
                "WebSocket session authenticated"
            );
            Ok(())
        } else {
            Err(AppError::not_found("会话不存在"))
        }
    }

    /// 移除会话
    pub async fn remove_session(&self, session_id: &str) -> AppResult<()> {
        let mut sessions = self.sessions.write().await;
        
        if sessions.remove(session_id).is_some() {
            tracing::info!(session_id = %session_id, "WebSocket session removed");
            Ok(())
        } else {
            Err(AppError::not_found("会话不存在"))
        }
    }

    /// 更新会话心跳
    pub async fn update_session_ping(&self, session_id: &str) -> AppResult<()> {
        let mut sessions = self.sessions.write().await;
        
        if let Some(session) = sessions.get_mut(session_id) {
            session.update_ping();
            Ok(())
        } else {
            Err(AppError::not_found("会话不存在"))
        }
    }

    /// 获取会话信息
    pub async fn get_session(&self, session_id: &str) -> Option<WebSocketSession> {
        let sessions = self.sessions.read().await;
        sessions.get(session_id).cloned()
    }

    /// 广播消息给所有已认证用户
    pub async fn broadcast_to_authenticated(&self, message: WebSocketMessage) -> AppResult<usize> {
        let sessions = self.sessions.read().await;
        let mut count = 0;

        for (session_id, session) in sessions.iter() {
            if session.is_authenticated() {
                if let Err(e) = self.message_sender.send((session_id.clone(), message.clone())) {
                    tracing::warn!(
                        session_id = %session_id,
                        error = %e,
                        "Failed to send broadcast message"
                    );
                } else {
                    count += 1;
                }
            }
        }

        tracing::debug!(count = %count, "Broadcast message sent to authenticated sessions");
        Ok(count)
    }

    /// 发送消息给特定用户
    pub async fn send_to_user(&self, user_id: &str, message: WebSocketMessage) -> AppResult<usize> {
        let sessions = self.sessions.read().await;
        let mut count = 0;

        for (session_id, session) in sessions.iter() {
            if let Some(session_user_id) = &session.user_id {
                if session_user_id == user_id {
                    if let Err(e) = self.message_sender.send((session_id.clone(), message.clone())) {
                        tracing::warn!(
                            session_id = %session_id,
                            user_id = %user_id,
                            error = %e,
                            "Failed to send user message"
                        );
                    } else {
                        count += 1;
                    }
                }
            }
        }

        tracing::debug!(
            user_id = %user_id,
            count = %count,
            "Message sent to user sessions"
        );
        Ok(count)
    }

    /// 发送消息给特定会话
    pub async fn send_to_session(&self, session_id: &str, message: WebSocketMessage) -> AppResult<()> {
        if let Err(e) = self.message_sender.send((session_id.to_string(), message)) {
            tracing::warn!(
                session_id = %session_id,
                error = %e,
                "Failed to send session message"
            );
            Err(AppError::internal("发送消息失败"))
        } else {
            Ok(())
        }
    }

    /// 清理过期会话
    pub async fn cleanup_expired_sessions(&self, timeout_seconds: i64) -> AppResult<usize> {
        let mut sessions = self.sessions.write().await;
        let mut expired_sessions = Vec::new();

        for (session_id, session) in sessions.iter() {
            if session.is_expired(timeout_seconds) {
                expired_sessions.push(session_id.clone());
            }
        }

        let count = expired_sessions.len();
        for session_id in expired_sessions {
            sessions.remove(&session_id);
            tracing::info!(session_id = %session_id, "Expired WebSocket session removed");
        }

        if count > 0 {
            tracing::info!(count = %count, "Cleaned up expired WebSocket sessions");
        }

        Ok(count)
    }

    /// 获取会话统计
    pub async fn get_session_stats(&self) -> SessionStats {
        let sessions = self.sessions.read().await;
        let total_sessions = sessions.len();
        let authenticated_sessions = sessions.values()
            .filter(|s| s.is_authenticated())
            .count();

        SessionStats {
            total_sessions,
            authenticated_sessions,
            anonymous_sessions: total_sessions - authenticated_sessions,
        }
    }

    /// 获取消息发送器的克隆
    pub fn get_message_sender(&self) -> broadcast::Sender<(String, WebSocketMessage)> {
        self.message_sender.clone()
    }

    /// 创建消息接收器
    pub fn subscribe(&self) -> broadcast::Receiver<(String, WebSocketMessage)> {
        self.message_sender.subscribe()
    }
}

/// 会话统计
#[derive(Debug, Clone, Serialize)]
pub struct SessionStats {
    pub total_sessions: usize,
    pub authenticated_sessions: usize,
    pub anonymous_sessions: usize,
}

/// WebSocket事件处理器
pub struct WebSocketEventHandler {
    manager: Arc<WebSocketManager>,
}

impl WebSocketEventHandler {
    pub fn new(manager: Arc<WebSocketManager>) -> Self {
        Self { manager }
    }

    /// 处理连接事件
    pub async fn handle_connect(&self) -> AppResult<WebSocketSession> {
        let session = self.manager.create_session().await;
        Ok(session)
    }

    /// 处理断开连接事件
    pub async fn handle_disconnect(&self, session_id: &str) -> AppResult<()> {
        self.manager.remove_session(session_id).await
    }

    /// 处理消息
    pub async fn handle_message(
        &self,
        session_id: &str,
        message: WebSocketMessage,
    ) -> AppResult<Option<WebSocketMessage>> {
        match message {
            WebSocketMessage::Ping => {
                self.manager.update_session_ping(session_id).await?;
                Ok(Some(WebSocketMessage::Pong))
            }
            WebSocketMessage::Authenticate { token } => {
                // 这里需要验证token并获取用户ID
                // 暂时模拟认证成功
                let user_id = "mock_user_id".to_string();
                self.manager.authenticate_session(session_id, user_id.clone()).await?;
                Ok(Some(WebSocketMessage::AuthResult {
                    success: true,
                    user_id: Some(user_id),
                }))
            }
            _ => {
                tracing::warn!(
                    session_id = %session_id,
                    message_type = ?std::mem::discriminant(&message),
                    "Unhandled WebSocket message type"
                );
                Ok(None)
            }
        }
    }

    /// 通知项目更新
    pub async fn notify_project_updated(
        &self,
        project_id: &str,
        changes: serde_json::Value,
        user_id: Option<&str>,
    ) -> AppResult<()> {
        let message = WebSocketMessage::ProjectUpdated {
            project_id: project_id.to_string(),
            changes,
        };

        if let Some(user_id) = user_id {
            self.manager.send_to_user(user_id, message).await?;
        } else {
            self.manager.broadcast_to_authenticated(message).await?;
        }

        Ok(())
    }

    /// 通知任务更新
    pub async fn notify_task_updated(
        &self,
        task_id: &str,
        changes: serde_json::Value,
        user_id: Option<&str>,
    ) -> AppResult<()> {
        let message = WebSocketMessage::TaskUpdated {
            task_id: task_id.to_string(),
            changes,
        };

        if let Some(user_id) = user_id {
            self.manager.send_to_user(user_id, message).await?;
        } else {
            self.manager.broadcast_to_authenticated(message).await?;
        }

        Ok(())
    }

    /// 发送系统通知
    pub async fn send_system_notification(
        &self,
        title: String,
        message: String,
        level: NotificationLevel,
        user_id: Option<&str>,
    ) -> AppResult<()> {
        let notification = WebSocketMessage::SystemNotification {
            title,
            message,
            level,
        };

        if let Some(user_id) = user_id {
            self.manager.send_to_user(user_id, notification).await?;
        } else {
            self.manager.broadcast_to_authenticated(notification).await?;
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_websocket_session_creation() {
        let session = WebSocketSession::new();
        assert!(!session.id.is_empty());
        assert!(!session.is_authenticated());
        assert!(!session.is_expired(60));
    }

    #[tokio::test]
    async fn test_websocket_manager_session_lifecycle() {
        let manager = WebSocketManager::new();
        
        // 创建会话
        let session = manager.create_session().await;
        let session_id = session.id.clone();
        
        // 验证会话存在
        assert!(manager.get_session(&session_id).await.is_some());
        
        // 认证会话
        let user_id = "test_user".to_string();
        assert!(manager.authenticate_session(&session_id, user_id.clone()).await.is_ok());
        
        // 验证认证状态
        let session = manager.get_session(&session_id).await.unwrap();
        assert!(session.is_authenticated());
        assert_eq!(session.user_id, Some(user_id));
        
        // 移除会话
        assert!(manager.remove_session(&session_id).await.is_ok());
        assert!(manager.get_session(&session_id).await.is_none());
    }

    #[tokio::test]
    async fn test_websocket_message_broadcasting() {
        let manager = WebSocketManager::new();
        
        // 创建并认证会话
        let session = manager.create_session().await;
        let user_id = "test_user".to_string();
        manager.authenticate_session(&session.id, user_id.clone()).await.unwrap();
        
        // 广播消息
        let message = WebSocketMessage::SystemNotification {
            title: "Test".to_string(),
            message: "Test message".to_string(),
            level: NotificationLevel::Info,
        };
        
        let count = manager.broadcast_to_authenticated(message).await.unwrap();
        assert_eq!(count, 1);
    }
}
