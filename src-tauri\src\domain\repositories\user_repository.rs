// 用户仓储接口
// 定义用户数据访问的抽象接口

use crate::domain::entities::user::{User, CreateUserData, UpdateUserData, UserQuery};
use crate::shared::types::{EntityId, QueryParams, Pagination};
use crate::shared::errors::AppResult;
use async_trait::async_trait;

/// 用户仓储接口
#[async_trait]
pub trait UserRepository: Send + Sync {
    /// 创建用户
    async fn create(&self, data: CreateUserData) -> AppResult<User>;

    /// 根据ID查找用户
    async fn find_by_id(&self, id: &EntityId) -> AppResult<Option<User>>;

    /// 根据用户名查找用户
    async fn find_by_username(&self, username: &str) -> AppResult<Option<User>>;

    /// 根据邮箱查找用户
    async fn find_by_email(&self, email: &str) -> AppResult<Option<User>>;

    /// 更新用户
    async fn update(&self, id: &EntityId, data: UpdateUserData) -> AppResult<User>;

    /// 删除用户
    async fn delete(&self, id: &EntityId) -> AppResult<()>;

    /// 查询用户列表
    async fn find_all(&self, query: UserQuery, params: QueryParams) -> AppResult<Vec<User>>;

    /// 统计用户数量
    async fn count(&self, query: UserQuery) -> AppResult<u64>;

    /// 检查用户名是否存在
    async fn username_exists(&self, username: &str) -> AppResult<bool>;

    /// 检查邮箱是否存在
    async fn email_exists(&self, email: &str) -> AppResult<bool>;

    /// 激活用户
    async fn activate(&self, id: &EntityId) -> AppResult<()>;

    /// 停用用户
    async fn deactivate(&self, id: &EntityId) -> AppResult<()>;

    /// 更新最后登录时间
    async fn update_last_login(&self, id: &EntityId) -> AppResult<()>;

    /// 更新密码
    async fn update_password(&self, id: &EntityId, password_hash: &str) -> AppResult<()>;

    /// 获取活跃用户列表
    async fn find_active_users(&self, params: QueryParams) -> AppResult<Vec<User>>;

    /// 获取最近注册的用户
    async fn find_recent_users(&self, days: i32, params: QueryParams) -> AppResult<Vec<User>>;
}
