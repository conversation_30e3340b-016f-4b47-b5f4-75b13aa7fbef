import { Component, ParentComponent, createSignal } from 'solid-js';
import { A, useNavigate } from '@solidjs/router';
import { useAuth } from '../contexts/AuthContext';
import { useTheme } from '../contexts/ThemeContext';

// 图标组件
const HomeIcon: Component = () => (
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
  </svg>
);

const ProjectIcon: Component = () => (
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
  </svg>
);

const TaskIcon: Component = () => (
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
  </svg>
);

const AreaIcon: Component = () => (
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z" />
  </svg>
);

const SettingsIcon: Component = () => (
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
);

const LogoutIcon: Component = () => (
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
  </svg>
);

const ThemeIcon: Component = () => (
  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
  </svg>
);

const MenuIcon: Component = () => (
  <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
  </svg>
);

// 导航项目类型
interface NavItem {
  path: string;
  label: string;
  icon: Component;
}

// 导航项目
const navItems: NavItem[] = [
  { path: '/dashboard', label: '仪表板', icon: HomeIcon },
  { path: '/projects', label: '项目', icon: ProjectIcon },
  { path: '/tasks', label: '任务', icon: TaskIcon },
  { path: '/areas', label: '领域', icon: AreaIcon },
  { path: '/settings', label: '设置', icon: SettingsIcon },
];

// 侧边栏组件
const Sidebar: Component<{ isOpen: () => boolean; onClose: () => void }> = (props) => {
  const { user, logout } = useAuth();
  const { toggleTheme, actualTheme } = useTheme();
  const navigate = useNavigate();

  const handleLogout = async () => {
    await logout();
    navigate('/login');
  };

  return (
    <div class={`fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out ${props.isOpen() ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 lg:static lg:inset-0`}>
      {/* 侧边栏头部 */}
      <div class="flex items-center justify-between h-16 px-6 bg-blue-600 dark:bg-blue-700">
        <h1 class="text-xl font-bold text-white">PaoLife</h1>
        <button
          onClick={props.onClose}
          class="lg:hidden text-white hover:text-gray-200"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      {/* 用户信息 */}
      <div class="p-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
            {user()?.username?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium text-gray-900 dark:text-white">
              {user()?.full_name || user()?.username}
            </p>
            <p class="text-xs text-gray-500 dark:text-gray-400">
              {user()?.email}
            </p>
          </div>
        </div>
      </div>

      {/* 导航菜单 */}
      <nav class="mt-6">
        <div class="px-3">
          {navItems.map((item) => (
            <A
              href={item.path}
              class="flex items-center px-3 py-2 mt-1 text-sm font-medium rounded-md transition-colors duration-200 hover:bg-gray-100 dark:hover:bg-gray-700"
              activeClass="bg-blue-100 dark:bg-blue-900 text-blue-700 dark:text-blue-200"
              inactiveClass="text-gray-700 dark:text-gray-200"
            >
              <item.icon />
              <span class="ml-3">{item.label}</span>
            </A>
          ))}
        </div>
      </nav>

      {/* 底部操作 */}
      <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200 dark:border-gray-700">
        <div class="space-y-2">
          <button
            onClick={toggleTheme}
            class="flex items-center w-full px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200"
          >
            <ThemeIcon />
            <span class="ml-3">
              {actualTheme() === 'dark' ? '浅色模式' : '深色模式'}
            </span>
          </button>
          <button
            onClick={handleLogout}
            class="flex items-center w-full px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 rounded-md hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors duration-200"
          >
            <LogoutIcon />
            <span class="ml-3">退出登录</span>
          </button>
        </div>
      </div>
    </div>
  );
};

// 顶部导航栏组件
const TopBar: Component<{ onMenuClick: () => void }> = (props) => {
  return (
    <header class="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700 lg:hidden">
      <div class="flex items-center justify-between h-16 px-4">
        <button
          onClick={props.onMenuClick}
          class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"
        >
          <MenuIcon />
        </button>
        <h1 class="text-lg font-semibold text-gray-900 dark:text-white">PaoLife</h1>
        <div class="w-6"></div> {/* 占位符保持居中 */}
      </div>
    </header>
  );
};

// 主布局组件
export const Layout: ParentComponent = (props) => {
  const [sidebarOpen, setSidebarOpen] = createSignal(false);

  const toggleSidebar = () => setSidebarOpen(!sidebarOpen());
  const closeSidebar = () => setSidebarOpen(false);

  return (
    <div class="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* 侧边栏 */}
      <Sidebar isOpen={sidebarOpen} onClose={closeSidebar} />

      {/* 遮罩层 (移动端) */}
      {sidebarOpen() && (
        <div
          class="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={closeSidebar}
        />
      )}

      {/* 主内容区域 */}
      <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">
        {/* 顶部导航栏 (移动端) */}
        <TopBar onMenuClick={toggleSidebar} />

        {/* 主内容 */}
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-gray-50 dark:bg-gray-900">
          <div class="container mx-auto px-6 py-8">
            {props.children}
          </div>
        </main>
      </div>
    </div>
  );
};
