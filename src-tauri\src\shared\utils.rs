// 工具函数
// 提供应用中通用的工具函数和辅助方法

use crate::shared::types::EntityId;
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;

/// 生成唯一ID
pub fn generate_id() -> EntityId {
    Uuid::new_v4().to_string()
}

/// 获取当前时间戳
pub fn now() -> DateTime<Utc> {
    Utc::now()
}

/// 格式化时间戳为字符串
pub fn format_timestamp(timestamp: DateTime<Utc>) -> String {
    timestamp.format("%Y-%m-%d %H:%M:%S UTC").to_string()
}

/// 解析时间戳字符串
pub fn parse_timestamp(timestamp_str: &str) -> Result<DateTime<Utc>, chrono::ParseError> {
    DateTime::parse_from_rfc3339(timestamp_str)
        .map(|dt| dt.with_timezone(&Utc))
        .or_else(|_| {
            chrono::NaiveDateTime::parse_from_str(timestamp_str, "%Y-%m-%d %H:%M:%S")
                .map(|ndt| DateTime::from_utc(ndt, Utc))
        })
}

/// 计算完成百分比
pub fn calculate_completion_percentage(completed: u32, total: u32) -> f64 {
    if total == 0 {
        0.0
    } else {
        (completed as f64 / total as f64) * 100.0
    }
}

/// 验证邮箱格式
pub fn is_valid_email(email: &str) -> bool {
    let email_regex = regex::Regex::new(
        r"^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"
    ).unwrap();
    email_regex.is_match(email)
}

/// 验证URL格式
pub fn is_valid_url(url: &str) -> bool {
    url::Url::parse(url).is_ok()
}

/// 清理和标准化文本
pub fn sanitize_text(text: &str) -> String {
    text.trim()
        .chars()
        .filter(|c| !c.is_control() || c.is_whitespace())
        .collect::<String>()
        .split_whitespace()
        .collect::<Vec<_>>()
        .join(" ")
}

/// 截断文本到指定长度
pub fn truncate_text(text: &str, max_length: usize) -> String {
    if text.len() <= max_length {
        text.to_string()
    } else {
        let mut truncated = text.chars().take(max_length - 3).collect::<String>();
        truncated.push_str("...");
        truncated
    }
}

/// 提取文本中的标签
pub fn extract_tags(text: &str) -> Vec<String> {
    let tag_regex = regex::Regex::new(r"#(\w+)").unwrap();
    tag_regex
        .captures_iter(text)
        .map(|cap| cap[1].to_string())
        .collect()
}

/// 提取文本中的WikiLink
pub fn extract_wikilinks(text: &str) -> Vec<String> {
    let wikilink_regex = regex::Regex::new(r"\[\[([^\]]+)\]\]").unwrap();
    wikilink_regex
        .captures_iter(text)
        .map(|cap| cap[1].to_string())
        .collect()
}

/// 计算文件哈希值
pub fn calculate_file_hash(content: &[u8]) -> String {
    use sha2::{Digest, Sha256};
    let mut hasher = Sha256::new();
    hasher.update(content);
    format!("{:x}", hasher.finalize())
}

/// 格式化文件大小
pub fn format_file_size(size_bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = size_bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", size_bytes, UNITS[unit_index])
    } else {
        format!("{:.1} {}", size, UNITS[unit_index])
    }
}

/// 解析配置字符串
pub fn parse_config_value(value: &str) -> crate::shared::types::ConfigValue {
    use crate::shared::types::ConfigValue;

    // 尝试解析为布尔值
    if let Ok(bool_val) = value.parse::<bool>() {
        return ConfigValue::Boolean(bool_val);
    }

    // 尝试解析为数字
    if let Ok(num_val) = value.parse::<f64>() {
        return ConfigValue::Number(num_val);
    }

    // 尝试解析为JSON
    if value.starts_with('{') || value.starts_with('[') {
        if let Ok(json_val) = serde_json::from_str::<serde_json::Value>(value) {
            return match json_val {
                serde_json::Value::Object(map) => {
                    let mut config_map = HashMap::new();
                    for (k, v) in map {
                        config_map.insert(k, json_value_to_config_value(v));
                    }
                    ConfigValue::Object(config_map)
                }
                serde_json::Value::Array(arr) => {
                    let config_arr: Vec<ConfigValue> = arr
                        .into_iter()
                        .map(json_value_to_config_value)
                        .collect();
                    ConfigValue::Array(config_arr)
                }
                _ => ConfigValue::String(value.to_string()),
            };
        }
    }

    // 默认作为字符串
    ConfigValue::String(value.to_string())
}

fn json_value_to_config_value(value: serde_json::Value) -> crate::shared::types::ConfigValue {
    use crate::shared::types::ConfigValue;

    match value {
        serde_json::Value::String(s) => ConfigValue::String(s),
        serde_json::Value::Number(n) => ConfigValue::Number(n.as_f64().unwrap_or(0.0)),
        serde_json::Value::Bool(b) => ConfigValue::Boolean(b),
        serde_json::Value::Object(map) => {
            let mut config_map = HashMap::new();
            for (k, v) in map {
                config_map.insert(k, json_value_to_config_value(v));
            }
            ConfigValue::Object(config_map)
        }
        serde_json::Value::Array(arr) => {
            let config_arr: Vec<ConfigValue> = arr
                .into_iter()
                .map(json_value_to_config_value)
                .collect();
            ConfigValue::Array(config_arr)
        }
        serde_json::Value::Null => ConfigValue::String("".to_string()),
    }
}

/// 验证密码强度
pub fn validate_password_strength(password: &str) -> PasswordStrength {
    let mut score = 0;
    let mut feedback = Vec::new();

    // 长度检查
    if password.len() >= 8 {
        score += 1;
    } else {
        feedback.push("密码长度至少8位".to_string());
    }

    // 包含小写字母
    if password.chars().any(|c| c.is_lowercase()) {
        score += 1;
    } else {
        feedback.push("需要包含小写字母".to_string());
    }

    // 包含大写字母
    if password.chars().any(|c| c.is_uppercase()) {
        score += 1;
    } else {
        feedback.push("需要包含大写字母".to_string());
    }

    // 包含数字
    if password.chars().any(|c| c.is_numeric()) {
        score += 1;
    } else {
        feedback.push("需要包含数字".to_string());
    }

    // 包含特殊字符
    if password.chars().any(|c| "!@#$%^&*()_+-=[]{}|;:,.<>?".contains(c)) {
        score += 1;
    } else {
        feedback.push("需要包含特殊字符".to_string());
    }

    let strength = match score {
        0..=1 => PasswordStrengthLevel::Weak,
        2..=3 => PasswordStrengthLevel::Medium,
        4 => PasswordStrengthLevel::Strong,
        _ => PasswordStrengthLevel::VeryStrong,
    };

    PasswordStrength {
        level: strength,
        score,
        feedback,
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PasswordStrength {
    pub level: PasswordStrengthLevel,
    pub score: u8,
    pub feedback: Vec<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum PasswordStrengthLevel {
    Weak,
    Medium,
    Strong,
    VeryStrong,
}

/// 生成随机字符串
pub fn generate_random_string(length: usize) -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZ\
                            abcdefghijklmnopqrstuvwxyz\
                            0123456789";
    let mut rng = rand::thread_rng();

    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

/// 计算两个日期之间的天数差
pub fn days_between(start: chrono::NaiveDate, end: chrono::NaiveDate) -> i64 {
    (end - start).num_days()
}

/// 获取本周的开始和结束日期
pub fn get_week_range(date: chrono::NaiveDate) -> (chrono::NaiveDate, chrono::NaiveDate) {
    let weekday = date.weekday().num_days_from_monday();
    let start_of_week = date - chrono::Duration::days(weekday as i64);
    let end_of_week = start_of_week + chrono::Duration::days(6);
    (start_of_week, end_of_week)
}

/// 获取本月的开始和结束日期
pub fn get_month_range(date: chrono::NaiveDate) -> (chrono::NaiveDate, chrono::NaiveDate) {
    let start_of_month = chrono::NaiveDate::from_ymd_opt(date.year(), date.month(), 1).unwrap();
    let end_of_month = if date.month() == 12 {
        chrono::NaiveDate::from_ymd_opt(date.year() + 1, 1, 1).unwrap()
            - chrono::Duration::days(1)
    } else {
        chrono::NaiveDate::from_ymd_opt(date.year(), date.month() + 1, 1).unwrap()
            - chrono::Duration::days(1)
    };
    (start_of_month, end_of_month)
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_generate_id() {
        let id1 = generate_id();
        let id2 = generate_id();
        assert_ne!(id1, id2);
        assert!(Uuid::parse_str(&id1).is_ok());
    }

    #[test]
    fn test_calculate_completion_percentage() {
        assert_eq!(calculate_completion_percentage(5, 10), 50.0);
        assert_eq!(calculate_completion_percentage(0, 10), 0.0);
        assert_eq!(calculate_completion_percentage(10, 10), 100.0);
        assert_eq!(calculate_completion_percentage(5, 0), 0.0);
    }

    #[test]
    fn test_is_valid_email() {
        assert!(is_valid_email("<EMAIL>"));
        assert!(is_valid_email("<EMAIL>"));
        assert!(!is_valid_email("invalid-email"));
        assert!(!is_valid_email("@domain.com"));
    }

    #[test]
    fn test_sanitize_text() {
        assert_eq!(sanitize_text("  hello   world  "), "hello world");
        assert_eq!(sanitize_text("text\nwith\twhitespace"), "text with whitespace");
    }

    #[test]
    fn test_truncate_text() {
        assert_eq!(truncate_text("hello world", 10), "hello w...");
        assert_eq!(truncate_text("short", 10), "short");
    }

    #[test]
    fn test_extract_tags() {
        let text = "This is a #test with #multiple #tags";
        let tags = extract_tags(text);
        assert_eq!(tags, vec!["test", "multiple", "tags"]);
    }

    #[test]
    fn test_format_file_size() {
        assert_eq!(format_file_size(1024), "1.0 KB");
        assert_eq!(format_file_size(1048576), "1.0 MB");
        assert_eq!(format_file_size(500), "500 B");
    }
}
