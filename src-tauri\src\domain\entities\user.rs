// 用户实体
// 用户账户和认证相关的核心业务逻辑

use crate::shared::types::{EntityId, Timestamp};
use crate::shared::errors::{AppError, AppResult};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// 用户实体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: EntityId,
    pub username: String,
    pub email: Option<String>,
    pub password_hash: String,
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    pub timezone: String,
    pub language: String,
    pub is_active: bool,
    pub created_at: Timestamp,
    pub updated_at: Timestamp,
    pub last_login_at: Option<Timestamp>,
}

/// 用户创建数据
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct CreateUserData {
    pub username: String,
    pub email: Option<String>,
    pub password: String,
    pub full_name: Option<String>,
    pub timezone: Option<String>,
    pub language: Option<String>,
}

/// 用户更新数据
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct UpdateUserData {
    pub email: Option<String>,
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    pub timezone: Option<String>,
    pub language: Option<String>,
}

/// 用户查询条件
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserQuery {
    pub username: Option<String>,
    pub email: Option<String>,
    pub is_active: Option<bool>,
    pub created_after: Option<Timestamp>,
    pub created_before: Option<Timestamp>,
}

impl User {
    /// 创建新用户
    pub fn new(data: CreateUserData) -> AppResult<Self> {
        // 验证用户名
        Self::validate_username(&data.username)?;
        
        // 验证邮箱
        if let Some(ref email) = data.email {
            Self::validate_email(email)?;
        }

        // 验证密码
        Self::validate_password(&data.password)?;

        // 生成密码哈希
        let password_hash = Self::hash_password(&data.password)?;

        let now = Utc::now();
        
        Ok(Self {
            id: crate::shared::utils::generate_id(),
            username: data.username,
            email: data.email,
            password_hash,
            full_name: data.full_name,
            avatar_url: None,
            timezone: data.timezone.unwrap_or_else(|| "UTC".to_string()),
            language: data.language.unwrap_or_else(|| "zh-CN".to_string()),
            is_active: true,
            created_at: now,
            updated_at: now,
            last_login_at: None,
        })
    }

    /// 更新用户信息
    pub fn update(&mut self, data: UpdateUserData) -> AppResult<()> {
        // 验证邮箱
        if let Some(ref email) = data.email {
            Self::validate_email(email)?;
        }

        // 更新字段
        if let Some(email) = data.email {
            self.email = Some(email);
        }
        if let Some(full_name) = data.full_name {
            self.full_name = Some(full_name);
        }
        if let Some(avatar_url) = data.avatar_url {
            self.avatar_url = Some(avatar_url);
        }
        if let Some(timezone) = data.timezone {
            self.timezone = timezone;
        }
        if let Some(language) = data.language {
            self.language = language;
        }

        self.updated_at = Utc::now();
        Ok(())
    }

    /// 验证密码
    pub fn verify_password(&self, password: &str) -> AppResult<bool> {
        // 这里应该使用实际的密码验证库，如bcrypt
        // 暂时使用简单的比较
        Ok(Self::hash_password(password)? == self.password_hash)
    }

    /// 更新密码
    pub fn update_password(&mut self, new_password: &str) -> AppResult<()> {
        Self::validate_password(new_password)?;
        self.password_hash = Self::hash_password(new_password)?;
        self.updated_at = Utc::now();
        Ok(())
    }

    /// 记录登录
    pub fn record_login(&mut self) {
        self.last_login_at = Some(Utc::now());
        self.updated_at = Utc::now();
    }

    /// 激活用户
    pub fn activate(&mut self) {
        self.is_active = true;
        self.updated_at = Utc::now();
    }

    /// 停用用户
    pub fn deactivate(&mut self) {
        self.is_active = false;
        self.updated_at = Utc::now();
    }

    /// 验证用户名
    fn validate_username(username: &str) -> AppResult<()> {
        if username.is_empty() {
            return Err(AppError::validation("用户名不能为空"));
        }
        
        if username.len() < 3 {
            return Err(AppError::validation("用户名长度至少3个字符"));
        }
        
        if username.len() > 50 {
            return Err(AppError::validation("用户名长度不能超过50个字符"));
        }

        // 检查用户名格式（只允许字母、数字、下划线）
        if !username.chars().all(|c| c.is_alphanumeric() || c == '_') {
            return Err(AppError::validation("用户名只能包含字母、数字和下划线"));
        }

        Ok(())
    }

    /// 验证邮箱
    fn validate_email(email: &str) -> AppResult<()> {
        if email.is_empty() {
            return Err(AppError::validation("邮箱不能为空"));
        }

        if !crate::shared::utils::is_valid_email(email) {
            return Err(AppError::validation("邮箱格式不正确"));
        }

        Ok(())
    }

    /// 验证密码
    fn validate_password(password: &str) -> AppResult<()> {
        if password.is_empty() {
            return Err(AppError::validation("密码不能为空"));
        }

        if password.len() < 8 {
            return Err(AppError::validation("密码长度至少8个字符"));
        }

        if password.len() > 128 {
            return Err(AppError::validation("密码长度不能超过128个字符"));
        }

        // 检查密码强度
        let strength = crate::shared::utils::validate_password_strength(password);
        if matches!(strength.level, crate::shared::utils::PasswordStrengthLevel::Weak) {
            return Err(AppError::validation(format!(
                "密码强度不足: {}",
                strength.feedback.join(", ")
            )));
        }

        Ok(())
    }

    /// 生成密码哈希
    fn hash_password(password: &str) -> AppResult<String> {
        // 这里应该使用实际的密码哈希库，如bcrypt
        // 暂时使用简单的哈希
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(password.as_bytes());
        hasher.update(b"salt"); // 实际应用中应该使用随机盐
        Ok(format!("{:x}", hasher.finalize()))
    }

    /// 获取显示名称
    pub fn display_name(&self) -> &str {
        self.full_name.as_deref().unwrap_or(&self.username)
    }

    /// 检查是否为管理员
    pub fn is_admin(&self) -> bool {
        // 这里可以根据实际需求实现管理员检查逻辑
        self.username == "admin"
    }

    /// 获取用户统计信息
    pub fn get_stats(&self) -> UserStats {
        UserStats {
            days_since_created: (Utc::now() - self.created_at).num_days(),
            days_since_last_login: self.last_login_at
                .map(|last_login| (Utc::now() - last_login).num_days())
                .unwrap_or(-1),
            is_recently_active: self.last_login_at
                .map(|last_login| (Utc::now() - last_login).num_days() <= 7)
                .unwrap_or(false),
        }
    }
}

/// 用户统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserStats {
    pub days_since_created: i64,
    pub days_since_last_login: i64, // -1 表示从未登录
    pub is_recently_active: bool,
}

impl Default for User {
    fn default() -> Self {
        let now = Utc::now();
        Self {
            id: crate::shared::utils::generate_id(),
            username: "".to_string(),
            email: None,
            password_hash: "".to_string(),
            full_name: None,
            avatar_url: None,
            timezone: "UTC".to_string(),
            language: "zh-CN".to_string(),
            is_active: true,
            created_at: now,
            updated_at: now,
            last_login_at: None,
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_user_creation() {
        let data = CreateUserData {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "SecurePass123!".to_string(),
            full_name: Some("Test User".to_string()),
            timezone: None,
            language: None,
        };

        let user = User::new(data).unwrap();
        assert_eq!(user.username, "testuser");
        assert_eq!(user.email, Some("<EMAIL>".to_string()));
        assert_eq!(user.timezone, "UTC");
        assert_eq!(user.language, "zh-CN");
        assert!(user.is_active);
    }

    #[test]
    fn test_username_validation() {
        // 测试空用户名
        assert!(User::validate_username("").is_err());
        
        // 测试过短用户名
        assert!(User::validate_username("ab").is_err());
        
        // 测试有效用户名
        assert!(User::validate_username("valid_user123").is_ok());
        
        // 测试无效字符
        assert!(User::validate_username("user@name").is_err());
    }

    #[test]
    fn test_email_validation() {
        // 测试有效邮箱
        assert!(User::validate_email("<EMAIL>").is_ok());
        
        // 测试无效邮箱
        assert!(User::validate_email("invalid-email").is_err());
        assert!(User::validate_email("").is_err());
    }

    #[test]
    fn test_password_validation() {
        // 测试过短密码
        assert!(User::validate_password("123").is_err());
        
        // 测试弱密码
        assert!(User::validate_password("password").is_err());
        
        // 测试强密码
        assert!(User::validate_password("SecurePass123!").is_ok());
    }

    #[test]
    fn test_user_update() {
        let data = CreateUserData {
            username: "testuser".to_string(),
            email: Some("<EMAIL>".to_string()),
            password: "SecurePass123!".to_string(),
            full_name: None,
            timezone: None,
            language: None,
        };

        let mut user = User::new(data).unwrap();
        
        let update_data = UpdateUserData {
            email: Some("<EMAIL>".to_string()),
            full_name: Some("New Name".to_string()),
            avatar_url: None,
            timezone: Some("Asia/Shanghai".to_string()),
            language: Some("en-US".to_string()),
        };

        user.update(update_data).unwrap();
        
        assert_eq!(user.email, Some("<EMAIL>".to_string()));
        assert_eq!(user.full_name, Some("New Name".to_string()));
        assert_eq!(user.timezone, "Asia/Shanghai");
        assert_eq!(user.language, "en-US");
    }

    #[test]
    fn test_display_name() {
        let mut user = User::default();
        user.username = "testuser".to_string();
        
        // 没有全名时使用用户名
        assert_eq!(user.display_name(), "testuser");
        
        // 有全名时使用全名
        user.full_name = Some("Test User".to_string());
        assert_eq!(user.display_name(), "Test User");
    }
}
