// 响应DTO定义
// 定义所有API响应的数据结构

use super::{DtoConvert, PaginatedResponseDto};
use crate::domain::entities::{user::User, project::Project, task::Task, area::Area};
use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 用户响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct UserResponseDto {
    pub id: String,
    pub username: String,
    pub email: Option<String>,
    pub full_name: Option<String>,
    pub avatar_url: Option<String>,
    pub timezone: String,
    pub language: String,
    pub is_active: bool,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub last_login_at: Option<chrono::DateTime<chrono::Utc>>,
}

impl DtoConvert<UserResponseDto> for User {
    fn to_dto(self) -> UserResponseDto {
        UserResponseDto {
            id: self.id().clone(),
            username: self.username().clone(),
            email: self.email().clone(),
            full_name: self.full_name().clone(),
            avatar_url: self.avatar_url().clone(),
            timezone: self.timezone().clone(),
            language: self.language().clone(),
            is_active: self.is_active(),
            created_at: self.created_at(),
            updated_at: self.updated_at(),
            last_login_at: self.last_login_at(),
        }
    }

    fn from_dto(_dto: UserResponseDto) -> Self {
        // 通常不需要从响应DTO转换回实体
        unimplemented!("从响应DTO转换为实体通常不需要实现")
    }
}

/// 项目响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct ProjectResponseDto {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub status: String,
    pub progress: u8,
    pub priority: String,
    pub start_date: Option<chrono::NaiveDate>,
    pub deadline: Option<chrono::NaiveDate>,
    pub estimated_hours: Option<u32>,
    pub actual_hours: u32,
    pub area_id: Option<String>,
    pub area_name: Option<String>,
    pub created_by: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub task_count: Option<u32>,
    pub completed_task_count: Option<u32>,
}

impl DtoConvert<ProjectResponseDto> for Project {
    fn to_dto(self) -> ProjectResponseDto {
        ProjectResponseDto {
            id: self.id().clone(),
            name: self.name().clone(),
            description: self.description().clone(),
            status: self.status().to_string(),
            progress: self.progress(),
            priority: self.priority().to_string(),
            start_date: self.start_date(),
            deadline: self.deadline(),
            estimated_hours: self.estimated_hours(),
            actual_hours: self.actual_hours(),
            area_id: self.area_id().clone(),
            area_name: None, // 需要额外查询
            created_by: self.created_by().clone(),
            created_at: self.created_at(),
            updated_at: self.updated_at(),
            task_count: None, // 需要额外查询
            completed_task_count: None, // 需要额外查询
        }
    }

    fn from_dto(_dto: ProjectResponseDto) -> Self {
        unimplemented!("从响应DTO转换为实体通常不需要实现")
    }
}

/// 任务响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct TaskResponseDto {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub status: String,
    pub priority: String,
    pub parent_task_id: Option<String>,
    pub project_id: Option<String>,
    pub project_name: Option<String>,
    pub area_id: Option<String>,
    pub area_name: Option<String>,
    pub assigned_to: Option<String>,
    pub assigned_user_name: Option<String>,
    pub due_date: Option<chrono::NaiveDate>,
    pub estimated_minutes: Option<u32>,
    pub actual_minutes: u32,
    pub completion_percentage: u8,
    pub created_by: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
    pub subtask_count: Option<u32>,
    pub completed_subtask_count: Option<u32>,
}

impl DtoConvert<TaskResponseDto> for Task {
    fn to_dto(self) -> TaskResponseDto {
        TaskResponseDto {
            id: self.id().clone(),
            title: self.title().clone(),
            description: self.description().clone(),
            status: self.status().to_string(),
            priority: self.priority().to_string(),
            parent_task_id: self.parent_task_id().clone(),
            project_id: self.project_id().clone(),
            project_name: None, // 需要额外查询
            area_id: self.area_id().clone(),
            area_name: None, // 需要额外查询
            assigned_to: self.assigned_to().clone(),
            assigned_user_name: None, // 需要额外查询
            due_date: self.due_date(),
            estimated_minutes: self.estimated_minutes(),
            actual_minutes: self.actual_minutes(),
            completion_percentage: self.completion_percentage(),
            created_by: self.created_by().clone(),
            created_at: self.created_at(),
            updated_at: self.updated_at(),
            completed_at: self.completed_at(),
            subtask_count: None, // 需要额外查询
            completed_subtask_count: None, // 需要额外查询
        }
    }

    fn from_dto(_dto: TaskResponseDto) -> Self {
        unimplemented!("从响应DTO转换为实体通常不需要实现")
    }
}

/// 领域响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct AreaResponseDto {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub color: Option<String>,
    pub icon: Option<String>,
    pub is_active: bool,
    pub created_by: String,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub updated_at: chrono::DateTime<chrono::Utc>,
    pub project_count: Option<u32>,
    pub task_count: Option<u32>,
}

impl DtoConvert<AreaResponseDto> for Area {
    fn to_dto(self) -> AreaResponseDto {
        AreaResponseDto {
            id: self.id().clone(),
            name: self.name().clone(),
            description: self.description().clone(),
            color: self.color().clone(),
            icon: self.icon().clone(),
            is_active: self.is_active(),
            created_by: self.created_by().clone(),
            created_at: self.created_at(),
            updated_at: self.updated_at(),
            project_count: None, // 需要额外查询
            task_count: None, // 需要额外查询
        }
    }

    fn from_dto(_dto: AreaResponseDto) -> Self {
        unimplemented!("从响应DTO转换为实体通常不需要实现")
    }
}

/// 登录响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct LoginResponseDto {
    pub user: UserResponseDto,
    pub access_token: String,
    pub refresh_token: Option<String>,
    pub expires_at: chrono::DateTime<chrono::Utc>,
    pub token_type: String,
}

/// 令牌响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct TokenResponseDto {
    pub access_token: String,
    pub token_type: String,
    pub expires_at: chrono::DateTime<chrono::Utc>,
}

/// 统计响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct StatsResponseDto {
    pub user_stats: UserStatsDto,
    pub project_stats: ProjectStatsDto,
    pub task_stats: TaskStatsDto,
    pub area_stats: AreaStatsDto,
    pub period: StatsPeriodDto,
}

/// 用户统计DTO
#[derive(Debug, Clone, Serialize)]
pub struct UserStatsDto {
    pub total_projects: u64,
    pub active_projects: u64,
    pub completed_projects: u64,
    pub total_tasks: u64,
    pub completed_tasks: u64,
    pub completion_rate: f64,
    pub productivity_score: f64,
}

/// 项目统计DTO
#[derive(Debug, Clone, Serialize)]
pub struct ProjectStatsDto {
    pub by_status: HashMap<String, u64>,
    pub by_priority: HashMap<String, u64>,
    pub by_area: HashMap<String, u64>,
    pub average_progress: f64,
    pub overdue_count: u64,
    pub completion_rate: f64,
    pub total_estimated_hours: u64,
    pub total_actual_hours: u64,
}

/// 任务统计DTO
#[derive(Debug, Clone, Serialize)]
pub struct TaskStatsDto {
    pub by_status: HashMap<String, u64>,
    pub by_priority: HashMap<String, u64>,
    pub by_project: HashMap<String, u64>,
    pub average_completion: f64,
    pub overdue_count: u64,
    pub completion_rate: f64,
    pub total_estimated_minutes: u64,
    pub total_actual_minutes: u64,
}

/// 领域统计DTO
#[derive(Debug, Clone, Serialize)]
pub struct AreaStatsDto {
    pub total_areas: u64,
    pub active_areas: u64,
    pub areas_with_projects: u64,
    pub areas_with_tasks: u64,
    pub most_active_area: Option<String>,
}

/// 统计周期DTO
#[derive(Debug, Clone, Serialize)]
pub struct StatsPeriodDto {
    pub start_date: chrono::NaiveDate,
    pub end_date: chrono::NaiveDate,
    pub period_type: String,
}

/// 仪表板响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct DashboardResponseDto {
    pub stats: StatsResponseDto,
    pub recent_activities: Vec<ActivityResponseDto>,
    pub upcoming_deadlines: Vec<DeadlineResponseDto>,
    pub productivity_trends: ProductivityTrendsDto,
}

/// 活动响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct ActivityResponseDto {
    pub id: String,
    pub activity_type: String,
    pub title: String,
    pub description: String,
    pub entity_id: String,
    pub entity_type: String,
    pub user_id: String,
    pub user_name: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

/// 截止日期响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct DeadlineResponseDto {
    pub entity_id: String,
    pub entity_type: String,
    pub title: String,
    pub deadline: chrono::NaiveDate,
    pub days_remaining: i64,
    pub priority: String,
    pub status: String,
    pub progress: Option<u8>,
}

/// 生产力趋势DTO
#[derive(Debug, Clone, Serialize)]
pub struct ProductivityTrendsDto {
    pub daily_completions: Vec<DailyCompletionDto>,
    pub weekly_progress: Vec<WeeklyProgressDto>,
    pub efficiency_score: f64,
    pub trend_direction: String,
}

/// 每日完成DTO
#[derive(Debug, Clone, Serialize)]
pub struct DailyCompletionDto {
    pub date: chrono::NaiveDate,
    pub tasks_completed: u64,
    pub projects_completed: u64,
    pub hours_worked: f64,
    pub efficiency_score: f64,
}

/// 每周进度DTO
#[derive(Debug, Clone, Serialize)]
pub struct WeeklyProgressDto {
    pub week_start: chrono::NaiveDate,
    pub tasks_completed: u64,
    pub projects_completed: u64,
    pub total_hours: f64,
    pub efficiency_score: f64,
}

/// 搜索结果响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct SearchResponseDto {
    pub results: Vec<SearchResultItemDto>,
    pub total: u64,
    pub search_time_ms: u64,
    pub suggestions: Vec<String>,
}

/// 搜索结果项DTO
#[derive(Debug, Clone, Serialize)]
pub struct SearchResultItemDto {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub entity_type: String,
    pub relevance_score: f64,
    pub highlight: Option<String>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 文件上传响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct FileUploadResponseDto {
    pub file_id: String,
    pub file_name: String,
    pub file_size: u64,
    pub content_type: String,
    pub url: String,
    pub upload_url: Option<String>,
    pub expires_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 批量操作响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct BatchOperationResponseDto<T> {
    pub success_count: usize,
    pub error_count: usize,
    pub total_count: usize,
    pub results: Vec<BatchOperationResultDto<T>>,
    pub execution_time_ms: u64,
}

/// 批量操作结果项DTO
#[derive(Debug, Clone, Serialize)]
pub struct BatchOperationResultDto<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub item_id: Option<String>,
}

/// 导出响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct ExportResponseDto {
    pub export_id: String,
    pub format: String,
    pub status: String,
    pub download_url: Option<String>,
    pub file_size: Option<u64>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub expires_at: chrono::DateTime<chrono::Utc>,
}

/// 导入响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct ImportResponseDto {
    pub import_id: String,
    pub status: String,
    pub total_records: u64,
    pub processed_records: u64,
    pub success_records: u64,
    pub error_records: u64,
    pub errors: Vec<ImportErrorDto>,
    pub started_at: chrono::DateTime<chrono::Utc>,
    pub completed_at: Option<chrono::DateTime<chrono::Utc>>,
}

/// 导入错误DTO
#[derive(Debug, Clone, Serialize)]
pub struct ImportErrorDto {
    pub row_number: u64,
    pub field: Option<String>,
    pub message: String,
    pub data: Option<serde_json::Value>,
}

/// 健康检查响应DTO
#[derive(Debug, Clone, Serialize)]
pub struct HealthCheckResponseDto {
    pub status: String,
    pub version: String,
    pub uptime_seconds: u64,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub components: HashMap<String, ComponentHealthDto>,
}

/// 组件健康状态DTO
#[derive(Debug, Clone, Serialize)]
pub struct ComponentHealthDto {
    pub status: String,
    pub message: Option<String>,
    pub response_time_ms: Option<u64>,
    pub last_check: chrono::DateTime<chrono::Utc>,
}

/// API版本信息DTO
#[derive(Debug, Clone, Serialize)]
pub struct ApiVersionResponseDto {
    pub version: String,
    pub build_date: String,
    pub git_commit: Option<String>,
    pub features: Vec<String>,
    pub endpoints: Vec<EndpointInfoDto>,
}

/// 端点信息DTO
#[derive(Debug, Clone, Serialize)]
pub struct EndpointInfoDto {
    pub path: String,
    pub method: String,
    pub description: String,
    pub requires_auth: bool,
    pub deprecated: bool,
}

/// 响应DTO工具类
pub struct ResponseDtoUtils;

impl ResponseDtoUtils {
    /// 创建分页响应
    pub fn create_paginated_response<T>(
        items: Vec<T>,
        total: u64,
        page: u32,
        page_size: u32,
    ) -> PaginatedResponseDto<T> {
        PaginatedResponseDto::new(items, total, page, page_size)
    }

    /// 创建批量操作响应
    pub fn create_batch_response<T>(
        results: Vec<(bool, Option<T>, Option<String>)>,
        execution_time_ms: u64,
    ) -> BatchOperationResponseDto<T> {
        let total_count = results.len();
        let success_count = results.iter().filter(|(success, _, _)| *success).count();
        let error_count = total_count - success_count;

        let results = results
            .into_iter()
            .map(|(success, data, error)| BatchOperationResultDto {
                success,
                data,
                error,
                item_id: None,
            })
            .collect();

        BatchOperationResponseDto {
            success_count,
            error_count,
            total_count,
            results,
            execution_time_ms,
        }
    }

    /// 添加关联数据到项目响应
    pub fn enrich_project_response(
        mut project: ProjectResponseDto,
        area_name: Option<String>,
        task_count: Option<u32>,
        completed_task_count: Option<u32>,
    ) -> ProjectResponseDto {
        project.area_name = area_name;
        project.task_count = task_count;
        project.completed_task_count = completed_task_count;
        project
    }

    /// 添加关联数据到任务响应
    pub fn enrich_task_response(
        mut task: TaskResponseDto,
        project_name: Option<String>,
        area_name: Option<String>,
        assigned_user_name: Option<String>,
        subtask_count: Option<u32>,
        completed_subtask_count: Option<u32>,
    ) -> TaskResponseDto {
        task.project_name = project_name;
        task.area_name = area_name;
        task.assigned_user_name = assigned_user_name;
        task.subtask_count = subtask_count;
        task.completed_subtask_count = completed_subtask_count;
        task
    }

    /// 添加关联数据到领域响应
    pub fn enrich_area_response(
        mut area: AreaResponseDto,
        project_count: Option<u32>,
        task_count: Option<u32>,
    ) -> AreaResponseDto {
        area.project_count = project_count;
        area.task_count = task_count;
        area
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_paginated_response_creation() {
        let items = vec!["item1", "item2", "item3"];
        let response = ResponseDtoUtils::create_paginated_response(items, 100, 1, 20);
        
        assert_eq!(response.total, 100);
        assert_eq!(response.page, 1);
        assert_eq!(response.page_size, 20);
        assert_eq!(response.total_pages, 5);
        assert!(response.has_next);
        assert!(!response.has_prev);
    }

    #[test]
    fn test_batch_response_creation() {
        let results = vec![
            (true, Some("success1".to_string()), None),
            (false, None, Some("error1".to_string())),
            (true, Some("success2".to_string()), None),
        ];
        
        let response = ResponseDtoUtils::create_batch_response(results, 1000);
        
        assert_eq!(response.total_count, 3);
        assert_eq!(response.success_count, 2);
        assert_eq!(response.error_count, 1);
        assert_eq!(response.execution_time_ms, 1000);
    }
}
