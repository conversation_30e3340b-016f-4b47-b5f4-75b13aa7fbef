// 数据传输对象(DTO)模块
// 定义API接口的请求和响应数据结构

pub mod request;
pub mod response;
pub mod validation;

// 重新导出
pub use request::*;
pub use response::*;
pub use validation::*;

use serde::{Deserialize, Serialize};
use std::collections::HashMap;

/// 通用分页请求
#[derive(Debug, <PERSON><PERSON>, Deserialize)]
pub struct PaginationDto {
    #[serde(default = "default_page")]
    pub page: u32,
    #[serde(default = "default_page_size")]
    pub page_size: u32,
}

fn default_page() -> u32 { 1 }
fn default_page_size() -> u32 { 20 }

impl Default for PaginationDto {
    fn default() -> Self {
        Self {
            page: 1,
            page_size: 20,
        }
    }
}

impl PaginationDto {
    pub fn validate(&self) -> Result<(), String> {
        if self.page == 0 {
            return Err("页码必须大于0".to_string());
        }
        if self.page_size == 0 || self.page_size > 100 {
            return Err("页面大小必须在1-100之间".to_string());
        }
        Ok(())
    }
}

/// 通用分页响应
#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize)]
pub struct PaginatedResponseDto<T> {
    pub items: Vec<T>,
    pub total: u64,
    pub page: u32,
    pub page_size: u32,
    pub total_pages: u32,
    pub has_next: bool,
    pub has_prev: bool,
}

impl<T> PaginatedResponseDto<T> {
    pub fn new(items: Vec<T>, total: u64, page: u32, page_size: u32) -> Self {
        let total_pages = ((total as f64) / (page_size as f64)).ceil() as u32;
        let has_next = page < total_pages;
        let has_prev = page > 1;

        Self {
            items,
            total,
            page,
            page_size,
            total_pages,
            has_next,
            has_prev,
        }
    }
}

/// 通用搜索请求
#[derive(Debug, Clone, Deserialize)]
pub struct SearchDto {
    pub keyword: String,
    #[serde(flatten)]
    pub pagination: PaginationDto,
    pub filters: Option<HashMap<String, serde_json::Value>>,
    pub sort_by: Option<String>,
    pub sort_order: Option<SortOrder>,
}

/// 排序顺序
#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum SortOrder {
    Asc,
    Desc,
}

/// 通用ID请求
#[derive(Debug, Clone, Deserialize)]
pub struct IdDto {
    pub id: String,
}

impl IdDto {
    pub fn validate(&self) -> Result<(), String> {
        if self.id.trim().is_empty() {
            return Err("ID不能为空".to_string());
        }
        if self.id.len() > 50 {
            return Err("ID长度不能超过50个字符".to_string());
        }
        Ok(())
    }
}

/// 通用批量操作请求
#[derive(Debug, Clone, Deserialize)]
pub struct BatchDto<T> {
    pub items: Vec<T>,
}

impl<T> BatchDto<T> {
    pub fn validate(&self) -> Result<(), String> {
        if self.items.is_empty() {
            return Err("批量操作项目不能为空".to_string());
        }
        if self.items.len() > 100 {
            return Err("批量操作项目不能超过100个".to_string());
        }
        Ok(())
    }
}

/// 通用批量操作响应
#[derive(Debug, Clone, Serialize)]
pub struct BatchResponseDto<T> {
    pub success_count: usize,
    pub error_count: usize,
    pub results: Vec<BatchResultDto<T>>,
}

/// 批量操作结果项
#[derive(Debug, Clone, Serialize)]
pub struct BatchResultDto<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
}

/// 状态更新请求
#[derive(Debug, Clone, Deserialize)]
pub struct StatusUpdateDto {
    pub status: String,
    pub reason: Option<String>,
}

/// 进度更新请求
#[derive(Debug, Clone, Deserialize)]
pub struct ProgressUpdateDto {
    pub progress: u8,
    pub notes: Option<String>,
}

impl ProgressUpdateDto {
    pub fn validate(&self) -> Result<(), String> {
        if self.progress > 100 {
            return Err("进度不能超过100%".to_string());
        }
        Ok(())
    }
}

/// 时间范围请求
#[derive(Debug, Clone, Deserialize)]
pub struct DateRangeDto {
    pub start_date: Option<chrono::NaiveDate>,
    pub end_date: Option<chrono::NaiveDate>,
}

impl DateRangeDto {
    pub fn validate(&self) -> Result<(), String> {
        if let (Some(start), Some(end)) = (self.start_date, self.end_date) {
            if start > end {
                return Err("开始日期不能晚于结束日期".to_string());
            }
        }
        Ok(())
    }
}

/// 统计请求
#[derive(Debug, Clone, Deserialize)]
pub struct StatsRequestDto {
    #[serde(flatten)]
    pub date_range: DateRangeDto,
    pub group_by: Option<String>,
    pub include_details: Option<bool>,
}

/// 导出请求
#[derive(Debug, Clone, Deserialize)]
pub struct ExportRequestDto {
    pub format: ExportFormat,
    pub filters: Option<HashMap<String, serde_json::Value>>,
    #[serde(flatten)]
    pub date_range: DateRangeDto,
}

/// 导出格式
#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum ExportFormat {
    Json,
    Csv,
    Excel,
}

/// 导入请求
#[derive(Debug, Clone, Deserialize)]
pub struct ImportRequestDto {
    pub data: serde_json::Value,
    pub format: ImportFormat,
    pub options: Option<ImportOptions>,
}

/// 导入格式
#[derive(Debug, Clone, Deserialize, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum ImportFormat {
    Json,
    Csv,
}

/// 导入选项
#[derive(Debug, Clone, Deserialize)]
pub struct ImportOptions {
    pub skip_duplicates: Option<bool>,
    pub update_existing: Option<bool>,
    pub validate_only: Option<bool>,
}

/// 通用成功响应
#[derive(Debug, Clone, Serialize)]
pub struct SuccessResponseDto {
    pub message: String,
    pub timestamp: chrono::DateTime<chrono::Utc>,
}

impl SuccessResponseDto {
    pub fn new(message: &str) -> Self {
        Self {
            message: message.to_string(),
            timestamp: chrono::Utc::now(),
        }
    }
}

/// 健康检查响应
#[derive(Debug, Clone, Serialize)]
pub struct HealthCheckDto {
    pub status: HealthStatus,
    pub version: String,
    pub uptime: String,
    pub components: HashMap<String, ComponentHealthDto>,
}

/// 健康状态
#[derive(Debug, Clone, Serialize)]
#[serde(rename_all = "lowercase")]
pub enum HealthStatus {
    Healthy,
    Degraded,
    Unhealthy,
}

/// 组件健康状态
#[derive(Debug, Clone, Serialize)]
pub struct ComponentHealthDto {
    pub status: HealthStatus,
    pub message: Option<String>,
    pub last_check: chrono::DateTime<chrono::Utc>,
}

/// API版本信息
#[derive(Debug, Clone, Serialize)]
pub struct ApiVersionDto {
    pub version: String,
    pub build_date: String,
    pub git_commit: Option<String>,
    pub features: Vec<String>,
}

/// 错误详情DTO
#[derive(Debug, Clone, Serialize)]
pub struct ErrorDetailDto {
    pub field: String,
    pub message: String,
    pub code: Option<String>,
}

/// 验证错误响应
#[derive(Debug, Clone, Serialize)]
pub struct ValidationErrorDto {
    pub message: String,
    pub errors: Vec<ErrorDetailDto>,
}

/// DTO转换特征
pub trait DtoConvert<T> {
    fn to_dto(self) -> T;
    fn from_dto(dto: T) -> Self;
}

/// DTO验证特征
pub trait DtoValidate {
    fn validate(&self) -> Result<(), Vec<String>>;
}

/// 通用DTO工具
pub struct DtoUtils;

impl DtoUtils {
    /// 验证字符串长度
    pub fn validate_string_length(
        value: &str,
        field_name: &str,
        min: Option<usize>,
        max: Option<usize>,
    ) -> Result<(), String> {
        if let Some(min_len) = min {
            if value.len() < min_len {
                return Err(format!("{} 至少需要 {} 个字符", field_name, min_len));
            }
        }
        if let Some(max_len) = max {
            if value.len() > max_len {
                return Err(format!("{} 不能超过 {} 个字符", field_name, max_len));
            }
        }
        Ok(())
    }

    /// 验证邮箱格式
    pub fn validate_email(email: &str) -> Result<(), String> {
        if !email.contains('@') || !email.contains('.') {
            return Err("邮箱格式不正确".to_string());
        }
        Ok(())
    }

    /// 验证URL格式
    pub fn validate_url(url: &str) -> Result<(), String> {
        if !url.starts_with("http://") && !url.starts_with("https://") {
            return Err("URL格式不正确".to_string());
        }
        Ok(())
    }

    /// 清理HTML标签
    pub fn sanitize_html(input: &str) -> String {
        // 简单的HTML标签清理
        input
            .replace('<', "&lt;")
            .replace('>', "&gt;")
            .replace('"', "&quot;")
            .replace('\'', "&#x27;")
    }

    /// 截断字符串
    pub fn truncate_string(input: &str, max_length: usize) -> String {
        if input.len() <= max_length {
            input.to_string()
        } else {
            format!("{}...", &input[..max_length.saturating_sub(3)])
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_pagination_dto_validation() {
        let valid_pagination = PaginationDto { page: 1, page_size: 20 };
        assert!(valid_pagination.validate().is_ok());

        let invalid_pagination = PaginationDto { page: 0, page_size: 20 };
        assert!(invalid_pagination.validate().is_err());
    }

    #[test]
    fn test_paginated_response_dto() {
        let items = vec!["item1", "item2", "item3"];
        let response = PaginatedResponseDto::new(items, 100, 1, 20);
        
        assert_eq!(response.total, 100);
        assert_eq!(response.total_pages, 5);
        assert!(response.has_next);
        assert!(!response.has_prev);
    }

    #[test]
    fn test_progress_update_dto_validation() {
        let valid_progress = ProgressUpdateDto { progress: 50, notes: None };
        assert!(valid_progress.validate().is_ok());

        let invalid_progress = ProgressUpdateDto { progress: 150, notes: None };
        assert!(invalid_progress.validate().is_err());
    }

    #[test]
    fn test_date_range_dto_validation() {
        use chrono::NaiveDate;
        
        let valid_range = DateRangeDto {
            start_date: Some(NaiveDate::from_ymd_opt(2024, 1, 1).unwrap()),
            end_date: Some(NaiveDate::from_ymd_opt(2024, 12, 31).unwrap()),
        };
        assert!(valid_range.validate().is_ok());

        let invalid_range = DateRangeDto {
            start_date: Some(NaiveDate::from_ymd_opt(2024, 12, 31).unwrap()),
            end_date: Some(NaiveDate::from_ymd_opt(2024, 1, 1).unwrap()),
        };
        assert!(invalid_range.validate().is_err());
    }

    #[test]
    fn test_dto_utils() {
        assert!(DtoUtils::validate_string_length("test", "field", Some(3), Some(10)).is_ok());
        assert!(DtoUtils::validate_string_length("ab", "field", Some(3), Some(10)).is_err());
        
        assert!(DtoUtils::validate_email("<EMAIL>").is_ok());
        assert!(DtoUtils::validate_email("invalid-email").is_err());
        
        assert_eq!(DtoUtils::sanitize_html("<script>alert('xss')</script>"), 
                   "&lt;script&gt;alert(&#x27;xss&#x27;)&lt;/script&gt;");
        
        assert_eq!(DtoUtils::truncate_string("This is a very long string", 10), "This is...");
    }
}
